#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整发信流程测试
测试两个账号同时发件的完整操作流程：
写信按钮→收件人→主题→内容→发送按钮，以及账号切换流程
"""

import sys
import os
import time
import tempfile
import sqlite3
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import get_logger

logger = get_logger("CompleteSendingFlowTest")

class CompleteSendingFlowTest:
    """完整发信流程测试类"""
    
    def __init__(self):
        """初始化测试"""
        self.test_name = "完整发信流程测试"
        self.browser_drivers = []
        self.test_accounts = [
            {'email': '<EMAIL>', 'name': '账号1'},
            {'email': '<EMAIL>', 'name': '账号2'}
        ]
        self.test_recipients = []
        self.test_emails_per_account = 3  # 每个账号发送3封邮件
        
        logger.info(f"🧪 {self.test_name} 初始化完成")
    
    def load_test_recipients(self):
        """加载测试收件人"""
        try:
            logger.info("📧 加载测试收件人...")
            
            # 连接数据库
            db_path = "data/sina_email_automation.db"
            if not os.path.exists(db_path):
                raise Exception(f"数据库文件不存在: {db_path}")
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询前6个收件人（每个账号3个）
            cursor.execute("""
                SELECT email, name FROM recipient_data 
                WHERE email IS NOT NULL AND email != ''
                LIMIT 6
            """)
            
            rows = cursor.fetchall()
            conn.close()
            
            if not rows:
                raise Exception("没有找到收件人数据")
            
            self.test_recipients = []
            for email, name in rows:
                self.test_recipients.append({
                    'email': email,
                    'name': name or email.split('@')[0]
                })
            
            logger.info(f"✅ 加载了 {len(self.test_recipients)} 个测试收件人")
            for i, recipient in enumerate(self.test_recipients):
                logger.info(f"  收件人{i+1}: {recipient['email']}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载收件人数据失败: {e}")
            return False
    
    def create_browsers_with_cookies(self):
        """创建浏览器并应用Cookie"""
        try:
            logger.info("🌐 创建浏览器并应用Cookie...")
            
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            for i, account in enumerate(self.test_accounts):
                logger.info(f"🌐 创建浏览器 {i+1}/{len(self.test_accounts)} - {account['email']}")
                
                # 配置Chrome选项
                chrome_options = Options()
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                
                # 设置用户数据目录
                temp_dir = tempfile.gettempdir()
                user_data_dir = os.path.join(temp_dir, f"test_flow_chrome_{i}_{int(time.time())}")
                chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
                
                # 设置窗口位置
                window_x = i * 450
                window_y = i * 100
                chrome_options.add_argument(f'--window-position={window_x},{window_y}')
                chrome_options.add_argument('--window-size=1300,900')
                
                # 创建浏览器
                driver = webdriver.Chrome(options=chrome_options)
                driver.set_page_load_timeout(30)
                driver.implicitly_wait(10)
                
                # 设置反检测
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                driver.execute_script(f"document.title = '新浪邮箱测试 - {account['name']}';")
                
                # 导航到新浪邮箱
                driver.get("https://mail.sina.com.cn")
                time.sleep(3)
                
                # 应用Cookie
                success = self._apply_cookies_and_login(driver, account, i+1)
                if success:
                    logger.info(f"🔑 浏览器 {i+1} Cookie登录成功: {account['email']}")
                else:
                    logger.warning(f"⚠️ 浏览器 {i+1} Cookie登录失败: {account['email']}")
                
                self.browser_drivers.append({
                    'driver': driver,
                    'account': account,
                    'browser_num': i+1
                })
                
                logger.info(f"✅ 浏览器 {i+1} 创建成功")
                time.sleep(2)
            
            logger.info(f"✅ 创建了 {len(self.browser_drivers)} 个测试浏览器")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建测试浏览器失败: {e}")
            return False
    
    def _apply_cookies_and_login(self, driver, account, browser_num):
        """应用Cookie并验证登录状态"""
        try:
            username = account['email']
            logger.info(f"🔑 浏览器 {browser_num} 开始应用Cookie: {username}")
            
            # 获取Cookie管理器
            from src.core.cookie_manager import CookieManager
            
            # 创建配置
            config = {
                'performance.max_concurrent_sessions': 100,
                'session.timeout': 3600
            }
            cookie_manager = CookieManager(config)
            
            # 获取账号的Cookie
            cookie_data = cookie_manager.get_cookies(username)
            if not cookie_data or 'cookies' not in cookie_data:
                logger.warning(f"⚠️ 浏览器 {browser_num} 没有找到Cookie: {username}")
                return False
            
            cookies = cookie_data['cookies']
            logger.info(f"🍪 浏览器 {browser_num} 开始应用 {len(cookies)} 个Cookie")
            
            # 应用Cookie
            for cookie in cookies:
                try:
                    cookie_dict = {
                        'name': cookie.get('name'),
                        'value': cookie.get('value'),
                        'domain': cookie.get('domain', '.sina.com.cn'),
                        'path': cookie.get('path', '/'),
                    }
                    
                    if 'secure' in cookie:
                        cookie_dict['secure'] = cookie['secure']
                    if 'httpOnly' in cookie:
                        cookie_dict['httpOnly'] = cookie['httpOnly']
                    
                    driver.add_cookie(cookie_dict)
                    
                except Exception as e:
                    logger.debug(f"跳过无效Cookie: {e}")
                    continue
            
            # 刷新页面以应用Cookie
            driver.refresh()
            time.sleep(3)
            
            # 强制导航到新浪邮箱（防止跳转到其他页面）
            current_url = driver.current_url
            if "mail.sina.com.cn" not in current_url:
                logger.warning(f"⚠️ 浏览器 {browser_num} 页面跳转异常: {current_url}")
                driver.get("https://mail.sina.com.cn")
                time.sleep(3)
            
            # 验证登录状态
            return self._verify_login_status(driver, username, browser_num)
            
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} Cookie应用失败: {e}")
            return False
    
    def _verify_login_status(self, driver, username, browser_num):
        """验证登录状态"""
        try:
            logger.info(f"🔍 浏览器 {browser_num} 验证登录状态: {username}")
            
            # 检查页面标题和URL
            current_url = driver.current_url
            page_title = driver.title
            
            logger.info(f"📍 浏览器 {browser_num} 当前URL: {current_url}")
            logger.info(f"📄 浏览器 {browser_num} 页面标题: {page_title}")
            
            # 检查是否在邮箱主页
            if "mail.sina.com.cn" in current_url and "登录" not in page_title:
                # 尝试查找写信按钮或其他登录标识
                try:
                    # 查找写信按钮
                    write_buttons = driver.find_elements(By.XPATH, "//a[contains(text(), '写信') or contains(@title, '写信')]")
                    if write_buttons:
                        logger.info(f"✅ 浏览器 {browser_num} 登录验证成功: 找到写信按钮")
                        return True
                    
                    # 查找其他登录标识
                    user_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '收件箱') or contains(text(), '发件箱')]")
                    if user_elements:
                        logger.info(f"✅ 浏览器 {browser_num} 登录验证成功: 找到邮箱功能")
                        return True
                    
                except Exception as e:
                    logger.debug(f"查找登录标识时出错: {e}")
            
            logger.warning(f"⚠️ 浏览器 {browser_num} 可能未登录，需要手动登录")
            return False
            
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} 登录状态验证失败: {e}")
            return False
    
    def test_complete_sending_flow(self):
        """测试完整发信流程"""
        try:
            logger.info("🚀 开始完整发信流程测试...")
            
            # 为每个浏览器分配收件人
            for browser_info in self.browser_drivers:
                driver = browser_info['driver']
                account = browser_info['account']
                browser_num = browser_info['browser_num']
                
                logger.info("=" * 60)
                logger.info(f"🌐 浏览器 {browser_num} ({account['email']}) 开始发送邮件")
                logger.info("=" * 60)
                
                # 计算该浏览器要发送的邮件
                start_idx = (browser_num - 1) * self.test_emails_per_account
                end_idx = start_idx + self.test_emails_per_account
                recipients_for_browser = self.test_recipients[start_idx:end_idx]
                
                logger.info(f"📧 浏览器 {browser_num} 将发送 {len(recipients_for_browser)} 封邮件")
                
                # 发送每封邮件
                for email_idx, recipient in enumerate(recipients_for_browser):
                    email_num = email_idx + 1
                    logger.info(f"📮 浏览器 {browser_num} 开始发送第 {email_num} 封邮件到: {recipient['email']}")
                    
                    success = self._send_single_email(driver, account, recipient, email_num, browser_num)
                    
                    if success:
                        logger.info(f"✅ 浏览器 {browser_num} 第 {email_num} 封邮件发送成功")
                    else:
                        logger.error(f"❌ 浏览器 {browser_num} 第 {email_num} 封邮件发送失败")
                    
                    # 邮件间隔
                    time.sleep(3)
                
                logger.info(f"🎉 浏览器 {browser_num} ({account['email']}) 所有邮件发送完成")
            
            logger.info("🎉 所有浏览器发送流程测试完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 完整发信流程测试失败: {e}")
            return False
    
    def _send_single_email(self, driver, account, recipient, email_num, browser_num):
        """发送单封邮件的完整流程"""
        try:
            logger.info(f"📝 浏览器 {browser_num} 开始发送邮件流程...")
            
            wait = WebDriverWait(driver, 10)
            
            # 步骤1: 点击写信按钮
            logger.info(f"🖱️ 浏览器 {browser_num} 步骤1: 查找并点击写信按钮")
            
            # 尝试多种写信按钮选择器
            write_button_selectors = [
                "//a[contains(text(), '写信')]",
                "//a[contains(@title, '写信')]",
                "//a[@href*='compose']",
                "//button[contains(text(), '写信')]",
                "//span[contains(text(), '写信')]",
                "//*[@id='compose']",
                "//*[contains(@class, 'compose')]"
            ]
            
            write_button = None
            for selector in write_button_selectors:
                try:
                    write_button = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    logger.info(f"✅ 浏览器 {browser_num} 找到写信按钮: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if not write_button:
                logger.error(f"❌ 浏览器 {browser_num} 未找到写信按钮")
                return False
            
            # 点击写信按钮
            driver.execute_script("arguments[0].click();", write_button)
            logger.info(f"✅ 浏览器 {browser_num} 写信按钮点击成功")
            time.sleep(2)
            
            # 步骤2: 填写收件人
            logger.info(f"📧 浏览器 {browser_num} 步骤2: 填写收件人 {recipient['email']}")
            
            # 尝试多种收件人输入框选择器
            recipient_selectors = [
                "//input[@name='to']",
                "//input[@id='to']",
                "//input[contains(@placeholder, '收件人')]",
                "//input[contains(@class, 'to')]",
                "//textarea[@name='to']"
            ]
            
            recipient_input = None
            for selector in recipient_selectors:
                try:
                    recipient_input = wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                    logger.info(f"✅ 浏览器 {browser_num} 找到收件人输入框: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if not recipient_input:
                logger.error(f"❌ 浏览器 {browser_num} 未找到收件人输入框")
                return False
            
            # 清空并填写收件人
            recipient_input.clear()
            recipient_input.send_keys(recipient['email'])
            logger.info(f"✅ 浏览器 {browser_num} 收件人填写成功")
            time.sleep(1)
            
            # 步骤3: 填写主题
            subject = f"🧪 测试邮件 {email_num} - 来自{account['email']}"
            logger.info(f"📝 浏览器 {browser_num} 步骤3: 填写主题 '{subject}'")
            
            # 尝试多种主题输入框选择器
            subject_selectors = [
                "//input[@name='subject']",
                "//input[@id='subject']",
                "//input[contains(@placeholder, '主题')]",
                "//input[contains(@class, 'subject')]"
            ]
            
            subject_input = None
            for selector in subject_selectors:
                try:
                    subject_input = wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                    logger.info(f"✅ 浏览器 {browser_num} 找到主题输入框: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if not subject_input:
                logger.error(f"❌ 浏览器 {browser_num} 未找到主题输入框")
                return False
            
            # 清空并填写主题
            subject_input.clear()
            subject_input.send_keys(subject)
            logger.info(f"✅ 浏览器 {browser_num} 主题填写成功")
            time.sleep(1)
            
            # 步骤4: 填写内容
            content = f"""
            <div style="font-family: Arial, sans-serif;">
                <h3>邮件发送流程测试</h3>
                <p>尊敬的 {recipient['name']}，</p>
                <p>这是一封来自多浏览器邮件发送系统的测试邮件。</p>
                <ul>
                    <li>发送账号: {account['email']}</li>
                    <li>邮件编号: {email_num}</li>
                    <li>发送时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</li>
                    <li>浏览器编号: {browser_num}</li>
                </ul>
                <p>如果您收到此邮件，说明我们的发送流程运行正常。</p>
                <p style="color: #666; font-size: 12px;">此邮件由自动化测试系统发送，请勿回复。</p>
            </div>
            """
            
            logger.info(f"📄 浏览器 {browser_num} 步骤4: 填写邮件内容")
            
            # 尝试多种内容输入框选择器
            content_selectors = [
                "//iframe[contains(@id, 'content')]",
                "//iframe[contains(@name, 'content')]",
                "//textarea[@name='content']",
                "//div[contains(@class, 'editor')]",
                "//div[@contenteditable='true']"
            ]
            
            content_element = None
            for selector in content_selectors:
                try:
                    if "iframe" in selector:
                        # 处理iframe内容编辑器
                        iframe = wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                        driver.switch_to.frame(iframe)
                        content_element = driver.find_element(By.TAG_NAME, "body")
                        logger.info(f"✅ 浏览器 {browser_num} 找到iframe内容编辑器")
                        break
                    else:
                        content_element = wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                        logger.info(f"✅ 浏览器 {browser_num} 找到内容输入框: {selector}")
                        break
                except TimeoutException:
                    if "iframe" in selector:
                        try:
                            driver.switch_to.default_content()
                        except:
                            pass
                    continue
            
            if not content_element:
                logger.error(f"❌ 浏览器 {browser_num} 未找到内容输入框")
                return False
            
            # 填写内容
            if content_element.tag_name.lower() == 'body':
                # iframe编辑器
                driver.execute_script("arguments[0].innerHTML = arguments[1];", content_element, content)
            else:
                # 普通输入框
                content_element.clear()
                content_element.send_keys(content)
            
            logger.info(f"✅ 浏览器 {browser_num} 内容填写成功")
            
            # 如果在iframe中，切换回主页面
            try:
                driver.switch_to.default_content()
            except:
                pass
            
            time.sleep(1)
            
            # 步骤5: 点击发送按钮
            logger.info(f"🚀 浏览器 {browser_num} 步骤5: 查找并点击发送按钮")
            
            # 尝试多种发送按钮选择器
            send_button_selectors = [
                "//button[contains(text(), '发送')]",
                "//input[@value='发送']",
                "//a[contains(text(), '发送')]",
                "//button[contains(@class, 'send')]",
                "//input[@type='submit']",
                "//*[@id='send']"
            ]
            
            send_button = None
            for selector in send_button_selectors:
                try:
                    send_button = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    logger.info(f"✅ 浏览器 {browser_num} 找到发送按钮: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if not send_button:
                logger.error(f"❌ 浏览器 {browser_num} 未找到发送按钮")
                return False
            
            # 点击发送按钮
            driver.execute_script("arguments[0].click();", send_button)
            logger.info(f"✅ 浏览器 {browser_num} 发送按钮点击成功")
            
            # 等待发送完成
            time.sleep(3)
            
            # 检查发送结果
            try:
                # 查找成功提示
                success_indicators = [
                    "//div[contains(text(), '发送成功')]",
                    "//div[contains(text(), '邮件已发送')]",
                    "//span[contains(text(), '发送成功')]"
                ]
                
                for indicator in success_indicators:
                    try:
                        success_element = driver.find_element(By.XPATH, indicator)
                        if success_element:
                            logger.info(f"✅ 浏览器 {browser_num} 发送成功确认")
                            return True
                    except NoSuchElementException:
                        continue
                
                # 如果没有找到明确的成功提示，检查是否回到了邮箱主页
                current_url = driver.current_url
                if "compose" not in current_url.lower():
                    logger.info(f"✅ 浏览器 {browser_num} 已离开写信页面，可能发送成功")
                    return True
                
            except Exception as e:
                logger.debug(f"检查发送结果时出错: {e}")
            
            logger.info(f"✅ 浏览器 {browser_num} 邮件发送流程完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} 发送邮件失败: {e}")
            return False
    
    def run_complete_test(self):
        """运行完整测试"""
        try:
            logger.info(f"🧪 开始 {self.test_name}")
            
            # 1. 加载测试数据
            logger.info("=" * 50)
            logger.info("步骤1: 加载测试数据")
            logger.info("=" * 50)
            
            if not self.load_test_recipients():
                return False
            
            # 2. 创建浏览器并应用Cookie
            logger.info("=" * 50)
            logger.info("步骤2: 创建浏览器并应用Cookie")
            logger.info("=" * 50)
            
            if not self.create_browsers_with_cookies():
                return False
            
            # 3. 等待用户确认
            logger.info("=" * 50)
            logger.info("测试信息确认")
            logger.info("=" * 50)
            
            logger.info(f"📧 将测试完整发信流程")
            logger.info(f"🌐 使用 {len(self.browser_drivers)} 个浏览器")
            logger.info(f"📮 每个浏览器发送 {self.test_emails_per_account} 封邮件")
            logger.info(f"👥 总共发送 {len(self.browser_drivers) * self.test_emails_per_account} 封邮件")
            
            logger.info("⏰ 测试将在5秒后开始...")
            time.sleep(5)
            
            # 4. 执行完整发信流程测试
            logger.info("=" * 50)
            logger.info("步骤3: 执行完整发信流程测试")
            logger.info("=" * 50)
            
            success = self.test_complete_sending_flow()
            
            if success:
                logger.info("🎉 完整发信流程测试成功！")
            else:
                logger.error("❌ 完整发信流程测试失败！")
            
            # 5. 保持浏览器打开供观察
            logger.info("⏰ 浏览器将保持打开30秒供观察...")
            time.sleep(30)
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}")
            return False
            
        finally:
            # 清理浏览器
            self.cleanup_browsers()
    
    def cleanup_browsers(self):
        """清理浏览器"""
        try:
            logger.info("🧹 清理测试浏览器...")
            
            for i, browser_info in enumerate(self.browser_drivers):
                try:
                    browser_info['driver'].quit()
                    logger.info(f"✅ 浏览器 {i+1} 已关闭")
                except Exception as e:
                    logger.warning(f"⚠️ 关闭浏览器 {i+1} 失败: {e}")
            
            self.browser_drivers.clear()
            logger.info("✅ 浏览器清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理浏览器失败: {e}")

def main():
    """主函数"""
    try:
        logger.info("🧪 启动完整发信流程测试程序")
        
        # 创建测试实例
        test = CompleteSendingFlowTest()
        
        # 运行完整测试
        success = test.run_complete_test()
        
        if success:
            logger.info("🎉 测试成功完成！")
            return 0
        else:
            logger.error("❌ 测试失败！")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 测试程序异常: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n测试完成，退出码: {exit_code}")
    sys.exit(exit_code)
