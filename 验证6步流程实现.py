#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证6步流程实现
检查第一步策略是否正确实现了6步完整流程，包含发送成功检查和账号频繁限制处理
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_6_steps_implementation():
    """检查6步流程实现"""
    print("🔍 检查6步流程实现")
    print("=" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        # 检查主要方法
        ultra_fast_method = inspect.getsource(UnifiedEmailSender._send_ultra_fast)
        execute_method = inspect.getsource(UnifiedEmailSender._execute_6_steps_logic)
        check_success_method = inspect.getsource(UnifiedEmailSender._check_send_success_fast_copy)
        mark_cooling_method = inspect.getsource(UnifiedEmailSender._mark_account_for_cooling)
        
        # 检查6步流程特性
        six_steps_features = [
            # 流程说明
            ("6步完整流程" in ultra_fast_method, "流程说明: 6步完整流程"),
            ("6. 检查是否发送成功" in ultra_fast_method, "步骤6: 检查是否发送成功"),
            ("7. 每封邮件开始前都需要重新点击写信按钮" in ultra_fast_method, "步骤7: 重新点击写信"),
            
            # 方法名称更新
            ("_execute_6_steps_logic" in ultra_fast_method, "方法调用: _execute_6_steps_logic"),
            ("6步流程完成成功" in ultra_fast_method, "成功日志: 6步流程完成"),
            ("6步流程失败" in ultra_fast_method, "失败日志: 6步流程失败"),
            
            # 第6步检查实现
            ("第6步：检查发送结果" in check_success_method, "第6步: 检查发送结果"),
            ("账号频繁限制检测" in check_success_method, "频繁限制检测"),
            ("发送成功确认" in check_success_method, "发送成功确认"),
            
            # 账号冷却处理
            ("_mark_account_for_cooling" in check_success_method, "冷却标记调用"),
            ("账号冷却标记" in mark_cooling_method, "冷却标记实现"),
            ("60 * 60" in mark_cooling_method, "冷却时间设置: 60分钟"),
        ]
        
        print("📋 6步流程实现特性:")
        passed = 0
        for check, desc in six_steps_features:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            if check:
                passed += 1
        
        print(f"\n📊 实现完整性: {passed}/{len(six_steps_features)}")
        
        return passed == len(six_steps_features)
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_frequency_limit_detection():
    """检查频繁限制检测"""
    print("\n🔍 检查频繁限制检测")
    print("=" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        check_success_method = inspect.getsource(UnifiedEmailSender._check_send_success_fast_copy)
        
        # 检查频繁限制检测特性
        frequency_features = [
            # 检测关键词
            ("发信过于频繁" in check_success_method, "检测关键词: 发信过于频繁"),
            ("请1小时后再试" in check_success_method, "检测关键词: 请1小时后再试"),
            ("发送频率过高" in check_success_method, "检测关键词: 发送频率过高"),
            ("操作过于频繁" in check_success_method, "检测关键词: 操作过于频繁"),
            ("m0.mail.sina.com.cn 显示" in check_success_method, "检测关键词: 域名显示"),
            
            # 处理逻辑
            ("账号发送过于频繁，需要冷却处理" in check_success_method, "处理逻辑: 冷却处理"),
            ("将此账号冷却60分钟，立即切换其他账号" in check_success_method, "处理建议: 切换账号"),
            ("return False" in check_success_method, "返回值: False表示需要切换"),
        ]
        
        print("📋 频繁限制检测特性:")
        passed = 0
        for check, desc in frequency_features:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            if check:
                passed += 1
        
        print(f"\n📊 检测完整性: {passed}/{len(frequency_features)}")
        
        return passed == len(frequency_features)
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_success_detection():
    """检查成功检测"""
    print("\n🔍 检查成功检测")
    print("=" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        check_success_method = inspect.getsource(UnifiedEmailSender._check_send_success_fast_copy)
        
        # 检查成功检测特性
        success_features = [
            # 成功关键词
            ("您的邮件已发送" in check_success_method, "成功关键词: 您的邮件已发送"),
            ("此邮件发送成功，并已保存到" in check_success_method, "成功关键词: 发送成功并保存"),
            ("邮件发送成功" in check_success_method, "成功关键词: 邮件发送成功"),
            ("发送成功" in check_success_method, "成功关键词: 发送成功"),
            
            # 成功处理
            ("第6步成功：发送成功确认" in check_success_method, "成功日志: 第6步成功"),
            ("return True" in check_success_method, "返回值: True表示成功"),
            
            # 错误检测
            ("发送失败" in check_success_method, "错误关键词: 发送失败"),
            ("网络错误" in check_success_method, "错误关键词: 网络错误"),
            ("第6步失败：发送错误" in check_success_method, "错误日志: 第6步失败"),
        ]
        
        print("📋 成功检测特性:")
        passed = 0
        for check, desc in success_features:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            if check:
                passed += 1
        
        print(f"\n📊 检测完整性: {passed}/{len(success_features)}")
        
        return passed == len(success_features)
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_6_steps_flow():
    """分析6步流程"""
    print("\n🔍 分析6步流程")
    print("=" * 60)
    
    print("📋 完整的6步流程:")
    print("  1. 🖱️ 点击写信按钮")
    print("     - 每封邮件都重新点击写信按钮")
    print("     - 确保进入写邮件界面")
    
    print("\n  2. 📧 填写收件人")
    print("     - 超极速收件人填写")
    print("     - 15个选择器确保成功")
    
    print("\n  3. 📝 填写主题")
    print("     - 精确主题填写")
    print("     - 26个选择器确保成功")
    
    print("\n  4. 📄 填写内容")
    print("     - 超级优化内容填写")
    print("     - iframe、富文本、textarea三种方式")
    
    print("\n  5. 🚀 点击发送")
    print("     - 超极速发送操作")
    print("     - 17个发送按钮选择器")
    
    print("\n  6. 🔍 检查发送结果 ⭐ 新增")
    print("     - 优先检查账号频繁限制")
    print("     - 检查发送成功消息")
    print("     - 检查发送错误消息")
    print("     - 处理账号冷却")
    
    print("\n  7. 🔄 连续发送准备")
    print("     - 更新成功计数")
    print("     - 为下一封邮件做准备")

def predict_6_steps_results():
    """预测6步流程结果"""
    print("\n🔍 预测6步流程结果")
    print("=" * 60)
    
    print("📋 成功情况预期日志:")
    print("  ⚡ 每封邮件都需要重新点击写信按钮...")
    print("  🔧 尝试元素操作发送（写信按钮已点击）...")
    print("  ✅ 收件人超极速填写: [邮箱地址]")
    print("  ✅ 正确选中主题字段: name=subj, class=input inp_base")
    print("  ✅ 主题超极速填写成功: [主题内容]")
    print("  ✅ iframe内容已填写 (选择器: //iframe[@class='iframe'], 目标: body)")
    print("  ✅ 发送按钮超极速点击完成 (耗时: X.XX秒)")
    print("  🔍 第6步：检查发送结果...")
    print("  ✅ 第6步成功：发送成功确认 - 您的邮件已发送")
    print("  ✅ 6步流程完成成功")
    print("  📊 成功计数已更新")
    
    print("\n📋 账号频繁限制情况预期日志:")
    print("  ⚡ 每封邮件都需要重新点击写信按钮...")
    print("  🔧 尝试元素操作发送（写信按钮已点击）...")
    print("  ✅ 收件人超极速填写: [邮箱地址]")
    print("  ✅ 正确选中主题字段: name=subj, class=input inp_base")
    print("  ✅ 主题超极速填写成功: [主题内容]")
    print("  ✅ iframe内容已填写 (选择器: //iframe[@class='iframe'], 目标: body)")
    print("  ✅ 发送按钮超极速点击完成 (耗时: X.XX秒)")
    print("  🔍 第6步：检查发送结果...")
    print("  🚨 账号频繁限制检测: 发信过于频繁")
    print("  ❌ 账号发送过于频繁，需要冷却处理")
    print("  🔄 建议：将此账号冷却60分钟，立即切换其他账号")
    print("  🧊 账号冷却标记: [账号名]")
    print("  ⏰ 冷却时间: 60分钟")
    print("  ⚠️ 6步流程失败")
    print("  🔄 系统将自动切换其他账号继续发送")

def main():
    """主函数"""
    print("🎯 验证6步流程实现")
    print("目标：确认第一步策略正确实现了6步完整流程")
    print("新增：第6步发送结果检查，包含账号频繁限制处理")
    print("=" * 80)
    
    # 检查6步流程实现
    implementation_ok = check_6_steps_implementation()
    
    # 检查频繁限制检测
    frequency_ok = check_frequency_limit_detection()
    
    # 检查成功检测
    success_ok = check_success_detection()
    
    # 分析6步流程
    analyze_6_steps_flow()
    
    # 预测结果
    predict_6_steps_results()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 6步流程实现验证结果")
    print("=" * 80)
    
    if implementation_ok and frequency_ok and success_ok:
        print("🎉 6步流程实现成功！")
        print("✅ 所有实现特性都已完成")
        print("✅ 频繁限制检测完整")
        print("✅ 成功检测完整")
        print("✅ 账号冷却处理完整")
        
        print("\n🎯 6步流程的关键价值:")
        print("  1. 🔍 第6步发送结果检查 - 确保发送状态明确")
        print("  2. 🚨 账号频繁限制检测 - 自动识别账号限制")
        print("  3. 🧊 账号冷却处理 - 智能账号管理")
        print("  4. 🔄 自动账号切换 - 提高发送连续性")
        print("  5. 📊 详细状态反馈 - 便于系统决策")
        
        print("\n🚀 预期效果:")
        print("第一步策略现在具备完整的6步流程:")
        print("  ✅ 智能发送结果检查")
        print("  ✅ 自动账号频繁限制处理")
        print("  ✅ 智能账号冷却和切换")
        print("  ✅ 提高多账号发送系统的稳定性")
        
    else:
        print("❌ 6步流程实现不完整")
        if not implementation_ok:
            print("❌ 基础实现检查失败")
        if not frequency_ok:
            print("❌ 频繁限制检测失败")
        if not success_ok:
            print("❌ 成功检测失败")
    
    return 0 if (implementation_ok and frequency_ok and success_ok) else 1

if __name__ == "__main__":
    sys.exit(main())
