#!/usr/bin/env python3
"""
测试主界面标签页
验证原有多浏览器发送和新的强大多浏览器发送标签页都能正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from src.utils.logger import setup_logger
from src.gui.main_window import MainWindow

logger = setup_logger("INFO")

def test_main_window_tabs():
    """测试主界面标签页"""
    try:
        logger.info("🧪 开始测试主界面标签页...")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 显示窗口
        main_window.show()
        
        # 获取标签页数量
        tab_count = main_window.tab_widget.count()
        logger.info(f"📋 标签页总数: {tab_count}")
        
        # 列出所有标签页
        for i in range(tab_count):
            tab_text = main_window.tab_widget.tabText(i)
            logger.info(f"  标签页 {i+1}: {tab_text}")
        
        # 检查是否包含预期的标签页
        expected_tabs = [
            "🌐 多浏览器发送",  # 原有系统
            "🚀 强大多浏览器发送",  # 新系统
            "⚡ 并发发送器"  # 另一个新系统
        ]
        
        found_tabs = []
        for i in range(tab_count):
            tab_text = main_window.tab_widget.tabText(i)
            if any(expected in tab_text for expected in expected_tabs):
                found_tabs.append(tab_text)
        
        logger.info(f"✅ 找到预期标签页: {found_tabs}")
        
        # 测试切换到每个标签页
        for i in range(tab_count):
            try:
                main_window.tab_widget.setCurrentIndex(i)
                tab_text = main_window.tab_widget.tabText(i)
                logger.info(f"✅ 成功切换到标签页: {tab_text}")
            except Exception as e:
                logger.error(f"❌ 切换到标签页 {i} 失败: {e}")
        
        logger.info("✅ 主界面标签页测试完成")
        
        # 不启动事件循环，直接退出
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试主界面标签页异常: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始主界面标签页测试...")
    
    success = test_main_window_tabs()
    
    if success:
        logger.info("🎉 主界面标签页测试成功！")
        logger.info("现在您有以下标签页：")
        logger.info("  1. 🌐 多浏览器发送 - 原有的多浏览器发送系统")
        logger.info("  2. 🚀 强大多浏览器发送 - 新的强大并发发送系统")
        logger.info("  3. ⚡ 并发发送器 - 另一个并发发送系统")
        logger.info("所有系统都保留，用户可以选择使用！")
        return 0
    else:
        logger.error("❌ 主界面标签页测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
