#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量发送适配器
集成现有的发送器到新的智能批量发送系统
"""

import time
import logging
from typing import Dict, Optional, Tuple
from .smart_batch_sender_manager import SmartBatchSenderManager, BatchTask
from .multi_browser_manager import MultiBrowserManager
from src.models.browser_instance import BrowserInstance
from .sina_ultra_fast_sender_final import SinaUltraFastSenderFinal

logger = logging.getLogger(__name__)

class BatchSenderAdapter:
    """批量发送适配器 - 集成现有组件"""
    
    def __init__(self, config):
        self.config = config
        self.smart_manager = SmartBatchSenderManager(config)
        self.browser_manager = MultiBrowserManager(config)
        self.browser_senders: Dict[str, SinaUltraFastSenderFinal] = {}
        self.browser_instances: Dict[str, BrowserInstance] = {}
        
        logger.info("🔧 批量发送适配器初始化完成")
    
    def import_recipients_from_file(self, file_path: str, subject: str, content: str) -> int:
        """导入收件人文件"""
        return self.smart_manager.import_recipients_from_file(file_path, subject, content)
    
    def start_batch_sending(self, max_browsers: int = 3):
        """启动批量发送"""
        # 初始化浏览器管理器
        if not self.browser_manager.initialize_browsers(max_browsers):
            logger.error("❌ 浏览器管理器初始化失败")
            return False
        
        # 为每个浏览器创建发送器
        for i in range(max_browsers):
            browser_id = f"browser_{i + 1}"
            
            # 获取浏览器实例
            browser_instance = self.browser_manager.get_available_browser()
            if browser_instance:
                self.browser_instances[browser_id] = browser_instance
                
                # 创建发送器
                sender = SinaUltraFastSenderFinal(browser_instance.driver)
                self.browser_senders[browser_id] = sender
                
                logger.info(f"✅ 浏览器 {browser_id} 发送器创建完成")
        
        # 启动智能管理器
        self.smart_manager.start_batch_sending(max_browsers)
        return True
    
    def stop_batch_sending(self):
        """停止批量发送"""
        # 停止智能管理器
        self.smart_manager.stop_batch_sending()
        
        # 清理发送器
        self.browser_senders.clear()
        self.browser_instances.clear()
        
        # 清理浏览器管理器
        self.browser_manager.cleanup_browsers()
        
        logger.info("✅ 批量发送已完全停止")
    
    def send_email_task(self, browser_id: str, task: BatchTask) -> Tuple[bool, str]:
        """执行邮件发送任务"""
        try:
            # 获取发送器
            sender = self.browser_senders.get(browser_id)
            if not sender:
                return False, f"浏览器 {browser_id} 发送器不存在"
            
            # 获取浏览器实例
            browser_instance = self.browser_instances.get(browser_id)
            if not browser_instance:
                return False, f"浏览器 {browser_id} 实例不存在"
            
            # 检查账号状态并切换
            account_info = self.smart_manager.account_manager.get_browser_account(browser_id)
            if account_info:
                current_account = account_info['account']
                
                # 如果需要切换账号，加载新的Cookie
                if (browser_instance.current_account != current_account or 
                    not browser_instance.current_account):
                    
                    logger.info(f"🔄 为浏览器 {browser_id} 切换到账号: {current_account.email}")
                    
                    if not self.browser_manager.load_account_cookies(browser_instance, current_account):
                        return False, f"账号 {current_account.email} Cookie加载失败"
                    
                    browser_instance.current_account = current_account
                    browser_instance.sent_count = 0  # 重置发送计数
            
            # 执行发送
            success = sender.send_email_ultra_fast(
                task.to_email, 
                task.subject, 
                task.content
            )
            
            if success:
                # 更新浏览器实例统计
                browser_instance.sent_count += 1
                browser_instance.last_activity = time.time()
                return True, None
            else:
                return False, "发送失败"
                
        except Exception as e:
            logger.error(f"❌ 发送任务异常 {browser_id}: {e}")
            return False, str(e)
    
    def get_sending_stats(self):
        """获取发送统计"""
        return self.smart_manager.get_sending_stats()
    
    def clear_all_tasks(self):
        """清空所有任务"""
        self.smart_manager.clear_all_tasks()

# 修改智能批量发送管理器，集成适配器
class IntegratedSmartBatchSender(SmartBatchSenderManager):
    """集成的智能批量发送管理器"""
    
    def __init__(self, config):
        super().__init__(config)
        self.adapter = None
    
    def set_adapter(self, adapter: BatchSenderAdapter):
        """设置适配器"""
        self.adapter = adapter
    
    def _send_email_task(self, browser_id: str, task: BatchTask) -> Tuple[bool, str]:
        """执行邮件发送任务 - 使用适配器"""
        if self.adapter:
            return self.adapter.send_email_task(browser_id, task)
        else:
            return False, "适配器未设置"
