#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一邮件调度器 - 整合所有调度策略
整合了超高速调度、标准调度、批量调度等多种策略
"""

import time
import queue
import threading
from typing import List, Optional, Dict, Any, Callable
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from loguru import logger

from .unified_email_sender import UnifiedEmailSender, SendingStrategy, SendingResult


class SchedulingMode(Enum):
    """调度模式枚举"""
    SEQUENTIAL = "sequential"      # 顺序调度
    BATCH = "batch"               # 批量调度
    CONCURRENT = "concurrent"     # 并发调度
    SMART = "smart"               # 智能调度


@dataclass
class EmailTask:
    """邮件任务数据类"""
    task_id: str
    to_email: str
    subject: str
    content: str
    content_type: str = "text/plain"
    priority: int = 1
    created_time: float = 0.0
    scheduled_time: float = 0.0
    retry_count: int = 0
    max_retries: int = 3
    status: str = "pending"  # pending, processing, completed, failed


@dataclass
class SchedulingConfig:
    """调度配置"""
    mode: SchedulingMode = SchedulingMode.SEQUENTIAL
    send_interval: float = 2.0
    max_concurrent: int = 3
    batch_size: int = 10
    max_retries: int = 3
    retry_interval: float = 5.0


class SchedulingStats:
    """调度统计"""
    def __init__(self):
        self.total_tasks = 0
        self.pending_tasks = 0
        self.processing_tasks = 0
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.start_time = 0.0
        self.end_time = 0.0
        self.avg_processing_time = 0.0


class UnifiedEmailScheduler:
    """
    统一邮件调度器
    
    整合了所有调度策略，提供统一的调度接口
    支持多种调度模式，自动选择最佳策略
    """
    
    def __init__(self, config: SchedulingConfig, sender_factory: Callable = None):
        """
        初始化统一调度器
        
        Args:
            config: 调度配置
            sender_factory: 发送器工厂函数
        """
        self.config = config
        self.sender_factory = sender_factory
        
        # 任务队列
        self.task_queue = queue.PriorityQueue()
        self.completed_tasks: List[EmailTask] = []
        self.failed_tasks: List[EmailTask] = []
        
        # 统计信息
        self.stats = SchedulingStats()
        
        # 线程控制
        self.is_running = False
        self.is_paused = False
        self.worker_threads: List[threading.Thread] = []
        self.scheduler_thread: Optional[threading.Thread] = None
        
        # 回调函数
        self.on_task_completed: Optional[Callable] = None
        self.on_task_failed: Optional[Callable] = None
        self.on_progress_updated: Optional[Callable] = None
        
        # 发送器缓存
        self.sender_cache: Dict[str, UnifiedEmailSender] = {}
        
        logger.info(f"📅 统一邮件调度器初始化完成，模式: {config.mode.value}")
    
    def add_task(self, to_email: str, subject: str, content: str, 
                 content_type: str = "text/plain", priority: int = 1,
                 scheduled_time: Optional[float] = None) -> str:
        """
        添加邮件任务
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            content: 邮件内容
            content_type: 内容类型
            priority: 优先级
            scheduled_time: 计划发送时间
            
        Returns:
            任务ID
        """
        task_id = f"task_{int(time.time() * 1000)}_{self.stats.total_tasks}"
        
        task = EmailTask(
            task_id=task_id,
            to_email=to_email,
            subject=subject,
            content=content,
            content_type=content_type,
            priority=priority,
            created_time=time.time(),
            scheduled_time=scheduled_time or time.time(),
            max_retries=self.config.max_retries
        )
        
        # 使用负优先级，因为PriorityQueue是最小堆
        self.task_queue.put((-priority, task.scheduled_time, task))
        
        self.stats.total_tasks += 1
        self.stats.pending_tasks += 1
        
        logger.info(f"📝 添加邮件任务: {task_id} -> {to_email}")
        return task_id
    
    def add_batch_tasks(self, email_list: List[tuple], 
                       content_type: str = "text/plain",
                       priority: int = 1) -> List[str]:
        """
        批量添加邮件任务
        
        Args:
            email_list: 邮件列表 [(to_email, subject, content), ...]
            content_type: 内容类型
            priority: 优先级
            
        Returns:
            任务ID列表
        """
        task_ids = []
        current_time = time.time()
        
        for i, (to_email, subject, content) in enumerate(email_list):
            # 计算调度时间，避免同时发送
            scheduled_time = current_time + (i * self.config.send_interval)
            
            task_id = self.add_task(
                to_email, subject, content, content_type, 
                priority, scheduled_time
            )
            task_ids.append(task_id)
        
        logger.info(f"📝 批量添加 {len(email_list)} 个邮件任务")
        return task_ids
    
    def start_scheduling(self, num_workers: int = 1) -> bool:
        """
        开始调度
        
        Args:
            num_workers: 工作线程数量
            
        Returns:
            是否启动成功
        """
        if self.is_running:
            logger.warning("⚠️ 调度器已在运行中")
            return True
        
        try:
            logger.info(f"🚀 启动邮件调度器，模式: {self.config.mode.value}")
            
            self.is_running = True
            self.is_paused = False
            self.stats.start_time = time.time()
            
            # 根据调度模式启动相应的线程
            if self.config.mode == SchedulingMode.SEQUENTIAL:
                self._start_sequential_scheduling()
            elif self.config.mode == SchedulingMode.BATCH:
                self._start_batch_scheduling()
            elif self.config.mode == SchedulingMode.CONCURRENT:
                self._start_concurrent_scheduling(num_workers)
            elif self.config.mode == SchedulingMode.SMART:
                self._start_smart_scheduling()
            
            logger.info("✅ 邮件调度器启动成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 调度器启动失败: {e}")
            self.is_running = False
            return False
    
    def stop_scheduling(self):
        """停止调度"""
        if not self.is_running:
            return
        
        logger.info("🛑 停止邮件调度器...")
        
        self.is_running = False
        self.stats.end_time = time.time()
        
        # 等待所有工作线程结束
        for thread in self.worker_threads:
            if thread.is_alive():
                thread.join(timeout=5)
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        # 清理发送器缓存
        for sender in self.sender_cache.values():
            sender.cleanup()
        self.sender_cache.clear()
        
        logger.info("✅ 邮件调度器已停止")
    
    def pause_scheduling(self):
        """暂停调度"""
        self.is_paused = True
        logger.info("⏸️ 邮件调度器已暂停")
    
    def resume_scheduling(self):
        """恢复调度"""
        self.is_paused = False
        logger.info("▶️ 邮件调度器已恢复")
    
    def _start_sequential_scheduling(self):
        """启动顺序调度"""
        def sequential_worker():
            logger.info("🔄 顺序调度工作线程启动")
            
            while self.is_running:
                try:
                    if self.is_paused:
                        time.sleep(0.1)
                        continue
                    
                    # 获取任务
                    try:
                        priority, scheduled_time, task = self.task_queue.get(timeout=1)
                    except queue.Empty:
                        continue
                    
                    # 检查是否到达调度时间
                    current_time = time.time()
                    if scheduled_time > current_time:
                        # 重新放回队列
                        self.task_queue.put((priority, scheduled_time, task))
                        time.sleep(0.1)
                        continue
                    
                    # 处理任务
                    self._process_task(task)
                    
                    # 发送间隔
                    time.sleep(self.config.send_interval)
                    
                except Exception as e:
                    logger.error(f"❌ 顺序调度异常: {e}")
                    time.sleep(1)
            
            logger.info("🔄 顺序调度工作线程结束")
        
        self.scheduler_thread = threading.Thread(target=sequential_worker, daemon=True)
        self.scheduler_thread.start()
    
    def _start_concurrent_scheduling(self, num_workers: int):
        """启动并发调度"""
        def concurrent_worker(worker_id: int):
            logger.info(f"🔄 并发调度工作线程 {worker_id} 启动")
            
            while self.is_running:
                try:
                    if self.is_paused:
                        time.sleep(0.1)
                        continue
                    
                    # 获取任务
                    try:
                        priority, scheduled_time, task = self.task_queue.get(timeout=1)
                    except queue.Empty:
                        continue
                    
                    # 检查是否到达调度时间
                    current_time = time.time()
                    if scheduled_time > current_time:
                        # 重新放回队列
                        self.task_queue.put((priority, scheduled_time, task))
                        time.sleep(0.1)
                        continue
                    
                    # 处理任务
                    self._process_task(task, worker_id)
                    
                except Exception as e:
                    logger.error(f"❌ 并发调度工作线程 {worker_id} 异常: {e}")
                    time.sleep(1)
            
            logger.info(f"🔄 并发调度工作线程 {worker_id} 结束")
        
        # 启动多个工作线程
        for i in range(num_workers):
            thread = threading.Thread(target=concurrent_worker, args=(i,), daemon=True)
            thread.start()
            self.worker_threads.append(thread)
    
    def _start_batch_scheduling(self):
        """启动批量调度"""
        # 批量调度逻辑
        self._start_sequential_scheduling()  # 暂时使用顺序调度
    
    def _start_smart_scheduling(self):
        """启动智能调度"""
        # 智能调度逻辑 - 根据任务数量自动选择策略
        pending_count = self.task_queue.qsize()
        
        if pending_count <= 10:
            self._start_sequential_scheduling()
        else:
            self._start_concurrent_scheduling(min(3, pending_count // 10))
    
    def _process_task(self, task: EmailTask, worker_id: int = 0):
        """处理单个任务"""
        try:
            logger.info(f"📧 处理任务: {task.task_id} -> {task.to_email}")
            
            task.status = "processing"
            self.stats.pending_tasks -= 1
            self.stats.processing_tasks += 1
            
            # 获取或创建发送器
            sender = self._get_sender(worker_id)
            if not sender:
                raise Exception("无法获取发送器")
            
            # 发送邮件
            result = sender.send_email(task.to_email, task.subject, task.content, task.content_type)
            
            if result.success:
                # 发送成功
                task.status = "completed"
                self.completed_tasks.append(task)
                self.stats.completed_tasks += 1
                
                if self.on_task_completed:
                    self.on_task_completed(task, result)
                
                logger.info(f"✅ 任务完成: {task.task_id}")
            else:
                # 发送失败，检查是否需要重试
                if task.retry_count < task.max_retries:
                    task.retry_count += 1
                    task.status = "pending"
                    
                    # 重新调度
                    retry_time = time.time() + self.config.retry_interval
                    self.task_queue.put((-task.priority, retry_time, task))
                    
                    logger.warning(f"⚠️ 任务重试: {task.task_id} (第{task.retry_count}次)")
                else:
                    # 最终失败
                    task.status = "failed"
                    self.failed_tasks.append(task)
                    self.stats.failed_tasks += 1
                    
                    if self.on_task_failed:
                        self.on_task_failed(task, result)
                    
                    logger.error(f"❌ 任务最终失败: {task.task_id}")
            
            self.stats.processing_tasks -= 1
            
            # 更新进度
            if self.on_progress_updated:
                self.on_progress_updated(self.get_progress())
            
        except Exception as e:
            logger.error(f"❌ 任务处理异常: {task.task_id} - {e}")
            
            task.status = "failed"
            self.failed_tasks.append(task)
            self.stats.processing_tasks -= 1
            self.stats.failed_tasks += 1
    
    def _get_sender(self, worker_id: int = 0) -> Optional[UnifiedEmailSender]:
        """获取发送器"""
        sender_key = f"sender_{worker_id}"
        
        if sender_key not in self.sender_cache:
            if self.sender_factory:
                try:
                    sender = self.sender_factory()
                    self.sender_cache[sender_key] = sender
                    logger.info(f"✅ 创建发送器: {sender_key}")
                except Exception as e:
                    logger.error(f"❌ 创建发送器失败: {e}")
                    return None
            else:
                logger.error("❌ 未设置发送器工厂函数")
                return None
        
        return self.sender_cache.get(sender_key)
    
    def get_progress(self) -> Dict[str, Any]:
        """获取进度信息"""
        total = self.stats.total_tasks
        completed = self.stats.completed_tasks
        failed = self.stats.failed_tasks
        pending = self.stats.pending_tasks
        processing = self.stats.processing_tasks
        
        progress_percent = 0.0
        if total > 0:
            progress_percent = ((completed + failed) / total) * 100
        
        return {
            'total_tasks': total,
            'completed_tasks': completed,
            'failed_tasks': failed,
            'pending_tasks': pending,
            'processing_tasks': processing,
            'progress_percent': round(progress_percent, 2),
            'success_rate': round((completed / max(1, completed + failed)) * 100, 2)
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取详细统计信息"""
        progress = self.get_progress()
        
        elapsed_time = 0.0
        if self.stats.start_time > 0:
            end_time = self.stats.end_time if self.stats.end_time > 0 else time.time()
            elapsed_time = end_time - self.stats.start_time
        
        return {
            **progress,
            'elapsed_time': round(elapsed_time, 2),
            'avg_processing_time': round(self.stats.avg_processing_time, 2),
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'mode': self.config.mode.value
        }
