#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的MockWebDriver
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.sender_factory import MockWebDriver, MockWebElement

def test_mock_webdriver():
    """测试MockWebDriver的所有属性和方法"""
    print("🧪 开始测试MockWebDriver...")
    
    # 创建MockWebDriver实例
    driver = MockWebDriver()
    
    # 测试基本属性
    print(f"✅ current_url: {driver.current_url}")
    print(f"✅ title: {driver.title}")
    print(f"✅ page_source长度: {len(driver.page_source)}")
    print(f"✅ is_active: {driver.is_active}")
    
    # 测试方法
    driver.get("https://test.com")
    print(f"✅ 访问URL后current_url: {driver.current_url}")
    
    # 测试查找元素
    element = driver.find_element("id", "test")
    print(f"✅ find_element返回: {type(element)}")
    
    elements = driver.find_elements("class", "test")
    print(f"✅ find_elements返回: {len(elements)} 个元素")
    
    # 测试switch_to
    switch_to = driver.switch_to()
    print(f"✅ switch_to返回: {type(switch_to)}")
    
    # 测试执行脚本
    result = driver.execute_script("return true;")
    print(f"✅ execute_script返回: {result}")
    
    print("🎉 MockWebDriver测试完成，所有功能正常！")

def test_mock_webelement():
    """测试MockWebElement的所有属性和方法"""
    print("\n🧪 开始测试MockWebElement...")
    
    # 创建MockWebElement实例
    element = MockWebElement("input", "测试元素")
    
    # 测试基本属性
    print(f"✅ tag_name: {element.tag_name}")
    print(f"✅ text: {element.text}")
    print(f"✅ is_displayed: {element.is_displayed()}")
    print(f"✅ is_enabled: {element.is_enabled()}")
    print(f"✅ size: {element.size}")
    print(f"✅ location: {element.location}")
    
    # 测试方法
    element.send_keys("测试输入")
    element.click()
    element.clear()
    
    # 测试获取属性
    attr = element.get_attribute("value")
    print(f"✅ get_attribute返回: '{attr}'")
    
    # 测试查找子元素
    child = element.find_element("id", "child")
    print(f"✅ find_element返回: {type(child)}")
    
    children = element.find_elements("class", "children")
    print(f"✅ find_elements返回: {len(children)} 个子元素")
    
    print("🎉 MockWebElement测试完成，所有功能正常！")

def test_sina_sender_compatibility():
    """测试与新浪发送器的兼容性"""
    print("\n🧪 开始测试与新浪发送器的兼容性...")
    
    try:
        from src.core.sina_ultra_fast_sender_final import SinaUltraFastSenderFinal
        
        # 创建MockWebDriver
        driver = MockWebDriver()
        
        # 创建发送器实例
        sender = SinaUltraFastSenderFinal(driver)
        
        # 测试检查登录状态（这是之前出错的地方）
        print("✅ 发送器创建成功")
        print(f"✅ driver.current_url: {driver.current_url}")
        print(f"✅ driver.title: {driver.title}")
        print(f"✅ driver.page_source存在: {hasattr(driver, 'page_source')}")
        
        print("🎉 与新浪发送器兼容性测试通过！")
        
    except Exception as e:
        print(f"❌ 兼容性测试失败: {e}")

if __name__ == "__main__":
    print("🚀 开始MockWebDriver修复验证测试")
    print("=" * 50)
    
    test_mock_webdriver()
    test_mock_webelement()
    test_sina_sender_compatibility()
    
    print("\n" + "=" * 50)
    print("✅ 所有测试完成！MockWebDriver修复成功！")
