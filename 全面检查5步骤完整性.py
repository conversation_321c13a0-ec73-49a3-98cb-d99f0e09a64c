#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面检查5步骤完整性
确保第一步策略中的所有5步都完全应用了测试成功的逻辑
对比multi_browser_sender_flow_test.py中测试的五步骤
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_step1_click_compose():
    """检查第1步：点击写信按钮"""
    print("🔍 检查第1步：点击写信按钮")
    print("-" * 50)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        step1_source = inspect.getsource(UnifiedEmailSender._step1_click_compose)
        
        checks = [
            ("prepare_compose_page()" in step1_source, "调用prepare_compose_page方法"),
            ("ultra_fast_sender" in step1_source, "使用ultra_fast_sender"),
            ("logger.info" in step1_source, "包含日志记录"),
            ("return" in step1_source, "有返回值"),
            ("Exception" in step1_source, "有异常处理")
        ]
        
        passed = 0
        for check, desc in checks:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            if check:
                passed += 1
        
        print(f"  📊 第1步检查: {passed}/{len(checks)} 项通过")
        return passed == len(checks)
        
    except Exception as e:
        print(f"  ❌ 第1步检查失败: {e}")
        return False

def check_step2_fill_recipient():
    """检查第2步：填写收件人"""
    print("\n🔍 检查第2步：填写收件人")
    print("-" * 50)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        step2_source = inspect.getsource(UnifiedEmailSender._step2_fill_recipient)
        
        # 检查选择器完整性
        selector_checks = [
            ("input[type=\"text\"]" in step2_source, "主要选择器: input[type='text']"),
            ("input[name=\"to\"]" in step2_source, "备用选择器: input[name='to']"),
            ("input[name=\"mailto\"]" in step2_source, "备用选择器: input[name='mailto']"),
            ("input[name*=\"to\"]" in step2_source, "备用选择器: input[name*='to']"),
            ("input[placeholder*=\"收件人\"]" in step2_source, "备用选择器: placeholder收件人"),
            ("textarea[name*=\"to\"]" in step2_source, "备用选择器: textarea[name*='to']"),
            ("querySelectorAll('input[type=\"text\"]')[0]" in step2_source, "备用选择器: 第一个文本框")
        ]
        
        # 检查操作逻辑
        operation_checks = [
            ("offsetParent !== null" in step2_source, "可见性检查"),
            ("focus()" in step2_source, "focus操作"),
            ("value = arguments[1]" in step2_source, "值设置"),
            ("Event('input'" in step2_source, "input事件"),
            ("Event('change'" in step2_source, "change事件"),
            ("Event('blur'" in step2_source, "blur事件"),
            ("time.sleep(0.2)" in step2_source, "等待机制"),
            ("页面所有输入框" in step2_source, "页面元素检查"),
            ("找到收件人字段" in step2_source, "元素发现日志")
        ]
        
        all_checks = selector_checks + operation_checks
        passed = 0
        
        print("  📋 选择器检查:")
        for check, desc in selector_checks:
            status = "✅" if check else "❌"
            print(f"    {status} {desc}")
            if check:
                passed += 1
        
        print("  📋 操作逻辑检查:")
        for check, desc in operation_checks:
            status = "✅" if check else "❌"
            print(f"    {status} {desc}")
            if check:
                passed += 1
        
        print(f"  📊 第2步检查: {passed}/{len(all_checks)} 项通过")
        return passed == len(all_checks)
        
    except Exception as e:
        print(f"  ❌ 第2步检查失败: {e}")
        return False

def check_step3_fill_subject():
    """检查第3步：填写主题"""
    print("\n🔍 检查第3步：填写主题")
    print("-" * 50)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        step3_source = inspect.getsource(UnifiedEmailSender._step3_fill_subject)
        
        # 检查选择器（基于测试成功的逻辑）
        selector_checks = [
            ("input[name=\"subj\"][class=\"input inp_base\"]" in step3_source, "精确选择器: name+class"),
            ("input[name=\"subj\"]" in step3_source, "备用选择器: name=subj")
        ]
        
        # 检查操作逻辑
        operation_checks = [
            ("offsetParent !== null" in step3_source, "可见性检查"),
            ("focus()" in step3_source, "focus操作"),
            ("value = arguments[1]" in step3_source, "值设置"),
            ("Event('input'" in step3_source, "input事件"),
            ("Event('change'" in step3_source, "change事件"),
            ("Event('blur'" not in step3_source, "正确无blur事件（与测试一致）"),
            ("主题已填写" in step3_source, "成功日志"),
            ("主题字段" in step3_source, "错误日志")
        ]
        
        all_checks = selector_checks + operation_checks
        passed = 0
        
        print("  📋 选择器检查:")
        for check, desc in selector_checks:
            status = "✅" if check else "❌"
            print(f"    {status} {desc}")
            if check:
                passed += 1
        
        print("  📋 操作逻辑检查:")
        for check, desc in operation_checks:
            status = "✅" if check else "❌"
            print(f"    {status} {desc}")
            if check:
                passed += 1
        
        print(f"  📊 第3步检查: {passed}/{len(all_checks)} 项通过")
        return passed == len(all_checks)
        
    except Exception as e:
        print(f"  ❌ 第3步检查失败: {e}")
        return False

def check_step4_fill_content():
    """检查第4步：填写内容"""
    print("\n🔍 检查第4步：填写内容")
    print("-" * 50)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        step4_source = inspect.getsource(UnifiedEmailSender._step4_fill_content)
        
        # 检查iframe方式
        iframe_checks = [
            ("iframe[class=\"iframe\"]" in step4_source, "主要iframe选择器"),
            ("iframe.iframe" in step4_source, "CSS类iframe选择器"),
            ("iframe[name=\"content\"]" in step4_source, "name=content iframe"),
            ("iframe[id=\"content\"]" in step4_source, "id=content iframe"),
            ("iframe[name*=\"editor\"]" in step4_source, "editor相关iframe"),
            ("iframe[id*=\"editor\"]" in step4_source, "editor相关iframe"),
            ("contentDocument || iframe.contentWindow.document" in step4_source, "使用contentDocument访问"),
            ("switch_to.frame" not in step4_source, "无Selenium iframe切换")
        ]
        
        # 检查富文本编辑器方式
        contenteditable_checks = [
            ("div[contenteditable=\"true\"]" in step4_source, "富文本编辑器选择器"),
            ("div[contenteditable=\"\"]" in step4_source, "空contenteditable选择器")
        ]
        
        # 检查textarea方式
        textarea_checks = [
            ("textarea[name=\"content\"]" in step4_source, "content textarea"),
            ("textarea[name*=\"content\"]" in step4_source, "content相关textarea"),
            ("textarea[name*=\"body\"]" in step4_source, "body相关textarea")
        ]
        
        # 检查通用逻辑
        general_checks = [
            ("offsetParent !== null" in step4_source, "可见性检查"),
            ("console.log" in step4_source, "JavaScript日志"),
            ("innerHTML = arguments[0]" in step4_source, "innerHTML设置"),
            ("Event('input'" in step4_source, "input事件")
        ]
        
        all_checks = iframe_checks + contenteditable_checks + textarea_checks + general_checks
        passed = 0
        
        print("  📋 iframe方式检查:")
        for check, desc in iframe_checks:
            status = "✅" if check else "❌"
            print(f"    {status} {desc}")
            if check:
                passed += 1
        
        print("  📋 富文本编辑器检查:")
        for check, desc in contenteditable_checks:
            status = "✅" if check else "❌"
            print(f"    {status} {desc}")
            if check:
                passed += 1
        
        print("  📋 textarea方式检查:")
        for check, desc in textarea_checks:
            status = "✅" if check else "❌"
            print(f"    {status} {desc}")
            if check:
                passed += 1
        
        print("  📋 通用逻辑检查:")
        for check, desc in general_checks:
            status = "✅" if check else "❌"
            print(f"    {status} {desc}")
            if check:
                passed += 1
        
        print(f"  📊 第4步检查: {passed}/{len(all_checks)} 项通过")
        return passed == len(all_checks)
        
    except Exception as e:
        print(f"  ❌ 第4步检查失败: {e}")
        return False

def check_step5_click_send():
    """检查第5步：点击发送"""
    print("\n🔍 检查第5步：点击发送")
    print("-" * 50)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        step5_source = inspect.getsource(UnifiedEmailSender._step5_click_send)
        
        # 检查发送按钮选择器
        button_checks = [
            ("input[type=\"submit\"][value=\"发送\"]" in step5_source, "精确发送按钮选择器"),
            ("input[value=\"发送\"]" in step5_source, "发送值选择器"),
            ("input[type=\"submit\"][value*=\"发送\"]" in step5_source, "模糊发送值匹配"),
            ("button[text()=\"发送\"]" in step5_source, "button text选择器"),
            ("button:contains(\"发送\")" in step5_source, "button contains选择器"),
            ("button[contains(text(), \"发送\")]" in step5_source, "button contains text选择器"),
            ("input[type=\"submit\"]" in step5_source, "通用submit选择器"),
            ("button[type=\"submit\"]" in step5_source, "button submit选择器")
        ]
        
        # 检查操作逻辑
        operation_checks = [
            ("offsetParent !== null" in step5_source, "可见性检查"),
            ("focus()" in step5_source, "focus操作"),
            ("click()" in step5_source, "click操作"),
            ("console.log" in step5_source, "JavaScript日志"),
            ("time.sleep(0.3)" in step5_source, "等待检查时间")
        ]
        
        # 检查成功确认
        success_checks = [
            ("您的邮件已发送" in step5_source, "成功检查文本1"),
            ("发送成功" in step5_source, "成功检查文本2"),
            ("邮件已发送" in step5_source, "成功检查文本3")
        ]
        
        all_checks = button_checks + operation_checks + success_checks
        passed = 0
        
        print("  📋 发送按钮选择器检查:")
        for check, desc in button_checks:
            status = "✅" if check else "❌"
            print(f"    {status} {desc}")
            if check:
                passed += 1
        
        print("  📋 操作逻辑检查:")
        for check, desc in operation_checks:
            status = "✅" if check else "❌"
            print(f"    {status} {desc}")
            if check:
                passed += 1
        
        print("  📋 成功确认检查:")
        for check, desc in success_checks:
            status = "✅" if check else "❌"
            print(f"    {status} {desc}")
            if check:
                passed += 1
        
        print(f"  📊 第5步检查: {passed}/{len(all_checks)} 项通过")
        return passed == len(all_checks)
        
    except Exception as e:
        print(f"  ❌ 第5步检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 全面检查5步骤完整性")
    print("目标：确保第一步策略中的所有5步都完全应用了测试成功的逻辑")
    print("对比：multi_browser_sender_flow_test.py中测试的五步骤")
    print("=" * 80)
    
    # 检查所有5步
    step_results = []
    step_results.append(check_step1_click_compose())
    step_results.append(check_step2_fill_recipient())
    step_results.append(check_step3_fill_subject())
    step_results.append(check_step4_fill_content())
    step_results.append(check_step5_click_send())
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 全面检查结果汇总")
    print("=" * 80)
    
    passed_steps = sum(step_results)
    total_steps = len(step_results)
    success_rate = (passed_steps / total_steps) * 100 if total_steps > 0 else 0
    
    print(f"✅ 通过的步骤: {passed_steps}/{total_steps}")
    print(f"❌ 失败的步骤: {total_steps - passed_steps}/{total_steps}")
    print(f"📈 完整性: {success_rate:.1f}%")
    
    # 详细结果
    step_names = ["第1步：点击写信按钮", "第2步：填写收件人", "第3步：填写主题", "第4步：填写内容", "第5步：点击发送"]
    print(f"\n📋 详细结果:")
    for i, (result, name) in enumerate(zip(step_results, step_names)):
        status = "✅" if result else "❌"
        print(f"  {status} {name}")
    
    if success_rate == 100:
        print("\n🎉 全面检查完全通过！所有5步都已完全应用测试成功的逻辑！")
        print("✅ 第一步策略已完整复刻测试成功的5步流程")
        print("✅ 多浏览器发送将严格按照测试成功的顺序执行")
        print("✅ 所有选择器、操作逻辑、事件触发都与测试一致")
        print("✅ 调试信息完善，错误处理精确")
        
        print("\n🎯 5步骤完整性确认:")
        print("  1. 🖱️ 点击写信按钮 - 调用prepare_compose_page()")
        print("  2. 📧 填写收件人 - 完整选择器 + 可见性检查 + 事件触发")
        print("  3. 📝 填写主题 - 精确选择器 + 无blur事件（与测试一致）")
        print("  4. 📄 填写内容 - iframe/contenteditable/textarea三种方式")
        print("  5. 🚀 点击发送 - 8种选择器 + 成功确认")
        
    elif success_rate >= 80:
        print("\n✅ 检查基本通过，但有部分步骤需要完善")
        print("请检查失败的步骤并进行修复")
    else:
        print("\n❌ 检查未通过，多个步骤需要修复")
        print("请逐步检查和修复每个失败的步骤")
    
    return 0 if success_rate == 100 else 1

if __name__ == "__main__":
    sys.exit(main())
