#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的多浏览器发送器
实现先添加任务，再启动发送器，然后逐渐发送邮件的流程
"""

import sys
import os
import time
import threading
from typing import List, Dict, Any, Optional
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from loguru import logger

# 导入原有组件
try:
    from src.core.email_sending_scheduler import EmailSendingScheduler, SendingConfig
    from src.core.multi_sender_manager import MultiSenderManager, MultiSenderConfig, RotationStrategy, IntervalStrategy
    from src.core.send_mode_manager import SendModeManager, SendModeConfig, SendMode, Priority
    from src.models.account import Account, AccountManager
    from src.models.database import DatabaseManager
    from src.gui.email_template_widget import EmailTemplateWidget
    from src.gui.send_statistics_widget import SendStatisticsWidget
    from src.gui.data_source_widget import DataSourceWidget
    from src.gui.import_template_widget import ImportTemplateWidget
    from src.utils.logger import setup_logger
except ImportError as e:
    logger.error(f"导入原有组件失败: {e}")

# 导入智能任务管理系统
try:
    from src.core.email_sending_manager import EmailSendingManager, SendingConfig as TaskSendingConfig, SendingMode as TaskSendingMode
    from src.core.smart_task_queue import TaskPriority
    from src.core.unified_email_sender import SendingStrategy, UnifiedEmailSender
    # 移除sender_factory导入，直接在代码中创建浏览器
except ImportError as e:
    logger.error(f"导入智能任务管理系统失败: {e}")


class OptimizedMultiBrowserWidget(QWidget):
    """优化的多浏览器发送器界面 - 先添加任务再发送"""
    
    # 信号定义
    stats_updated = pyqtSignal(dict)
    log_message = pyqtSignal(str, str)  # message, level
    task_added = pyqtSignal(dict)  # 任务添加信号
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        super().__init__()
        
        # 数据库管理器
        if db_manager is None:
            from src.utils.config_manager import ConfigManager
            config_manager = ConfigManager()
            db_path = config_manager.get_database_path()
            self.db_manager = DatabaseManager(str(db_path))
        else:
            self.db_manager = db_manager
        
        # 原有组件
        self.scheduler: Optional[EmailSendingScheduler] = None
        self.accounts: List[Account] = []
        self.is_sending = False
        
        # 新增：智能任务管理系统
        self.task_sending_manager = None
        self.task_queue = []  # 本地任务队列
        self.current_task_index = 0
        
        # 界面组件
        self.template_widget = None
        self.statistics_widget = None
        self.data_source_widget = None
        self.import_template_widget = None
        
        # 变量存储
        self.current_variables = {}
        
        # 多邮箱管理器
        self.multi_sender_manager: Optional[MultiSenderManager] = None
        
        # 发送模式管理器
        self.send_mode_manager: Optional[SendModeManager] = None
        
        # 定时器
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_stats_display)
        
        # 发送定时器（用于逐渐发送）
        self.send_timer = QTimer()
        self.send_timer.timeout.connect(self.send_next_task)
        
        self.init_ui()
        self.connect_signals()
        self.init_task_management()
        self.load_accounts()

        # 延迟加载模板和数据源，确保界面组件已创建
        QTimer.singleShot(100, self.load_templates)
        QTimer.singleShot(200, self.refresh_data_sources)
        
        logger.info("🌐 优化多浏览器发送器初始化完成")
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("🚀 优化多浏览器邮件发送系统")
        self.setGeometry(100, 100, 1600, 1000)
        self.setMinimumSize(1400, 900)
        
        # 设置样式
        self.setStyleSheet("""
            QWidget {
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 9pt;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                font-size: 10pt;
                font-weight: bold;
            }
            QPushButton {
                background-color: #3498db;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }
        """)
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 顶部流程说明
        flow_info = self.create_flow_info()
        main_layout.addWidget(flow_info)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧控制面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧主工作区域
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 1200])
        
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)
    
    def create_flow_info(self) -> QWidget:
        """创建流程说明"""
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.Box)
        info_frame.setMaximumHeight(50)  # 限制高度
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 1px solid #4CAF50;
                border-radius: 6px;
                padding: 5px;
            }
        """)

        layout = QHBoxLayout()
        layout.setContentsMargins(8, 4, 8, 4)  # 减少边距
        layout.setSpacing(10)

        # 流程图标
        icon_label = QLabel("🔄")
        icon_label.setStyleSheet("font-size: 16px;")  # 减小图标
        layout.addWidget(icon_label)

        # 流程说明
        flow_label = QLabel("""
        <b>发送流程：</b>
        <span style="color: #2e7d32;">
        ① 编辑邮件 → ② 添加任务 → ③ 启动发送器 → ④ 逐渐发送
        </span>
        """)
        flow_label.setStyleSheet("font-size: 11px;")  # 减小字体
        layout.addWidget(flow_label)

        layout.addStretch()

        # 状态指示
        self.flow_status_label = QLabel("📝 准备添加任务")
        self.flow_status_label.setStyleSheet("font-weight: bold; color: #1976d2; font-size: 11px;")
        layout.addWidget(self.flow_status_label)

        info_frame.setLayout(layout)
        return info_frame

    def create_left_panel(self) -> QWidget:
        """创建左侧控制面板"""
        panel = QWidget()
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(5, 5, 5, 5)

        # 任务队列状态
        queue_group = self.create_task_queue_group()
        layout.addWidget(queue_group)

        # 发送控制
        control_group = self.create_send_control_group()
        layout.addWidget(control_group)

        # 发送配置
        config_group = self.create_send_config_group()
        layout.addWidget(config_group)

        # 账号状态
        account_group = self.create_account_status_group()
        layout.addWidget(account_group)

        # 发送日志
        log_group = self.create_log_group()
        layout.addWidget(log_group)

        panel.setLayout(layout)
        return panel

    def create_task_queue_group(self) -> QGroupBox:
        """创建任务队列状态组"""
        group = QGroupBox("📋 任务队列状态")
        layout = QVBoxLayout()

        # 任务统计
        stats_layout = QGridLayout()

        self.total_tasks_label = QLabel("总任务: 0")
        self.pending_tasks_label = QLabel("待发送: 0")
        self.completed_tasks_label = QLabel("已完成: 0")
        self.failed_tasks_label = QLabel("失败: 0")

        stats_layout.addWidget(self.total_tasks_label, 0, 0)
        stats_layout.addWidget(self.pending_tasks_label, 0, 1)
        stats_layout.addWidget(self.completed_tasks_label, 1, 0)
        stats_layout.addWidget(self.failed_tasks_label, 1, 1)

        layout.addLayout(stats_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)

        # 队列操作按钮
        queue_btn_layout = QHBoxLayout()

        self.clear_queue_btn = QPushButton("🧹 清空队列")
        self.clear_queue_btn.clicked.connect(self.clear_task_queue)
        queue_btn_layout.addWidget(self.clear_queue_btn)

        self.refresh_queue_btn = QPushButton("🔄 刷新")
        self.refresh_queue_btn.clicked.connect(self.refresh_task_queue)
        queue_btn_layout.addWidget(self.refresh_queue_btn)

        layout.addLayout(queue_btn_layout)

        group.setLayout(layout)
        return group

    def create_send_control_group(self) -> QGroupBox:
        """创建发送控制组"""
        group = QGroupBox("🚀 发送控制")
        layout = QVBoxLayout()
        layout.setSpacing(6)  # 减少间距

        # 主要控制按钮 - 使用网格布局
        main_btn_layout = QGridLayout()
        main_btn_layout.setSpacing(4)

        self.start_sender_btn = QPushButton("🚀 启动")
        self.start_sender_btn.clicked.connect(self.start_sender)
        self.start_sender_btn.setStyleSheet("background-color: #27ae60; font-size: 10px; padding: 6px;")
        main_btn_layout.addWidget(self.start_sender_btn, 0, 0)

        self.stop_btn = QPushButton("🛑 停止")
        self.stop_btn.clicked.connect(self.stop_sending)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("background-color: #e74c3c; font-size: 10px; padding: 6px;")
        main_btn_layout.addWidget(self.stop_btn, 0, 1)

        # 次要控制按钮
        secondary_btn_layout = QHBoxLayout()
        secondary_btn_layout.setSpacing(4)

        self.pause_btn = QPushButton("⏸️")
        self.pause_btn.clicked.connect(self.pause_sending)
        self.pause_btn.setEnabled(False)
        self.pause_btn.setMaximumWidth(40)
        self.pause_btn.setStyleSheet("font-size: 10px; padding: 4px;")
        secondary_btn_layout.addWidget(self.pause_btn)

        self.resume_btn = QPushButton("▶️")
        self.resume_btn.clicked.connect(self.resume_sending)
        self.resume_btn.setEnabled(False)
        self.resume_btn.setMaximumWidth(40)
        self.resume_btn.setStyleSheet("font-size: 10px; padding: 4px;")
        secondary_btn_layout.addWidget(self.resume_btn)

        secondary_btn_layout.addStretch()

        layout.addLayout(main_btn_layout)
        layout.addLayout(secondary_btn_layout)

        # 状态信息 - 使用更紧凑的布局
        status_layout = QVBoxLayout()
        status_layout.setSpacing(2)

        self.send_status_label = QLabel("状态: 空闲")
        self.send_status_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 10px;")
        status_layout.addWidget(self.send_status_label)

        self.send_speed_label = QLabel("速度: 0/分钟")
        self.send_speed_label.setStyleSheet("font-size: 9px; color: #7f8c8d;")
        status_layout.addWidget(self.send_speed_label)

        layout.addLayout(status_layout)

        group.setLayout(layout)
        return group

    def create_send_config_group(self) -> QGroupBox:
        """创建发送配置组"""
        group = QGroupBox("⚙️ 发送配置")
        layout = QFormLayout()
        layout.setSpacing(4)  # 减少间距

        # 发送间隔
        self.send_interval_spin = QDoubleSpinBox()
        self.send_interval_spin.setRange(0.5, 60.0)
        self.send_interval_spin.setValue(2.0)
        self.send_interval_spin.setSuffix(" 秒")
        self.send_interval_spin.setStyleSheet("font-size: 9px;")
        layout.addRow("发送间隔:", self.send_interval_spin)

        # 浏览器数量（多账号发送）
        self.browser_count_spin = QSpinBox()
        self.browser_count_spin.setRange(1, 10)
        self.browser_count_spin.setValue(3)
        self.browser_count_spin.setStyleSheet("font-size: 9px;")
        layout.addRow("同时账号数:", self.browser_count_spin)

        # 每账号发送数
        self.emails_per_account_spin = QSpinBox()
        self.emails_per_account_spin.setRange(1, 100)
        self.emails_per_account_spin.setValue(10)
        self.emails_per_account_spin.setStyleSheet("font-size: 9px;")
        layout.addRow("每账号发送:", self.emails_per_account_spin)

        # 轮换策略
        self.rotation_strategy_combo = QComboBox()
        self.rotation_strategy_combo.addItems(["顺序轮换", "随机轮换", "负载均衡"])
        self.rotation_strategy_combo.setStyleSheet("font-size: 9px;")
        layout.addRow("轮换策略:", self.rotation_strategy_combo)

        # 固定抄送邮箱
        self.fixed_cc_edit = QLineEdit()
        self.fixed_cc_edit.setPlaceholderText("每封邮件都抄送的固定邮箱")
        self.fixed_cc_edit.setStyleSheet("font-size: 9px;")
        layout.addRow("固定抄送:", self.fixed_cc_edit)

        # 多账号发送开关
        self.multi_account_check = QCheckBox("启用多账号同时发送")
        self.multi_account_check.setChecked(True)
        self.multi_account_check.setStyleSheet("font-size: 9px;")
        layout.addRow("", self.multi_account_check)

        # 移除真实浏览器开关，强制使用真实浏览器
        # 添加浏览器状态显示
        self.browser_status_label = QLabel("🌐 真实Chrome浏览器（启动发送器时创建）")
        self.browser_status_label.setStyleSheet("font-size: 9px; color: #1976d2; font-weight: bold;")
        layout.addRow("浏览器模式:", self.browser_status_label)

        group.setLayout(layout)
        return group

    def create_account_status_group(self) -> QGroupBox:
        """创建账号状态组"""
        group = QGroupBox("👤 账号状态")
        layout = QVBoxLayout()

        # 账号统计
        self.account_count_label = QLabel("账号数量: 0")
        layout.addWidget(self.account_count_label)

        self.active_account_label = QLabel("活跃账号: 0")
        layout.addWidget(self.active_account_label)

        # 刷新按钮
        refresh_account_btn = QPushButton("🔄 刷新账号")
        refresh_account_btn.clicked.connect(self.load_accounts)
        layout.addWidget(refresh_account_btn)

        group.setLayout(layout)
        return group

    def create_log_group(self) -> QGroupBox:
        """创建日志组"""
        group = QGroupBox("📝 发送日志")
        layout = QVBoxLayout()

        # 日志文本
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(150)
        self.log_text.setStyleSheet(
            "background-color: #2c3e50; color: #ecf0f1; "
            "font-family: 'Consolas', monospace; font-size: 8pt;"
        )
        layout.addWidget(self.log_text)

        # 日志控制
        log_btn_layout = QHBoxLayout()

        clear_log_btn = QPushButton("🧹 清空")
        clear_log_btn.setMaximumWidth(60)
        clear_log_btn.clicked.connect(self.clear_log)
        log_btn_layout.addWidget(clear_log_btn)

        log_btn_layout.addStretch()
        layout.addLayout(log_btn_layout)

        group.setLayout(layout)
        return group

    def create_right_panel(self) -> QWidget:
        """创建右侧主工作区域"""
        panel = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(10)

        # 创建主标签页
        self.main_tab_widget = QTabWidget()

        # 邮件编辑标签页
        email_edit_tab = self.create_email_edit_tab()
        self.main_tab_widget.addTab(email_edit_tab, "📧 邮件编辑")

        # 任务队列标签页
        task_queue_tab = self.create_task_queue_tab()
        self.main_tab_widget.addTab(task_queue_tab, "📋 任务队列")

        # 模板管理标签页
        template_tab = self.create_template_tab()
        self.main_tab_widget.addTab(template_tab, "📝 模板管理")

        # 数据源管理标签页
        data_source_tab = self.create_data_source_tab()
        self.main_tab_widget.addTab(data_source_tab, "📊 数据源管理")

        # 发送统计标签页
        statistics_tab = self.create_statistics_tab()
        self.main_tab_widget.addTab(statistics_tab, "📈 发送统计")

        main_layout.addWidget(self.main_tab_widget)
        panel.setLayout(main_layout)
        return panel

    def create_email_edit_tab(self) -> QWidget:
        """创建邮件编辑标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        layout.setSpacing(15)

        # 收件人设置组
        recipient_group = QGroupBox("📮 收件人设置")
        recipient_layout = QVBoxLayout()

        # 收件人输入方式选择
        input_method_layout = QHBoxLayout()

        self.manual_input_radio = QRadioButton("手动输入")
        self.manual_input_radio.setChecked(True)
        self.manual_input_radio.toggled.connect(self.on_input_method_changed)
        input_method_layout.addWidget(self.manual_input_radio)

        self.data_source_radio = QRadioButton("数据源选择")
        self.data_source_radio.toggled.connect(self.on_input_method_changed)
        input_method_layout.addWidget(self.data_source_radio)

        self.data_import_radio = QRadioButton("数据导入")
        self.data_import_radio.toggled.connect(self.on_input_method_changed)
        input_method_layout.addWidget(self.data_import_radio)

        input_method_layout.addStretch()
        recipient_layout.addLayout(input_method_layout)

        # 手动输入区域
        self.manual_input_widget = QWidget()
        manual_layout = QVBoxLayout()

        # 收件人邮箱
        email_layout = QHBoxLayout()
        email_layout.addWidget(QLabel("收件人邮箱:"))
        self.recipient_emails_edit = QTextEdit()
        self.recipient_emails_edit.setPlaceholderText(
            "请输入收件人邮箱，每行一个，例如:\n"
            "<EMAIL>\n"
            "<EMAIL>\n"
            "<EMAIL>"
        )
        self.recipient_emails_edit.setMaximumHeight(100)
        email_layout.addWidget(self.recipient_emails_edit)
        manual_layout.addLayout(email_layout)

        self.manual_input_widget.setLayout(manual_layout)
        recipient_layout.addWidget(self.manual_input_widget)

        # 数据源选择区域
        self.data_source_widget_container = QWidget()
        data_source_layout = QVBoxLayout()

        data_source_select_layout = QHBoxLayout()
        data_source_select_layout.addWidget(QLabel("选择数据源:"))
        self.data_source_combo = QComboBox()
        self.data_source_combo.addItem("请选择数据源...")
        data_source_select_layout.addWidget(self.data_source_combo)
        self.data_source_combo.currentTextChanged.connect(self.on_data_source_selected)

        refresh_data_btn = QPushButton("🔄 刷新")
        refresh_data_btn.clicked.connect(self.refresh_data_sources)
        data_source_select_layout.addWidget(refresh_data_btn)

        # 添加选择收件人按钮
        select_recipients_btn = QPushButton("📋 选择收件人")
        select_recipients_btn.clicked.connect(self.select_recipients_from_source)
        data_source_select_layout.addWidget(select_recipients_btn)

        data_source_layout.addLayout(data_source_select_layout)

        self.data_source_widget_container.setLayout(data_source_layout)
        self.data_source_widget_container.setVisible(False)
        recipient_layout.addWidget(self.data_source_widget_container)

        # 数据导入区域
        self.data_import_widget_container = QWidget()
        data_import_layout = QVBoxLayout()

        import_btn_layout = QHBoxLayout()

        import_excel_btn = QPushButton("📊 导入Excel")
        import_excel_btn.clicked.connect(self.import_excel_data)
        import_btn_layout.addWidget(import_excel_btn)

        import_csv_btn = QPushButton("📄 导入CSV")
        import_csv_btn.clicked.connect(self.import_csv_data)
        import_btn_layout.addWidget(import_csv_btn)

        import_txt_btn = QPushButton("📝 导入TXT")
        import_txt_btn.clicked.connect(self.import_txt_data)
        import_btn_layout.addWidget(import_txt_btn)

        data_import_layout.addLayout(import_btn_layout)

        # 导入预览
        self.import_preview_label = QLabel("导入预览：")
        data_import_layout.addWidget(self.import_preview_label)

        self.import_preview_text = QTextEdit()
        self.import_preview_text.setMaximumHeight(80)
        self.import_preview_text.setReadOnly(True)
        self.import_preview_text.setPlaceholderText("导入的邮箱地址将在这里预览...")
        data_import_layout.addWidget(self.import_preview_text)

        # 导入操作按钮
        import_action_layout = QHBoxLayout()

        clear_import_btn = QPushButton("🧹 清空")
        clear_import_btn.clicked.connect(self.clear_import_data)
        import_action_layout.addWidget(clear_import_btn)

        apply_import_btn = QPushButton("✅ 应用到收件人")
        apply_import_btn.clicked.connect(self.apply_import_data)
        import_action_layout.addWidget(apply_import_btn)

        data_import_layout.addLayout(import_action_layout)

        self.data_import_widget_container.setLayout(data_import_layout)
        self.data_import_widget_container.setVisible(False)
        recipient_layout.addWidget(self.data_import_widget_container)

        recipient_group.setLayout(recipient_layout)
        layout.addWidget(recipient_group)

        # 邮件内容组
        content_group = QGroupBox("📝 邮件内容")
        content_layout = QFormLayout()

        # 邮件主题
        self.subject_edit = QLineEdit()
        self.subject_edit.setPlaceholderText("请输入邮件主题")
        content_layout.addRow("邮件主题:", self.subject_edit)

        # 邮件内容
        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("请输入邮件内容，支持HTML格式")
        self.content_edit.setMaximumHeight(200)
        content_layout.addRow("邮件内容:", self.content_edit)

        # 内容类型
        content_type_layout = QHBoxLayout()
        self.content_type_combo = QComboBox()
        self.content_type_combo.addItems(["text/plain", "text/html"])
        content_type_layout.addWidget(self.content_type_combo)

        # 模板选择
        template_select_layout = QHBoxLayout()
        template_select_layout.addWidget(QLabel("快速模板:"))
        self.template_combo = QComboBox()
        self.template_combo.addItem("选择模板...")
        self.template_combo.currentTextChanged.connect(self.on_template_selected)
        template_select_layout.addWidget(self.template_combo)

        content_type_layout.addLayout(template_select_layout)
        content_layout.addRow("内容类型:", content_type_layout)

        content_group.setLayout(content_layout)
        layout.addWidget(content_group)

        # 任务设置组
        task_settings_group = QGroupBox("⚙️ 任务设置")
        task_settings_layout = QFormLayout()

        # 发送模式
        self.send_mode_combo = QComboBox()
        self.send_mode_combo.addItems([
            "单独发送 (每个收件人单独一封邮件)",
            "批量发送 (一封邮件发送给多个收件人)"
        ])
        self.send_mode_combo.setCurrentIndex(0)
        task_settings_layout.addRow("发送模式:", self.send_mode_combo)

        # 任务优先级
        self.task_priority_combo = QComboBox()
        self.task_priority_combo.addItems(["低", "普通", "高", "紧急", "关键"])
        self.task_priority_combo.setCurrentText("普通")
        task_settings_layout.addRow("任务优先级:", self.task_priority_combo)

        # 发送延迟
        self.task_delay_spin = QSpinBox()
        self.task_delay_spin.setRange(0, 3600)
        self.task_delay_spin.setValue(0)
        self.task_delay_spin.setSuffix(" 秒")
        task_settings_layout.addRow("发送延迟:", self.task_delay_spin)

        # 批量发送设置（仅在批量模式下显示）
        self.batch_settings_widget = QWidget()
        batch_settings_layout = QFormLayout()

        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 100)
        self.batch_size_spin.setValue(10)
        self.batch_size_spin.setToolTip("每封邮件最多包含多少个收件人")
        batch_settings_layout.addRow("每批收件人数:", self.batch_size_spin)

        self.batch_settings_widget.setLayout(batch_settings_layout)
        self.batch_settings_widget.setVisible(False)

        # 连接发送模式改变信号
        self.send_mode_combo.currentTextChanged.connect(self.on_send_mode_changed)

        task_settings_layout.addRow("", self.batch_settings_widget)
        task_settings_group.setLayout(task_settings_layout)
        layout.addWidget(task_settings_group)

        # 操作按钮
        button_layout = QHBoxLayout()

        preview_btn = QPushButton("👁️ 预览邮件")
        preview_btn.clicked.connect(self.preview_email)
        button_layout.addWidget(preview_btn)

        save_template_btn = QPushButton("💾 保存为模板")
        save_template_btn.clicked.connect(self.save_as_template)
        button_layout.addWidget(save_template_btn)

        button_layout.addStretch()

        # 关键按钮：添加到任务队列
        self.add_to_queue_btn = QPushButton("➕ 添加到任务队列")
        self.add_to_queue_btn.clicked.connect(self.add_to_task_queue)
        self.add_to_queue_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                font-size: 12px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
        """)
        button_layout.addWidget(self.add_to_queue_btn)

        layout.addLayout(button_layout)

        tab.setLayout(layout)
        return tab

    def create_task_queue_tab(self) -> QWidget:
        """创建任务队列标签页"""
        tab = QWidget()
        layout = QVBoxLayout()

        # 任务队列表格
        self.task_table = QTableWidget()
        self.task_table.setColumnCount(6)
        self.task_table.setHorizontalHeaderLabels([
            "收件人", "主题", "状态", "优先级", "添加时间", "操作"
        ])

        # 设置表格属性
        header = self.task_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)

        layout.addWidget(self.task_table)

        # 任务操作按钮
        task_btn_layout = QHBoxLayout()

        refresh_task_btn = QPushButton("🔄 刷新任务")
        refresh_task_btn.clicked.connect(self.refresh_task_queue)
        task_btn_layout.addWidget(refresh_task_btn)

        clear_completed_btn = QPushButton("🧹 清理已完成")
        clear_completed_btn.clicked.connect(self.clear_completed_tasks)
        task_btn_layout.addWidget(clear_completed_btn)

        clear_all_btn = QPushButton("🗑️ 清空队列")
        clear_all_btn.clicked.connect(self.clear_task_queue)
        task_btn_layout.addWidget(clear_all_btn)

        task_btn_layout.addStretch()

        # 批量导入按钮
        batch_import_btn = QPushButton("📥 批量导入任务")
        batch_import_btn.clicked.connect(self.batch_import_tasks)
        task_btn_layout.addWidget(batch_import_btn)

        layout.addLayout(task_btn_layout)

        tab.setLayout(layout)
        return tab

    def create_template_tab(self) -> QWidget:
        """创建模板管理选项卡"""
        try:
            self.template_widget = EmailTemplateWidget(self.db_manager)
            self.template_widget.template_selected.connect(self.on_template_widget_selected)
            return self.template_widget
        except Exception as e:
            logger.error(f"创建模板管理选项卡失败: {e}")
            tab = QWidget()
            layout = QVBoxLayout()
            label = QLabel("模板管理功能暂时不可用\n请检查相关依赖是否已安装")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            tab.setLayout(layout)
            return tab

    def create_data_source_tab(self) -> QWidget:
        """创建数据源管理选项卡"""
        try:
            self.data_source_widget = DataSourceWidget(self.db_manager)
            self.data_source_widget.recipients_selected.connect(self.on_recipients_selected)
            return self.data_source_widget
        except Exception as e:
            logger.error(f"创建数据源管理选项卡失败: {e}")
            tab = QWidget()
            layout = QVBoxLayout()
            label = QLabel("数据源管理功能暂时不可用\n请检查相关依赖是否已安装")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            tab.setLayout(layout)
            return tab

    def create_statistics_tab(self) -> QWidget:
        """创建发送统计选项卡"""
        try:
            self.statistics_widget = SendStatisticsWidget(self.db_manager)
            return self.statistics_widget
        except Exception as e:
            logger.error(f"创建发送统计选项卡失败: {e}")
            tab = QWidget()
            layout = QVBoxLayout()
            label = QLabel("发送统计功能暂时不可用\n请检查相关依赖是否已安装")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            tab.setLayout(layout)
            return tab

    def init_task_management(self):
        """初始化任务管理系统 - 不创建浏览器，只准备配置"""
        try:
            # 创建发送配置 - 使用第一步策略（5步逻辑复刻）
            config = TaskSendingConfig(
                mode=TaskSendingMode.MANUAL,
                strategy=SendingStrategy.ULTRA_FAST,
                concurrent_workers=self.browser_count_spin.value(),
                send_interval=2.0
            )

            # 创建邮件发送管理器
            self.task_sending_manager = EmailSendingManager(config)

            # 初始化浏览器相关变量（但不创建浏览器）
            self.browser_drivers = []
            self.current_driver_index = 0
            self.browsers_initialized = False

            logger.info("✅ 任务管理系统初始化完成（浏览器将在启动发送器时创建）")

        except Exception as e:
            logger.error(f"❌ 任务管理系统初始化失败: {e}")

    def initialize_browsers_for_sending(self):
        """启动发送器时初始化浏览器"""
        try:
            if self.browsers_initialized:
                logger.info("🌐 浏览器已初始化，跳过创建")
                return True

            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options

            # 获取浏览器数量
            browser_count = self.browser_count_spin.value()
            logger.info(f"🚀 启动发送器：开始创建 {browser_count} 个真实Chrome浏览器...")

            # 清理现有浏览器和进程
            self.cleanup_browsers()
            self._kill_existing_chrome_processes()
            self._cleanup_user_data_dirs()

            # 创建多个浏览器实例
            self.browser_drivers = []
            for i in range(browser_count):
                chrome_options = Options()
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)

                # 为每个浏览器设置不同的用户数据目录（使用绝对路径）
                import tempfile
                import os
                temp_dir = tempfile.gettempdir()
                user_data_dir = os.path.join(temp_dir, f"sina_chrome_profile_{i}_{int(time.time())}")
                chrome_options.add_argument(f'--user-data-dir={user_data_dir}')

                # 窗口位置和大小（避免重叠）
                window_x = (i % 3) * 420  # 每行3个窗口
                window_y = (i // 3) * 350  # 每350像素一行
                chrome_options.add_argument(f'--window-position={window_x},{window_y}')
                chrome_options.add_argument('--window-size=1200,800')

                # 创建浏览器实例
                driver = self._create_browser_with_retry(chrome_options, i+1)
                if not driver:
                    logger.error(f"❌ 浏览器 {i+1} 创建失败，跳过")
                    continue

                # 设置反检测
                try:
                    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                except Exception as e:
                    logger.warning(f"⚠️ 浏览器 {i+1} 反检测设置失败: {e}")

                # 设置窗口标题
                account_info = f" - {self.accounts[i].email}" if i < len(self.accounts) else f" - 浏览器{i+1}"
                try:
                    driver.execute_script(f"document.title = '新浪邮箱{account_info}';")
                except Exception as e:
                    logger.warning(f"⚠️ 浏览器 {i+1} 标题设置失败: {e}")

                # 首先导航到新浪邮箱
                success = self._navigate_to_sina_mail(driver, i+1)
                if not success:
                    logger.warning(f"⚠️ 浏览器 {i+1} 无法访问新浪邮箱")
                    # 即使导航失败也继续，可能Cookie能修复

                # 🔑 关键修复：应用Cookie进行自动登录
                if i < len(self.accounts):
                    account = self.accounts[i]
                    success = self._apply_cookies_and_login(driver, account, i+1)
                    if success:
                        logger.info(f"🔑 浏览器 {i+1} Cookie登录成功: {account.email}")
                    else:
                        logger.warning(f"⚠️ 浏览器 {i+1} Cookie登录失败: {account.email}")
                else:
                    logger.info(f"ℹ️ 浏览器 {i+1} 没有对应账号，保持当前状态")

                self.browser_drivers.append(driver)
                logger.info(f"✅ 浏览器 {i+1}/{browser_count} 创建成功")

                # 避免同时请求过多
                time.sleep(1)

            # 创建发送器工厂（改进版）
            self.current_driver_index = 0

            def create_sender_with_rotation():
                """轮换使用浏览器创建发送器，并绑定对应账号"""
                if not self.browser_drivers:
                    return None

                driver = self.browser_drivers[self.current_driver_index]
                account = self.accounts[self.current_driver_index] if self.current_driver_index < len(self.accounts) else None

                # 创建发送器并绑定账号信息 - 使用第一步策略（5步逻辑复刻）
                sender = UnifiedEmailSender(driver, SendingStrategy.ULTRA_FAST)
                if account:
                    # 为发送器设置账号信息
                    sender.account_info = account
                    logger.info(f"🔗 发送器绑定账号: {account.email}")

                self.current_driver_index = (self.current_driver_index + 1) % len(self.browser_drivers)

                return sender

            self.task_sending_manager.set_sender_factory(create_sender_with_rotation)

            # 检查是否至少有一个浏览器创建成功
            if not self.browser_drivers:
                logger.error("❌ 没有任何浏览器创建成功")
                return False

            self.browsers_initialized = True
            logger.info(f"🎉 {len(self.browser_drivers)} 个真实Chrome浏览器初始化完成")

            # 更新界面状态
            self.browser_status_label.setText(f"🌐 {len(self.browser_drivers)} 个Chrome浏览器运行中")
            self.browser_status_label.setStyleSheet("font-size: 9px; color: green; font-weight: bold;")

            return True

        except Exception as e:
            logger.error(f"❌ 浏览器初始化失败: {e}")
            return False

    def connect_signals(self):
        """连接信号"""
        self.stats_updated.connect(self.on_stats_updated)
        self.log_message.connect(self.on_log_message)
        self.task_added.connect(self.on_task_added)

    def load_templates(self):
        """加载模板"""
        try:
            # 加载邮件模板到下拉框
            if hasattr(self, 'template_combo'):
                # 清空现有选项
                self.template_combo.clear()
                self.template_combo.addItem("选择模板...")

                # 从数据库加载模板
                from src.models.email_template import EmailTemplateManager
                template_manager = EmailTemplateManager(self.db_manager)
                templates = template_manager.get_all_templates()

                for template in templates:
                    # 修复属性访问问题
                    template_name = getattr(template, 'name', None) or getattr(template, 'template_name', f"模板_{template.id}")
                    self.template_combo.addItem(template_name, template)

                logger.info(f"已加载 {len(templates)} 个邮件模板")
                self.log_message.emit(f"✅ 已加载 {len(templates)} 个邮件模板", "info")

        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            self.log_message.emit(f"加载模板失败: {e}", "error")

    def refresh_data_sources(self):
        """刷新数据源"""
        try:
            if hasattr(self, 'data_source_combo'):
                # 清空现有选项
                self.data_source_combo.clear()
                self.data_source_combo.addItem("请选择数据源...")

                # 从数据库加载数据源
                from src.core.data_source_manager import DataSourceManager
                data_manager = DataSourceManager(self.db_manager)
                data_sources = data_manager.get_all_data_sources()

                for source in data_sources:
                    # 确保source是字典格式
                    if isinstance(source, dict):
                        source_name = source.get('name', f"数据源_{source.get('id', 'unknown')}")
                        self.data_source_combo.addItem(source_name, source)
                    else:
                        # 如果是对象，尝试获取属性
                        source_name = getattr(source, 'name', None) or getattr(source, 'source_name', f"数据源_{getattr(source, 'id', 'unknown')}")
                        source_dict = {
                            'id': getattr(source, 'id', None),
                            'name': source_name,
                            'description': getattr(source, 'description', ''),
                        }
                        self.data_source_combo.addItem(source_name, source_dict)

                logger.info(f"已加载 {len(data_sources)} 个数据源")
                self.log_message.emit(f"🔄 已刷新 {len(data_sources)} 个数据源", "info")

        except Exception as e:
            logger.error(f"刷新数据源失败: {e}")
            self.log_message.emit(f"刷新数据源失败: {e}", "error")

    def on_data_source_selected(self, source_name):
        """数据源选择"""
        try:
            if source_name == "请选择数据源...":
                return

            self.log_message.emit(f"已选择数据源: {source_name}", "info")

        except Exception as e:
            logger.error(f"选择数据源失败: {e}")

    def select_recipients_from_source(self):
        """从数据源选择收件人"""
        try:
            current_data = self.data_source_combo.currentData()
            if not current_data:
                QMessageBox.warning(self, "警告", "请先选择数据源")
                return

            # 获取数据源中的收件人
            from src.core.data_source_manager import DataSourceManager
            data_manager = DataSourceManager(self.db_manager)
            recipients = data_manager.get_recipients_by_source_id(current_data['id'])

            if recipients:
                # 创建选择对话框
                dialog = QDialog(self)
                dialog.setWindowTitle("选择收件人")
                dialog.setModal(True)
                dialog.resize(600, 400)

                layout = QVBoxLayout()

                # 收件人列表
                recipient_list = QListWidget()
                recipient_list.setSelectionMode(QAbstractItemView.MultiSelection)

                for recipient in recipients:
                    # 处理不同的数据格式
                    if isinstance(recipient, dict):
                        email = recipient.get('email', str(recipient))
                        item = QListWidgetItem(email)
                        item.setData(Qt.UserRole, recipient)
                    else:
                        # 如果是对象，尝试获取email属性
                        email = getattr(recipient, 'email', str(recipient))
                        item = QListWidgetItem(email)
                        item.setData(Qt.UserRole, {'email': email})

                    recipient_list.addItem(item)

                layout.addWidget(QLabel(f"数据源: {current_data['name']} (共 {len(recipients)} 个收件人)"))
                layout.addWidget(recipient_list)

                # 按钮
                btn_layout = QHBoxLayout()

                select_all_btn = QPushButton("全选")
                select_all_btn.clicked.connect(recipient_list.selectAll)
                btn_layout.addWidget(select_all_btn)

                clear_btn = QPushButton("清空")
                clear_btn.clicked.connect(recipient_list.clearSelection)
                btn_layout.addWidget(clear_btn)

                btn_layout.addStretch()

                ok_btn = QPushButton("确定")
                cancel_btn = QPushButton("取消")

                def on_ok():
                    selected_items = recipient_list.selectedItems()
                    if selected_items:
                        emails = [item.text() for item in selected_items]
                        emails_text = '\n'.join(emails)
                        self.recipient_emails_edit.setPlainText(emails_text)
                        self.log_message.emit(f"✅ 已选择 {len(emails)} 个收件人", "info")
                    dialog.accept()

                ok_btn.clicked.connect(on_ok)
                cancel_btn.clicked.connect(dialog.reject)

                btn_layout.addWidget(ok_btn)
                btn_layout.addWidget(cancel_btn)

                layout.addLayout(btn_layout)
                dialog.setLayout(layout)
                dialog.exec_()
            else:
                QMessageBox.information(self, "提示", "该数据源中没有收件人")

        except Exception as e:
            logger.error(f"从数据源选择收件人失败: {e}")
            QMessageBox.critical(self, "错误", f"从数据源选择收件人失败:\n{e}")

    def load_accounts(self):
        """加载邮箱账号"""
        try:
            # 使用AccountManager加载账号
            from src.models.account import AccountManager
            account_manager = AccountManager(self.db_manager)
            self.accounts = account_manager.get_all_accounts()

            # 更新账号状态显示
            self.account_count_label.setText(f"账号数量: {len(self.accounts)}")
            active_count = len([acc for acc in self.accounts if hasattr(acc, 'is_active') and acc.is_active])
            self.active_account_label.setText(f"活跃账号: {active_count}")

            self.log_message.emit(f"已加载 {len(self.accounts)} 个邮箱账号", "info")

        except Exception as e:
            logger.error(f"加载账号失败: {e}")
            self.log_message.emit(f"加载账号失败: {e}", "error")

    def add_to_task_queue(self):
        """添加到任务队列 - 核心方法"""
        try:
            # 获取邮件内容
            subject = self.subject_edit.text().strip()
            content = self.content_edit.toPlainText().strip()

            if not subject or not content:
                QMessageBox.warning(self, "警告", "请填写邮件主题和内容")
                return

            # 获取收件人
            recipients = []
            if self.manual_input_radio.isChecked():
                emails_text = self.recipient_emails_edit.toPlainText().strip()
                if emails_text:
                    recipients = [email.strip() for email in emails_text.split('\n') if email.strip()]

            if not recipients:
                QMessageBox.warning(self, "警告", "请添加收件人邮箱")
                return

            # 转换优先级
            priority_map = {
                "低": TaskPriority.LOW,
                "普通": TaskPriority.NORMAL,
                "高": TaskPriority.HIGH,
                "紧急": TaskPriority.URGENT,
                "关键": TaskPriority.CRITICAL
            }
            priority = priority_map.get(self.task_priority_combo.currentText(), TaskPriority.NORMAL)

            # 获取固定抄送邮箱
            fixed_cc = self.fixed_cc_edit.text().strip()

            # 获取发送模式
            send_mode = self.send_mode_combo.currentText()
            is_batch_mode = "批量发送" in send_mode

            # 创建任务数据
            tasks_data = []

            if is_batch_mode:
                # 批量发送模式：将收件人分组
                batch_size = self.batch_size_spin.value()

                # 将收件人分批
                for i in range(0, len(recipients), batch_size):
                    batch_recipients = recipients[i:i + batch_size]

                    task = {
                        'to_emails': batch_recipients,  # 批量收件人
                        'cc_email': fixed_cc if fixed_cc else None,
                        'subject': subject,
                        'content': content,
                        'content_type': self.content_type_combo.currentText(),
                        'priority': priority,
                        'delay': self.task_delay_spin.value(),
                        'status': 'pending',
                        'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'multi_account_enabled': self.multi_account_check.isChecked(),
                        'concurrent_accounts': self.browser_count_spin.value(),
                        'send_mode': 'batch',
                        'batch_size': len(batch_recipients)
                    }
                    tasks_data.append(task)
                    self.task_queue.append(task)
            else:
                # 单独发送模式：每个收件人一个任务
                for email in recipients:
                    task = {
                        'to_email': email,
                        'cc_email': fixed_cc if fixed_cc else None,
                        'subject': subject,
                        'content': content,
                        'content_type': self.content_type_combo.currentText(),
                        'priority': priority,
                        'delay': self.task_delay_spin.value(),
                        'status': 'pending',
                        'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'multi_account_enabled': self.multi_account_check.isChecked(),
                        'concurrent_accounts': self.browser_count_spin.value(),
                        'send_mode': 'individual'
                    }
                    tasks_data.append(task)
                    self.task_queue.append(task)

            # 添加到智能任务管理系统
            if self.task_sending_manager:
                batch_id = self.task_sending_manager.add_batch_tasks(
                    tasks_data=tasks_data,
                    batch_name=f"多浏览器批次_{int(time.time())}",
                    priority=priority
                )

                self.log_message.emit(f"✅ 已添加 {len(tasks_data)} 个任务到队列", "info")

            # 更新界面
            self.refresh_task_queue()
            self.update_flow_status("📋 任务已添加，可以启动发送器")

            # 清空输入框
            self.recipient_emails_edit.clear()

            QMessageBox.information(self, "成功", f"已添加 {len(tasks_data)} 个任务到队列")

        except Exception as e:
            logger.error(f"添加任务到队列失败: {e}")
            QMessageBox.critical(self, "错误", f"添加任务到队列失败:\n{e}")

    def start_sender(self):
        """启动发送器 - 核心方法"""
        try:
            if not self.task_queue and not (self.task_sending_manager and
                self.task_sending_manager.get_sending_status()['queue_stats']['total_tasks'] > 0):
                QMessageBox.warning(self, "警告", "任务队列为空，请先添加任务")
                return

            if not self.accounts:
                QMessageBox.warning(self, "警告", "没有可用的邮箱账号，请先加载账号")
                return

            # 🚀 关键修改：在启动发送器时创建浏览器
            logger.info("🚀 启动发送器：开始初始化浏览器...")
            if not self.initialize_browsers_for_sending():
                QMessageBox.critical(self, "错误", "浏览器初始化失败，无法启动发送器")
                return

            # 更新发送配置
            self.update_sending_config()

            # 启动发送器
            if self.task_sending_manager:
                success = self.task_sending_manager.start_sending()
                if success:
                    self.is_sending = True
                    self.update_button_states()
                    self.stats_timer.start(2000)
                    self.send_timer.start(int(self.send_interval_spin.value() * 1000))

                    self.log_message.emit("🚀 发送器已启动，开始逐渐发送邮件", "info")
                    self.send_status_label.setText("发送状态: 发送中")
                    self.update_flow_status("🚀 发送器运行中，正在逐渐发送邮件")
                else:
                    QMessageBox.warning(self, "警告", "启动发送器失败")
            else:
                # 使用本地队列发送
                self.is_sending = True
                self.current_task_index = 0
                self.update_button_states()
                self.send_timer.start(int(self.send_interval_spin.value() * 1000))

                self.log_message.emit("🚀 本地发送器已启动", "info")
                self.send_status_label.setText("发送状态: 发送中")
                self.update_flow_status("🚀 发送器运行中，正在逐渐发送邮件")

        except Exception as e:
            logger.error(f"启动发送器失败: {e}")
            QMessageBox.critical(self, "错误", f"启动发送器失败:\n{e}")

    def send_next_task(self):
        """发送下一个任务 - 逐渐发送的核心逻辑"""
        try:
            if not self.is_sending:
                return

            # 使用智能任务管理系统发送
            if self.task_sending_manager:
                status = self.task_sending_manager.get_sending_status()
                if status['status'] == 'completed':
                    self.stop_sending()
                    return
            else:
                # 使用本地队列发送
                pending_tasks = [task for task in self.task_queue if task['status'] == 'pending']
                if not pending_tasks:
                    self.stop_sending()
                    return

                # 发送下一个任务
                task = pending_tasks[0]
                success = self.send_single_email(task)

                if success:
                    task['status'] = 'completed'
                    self.log_message.emit(f"✅ 邮件发送成功: {task['to_email']}", "success")
                else:
                    task['status'] = 'failed'
                    self.log_message.emit(f"❌ 邮件发送失败: {task['to_email']}", "error")

                # 更新界面
                self.refresh_task_queue()

        except Exception as e:
            logger.error(f"发送任务失败: {e}")

    def send_single_email(self, task: dict) -> bool:
        """发送单封邮件"""
        try:
            # 这里实现实际的邮件发送逻辑
            # 模拟发送过程
            time.sleep(0.1)  # 模拟发送时间

            # 实际发送逻辑应该在这里实现
            # 使用多浏览器发送器发送邮件

            return True  # 模拟发送成功

        except Exception as e:
            logger.error(f"发送单封邮件失败: {e}")
            return False

    def pause_sending(self):
        """暂停发送"""
        try:
            if self.task_sending_manager:
                success = self.task_sending_manager.pause_sending()
                if success:
                    self.send_timer.stop()
                    self.update_button_states()
                    self.log_message.emit("⏸️ 发送已暂停", "info")
                    self.send_status_label.setText("发送状态: 已暂停")
                    self.update_flow_status("⏸️ 发送已暂停")
            else:
                self.send_timer.stop()
                self.update_button_states()
                self.log_message.emit("⏸️ 发送已暂停", "info")
                self.send_status_label.setText("发送状态: 已暂停")
        except Exception as e:
            logger.error(f"暂停发送失败: {e}")

    def resume_sending(self):
        """恢复发送"""
        try:
            if self.task_sending_manager:
                success = self.task_sending_manager.resume_sending()
                if success:
                    self.send_timer.start(int(self.send_interval_spin.value() * 1000))
                    self.update_button_states()
                    self.log_message.emit("▶️ 发送已恢复", "info")
                    self.send_status_label.setText("发送状态: 发送中")
                    self.update_flow_status("🚀 发送器运行中，正在逐渐发送邮件")
            else:
                self.send_timer.start(int(self.send_interval_spin.value() * 1000))
                self.update_button_states()
                self.log_message.emit("▶️ 发送已恢复", "info")
                self.send_status_label.setText("发送状态: 发送中")
        except Exception as e:
            logger.error(f"恢复发送失败: {e}")

    def stop_sending(self):
        """停止发送"""
        try:
            if self.task_sending_manager:
                success = self.task_sending_manager.stop_sending()
                if success:
                    self.is_sending = False
                    self.send_timer.stop()
                    self.stats_timer.stop()
                    self.update_button_states()
                    self.log_message.emit("🛑 发送已停止", "info")
                    self.send_status_label.setText("发送状态: 空闲")
                    self.update_flow_status("✅ 发送完成，可以添加新任务")
            else:
                self.is_sending = False
                self.send_timer.stop()
                self.stats_timer.stop()
                self.update_button_states()
                self.log_message.emit("🛑 发送已停止", "info")
                self.send_status_label.setText("发送状态: 空闲")
                self.update_flow_status("✅ 发送完成，可以添加新任务")
        except Exception as e:
            logger.error(f"停止发送失败: {e}")

    def update_sending_config(self):
        """更新发送配置"""
        try:
            if self.task_sending_manager:
                # 获取界面配置 - 使用第一步策略（5步逻辑复刻）
                strategy = SendingStrategy.ULTRA_FAST
                concurrent_workers = self.browser_count_spin.value()
                send_interval = self.send_interval_spin.value()

                # 创建新配置
                config = TaskSendingConfig(
                    mode=TaskSendingMode.MANUAL,
                    strategy=strategy,
                    concurrent_workers=concurrent_workers,
                    send_interval=send_interval
                )

                # 更新配置 - 使用正确的方法名
                if hasattr(self.task_sending_manager, 'update_config'):
                    self.task_sending_manager.update_config(config)
                elif hasattr(self.task_sending_manager, 'config'):
                    self.task_sending_manager.config = config
                else:
                    logger.warning("无法更新发送配置，管理器不支持配置更新")

                logger.info(f"发送配置已更新: 工作线程={concurrent_workers}, 发送间隔={send_interval}秒")

        except Exception as e:
            logger.error(f"更新发送配置失败: {e}")

    def update_button_states(self):
        """更新按钮状态"""
        if self.is_sending:
            self.start_sender_btn.setEnabled(False)
            self.pause_btn.setEnabled(True)
            self.resume_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.add_to_queue_btn.setEnabled(False)
        else:
            has_tasks = len(self.task_queue) > 0
            if self.task_sending_manager:
                status = self.task_sending_manager.get_sending_status()
                has_tasks = has_tasks or status['queue_stats']['total_tasks'] > 0

            self.start_sender_btn.setEnabled(has_tasks)
            self.pause_btn.setEnabled(False)
            self.resume_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)
            self.add_to_queue_btn.setEnabled(True)

    def update_flow_status(self, status: str):
        """更新流程状态"""
        self.flow_status_label.setText(status)

    def refresh_task_queue(self):
        """刷新任务队列"""
        try:
            # 更新任务统计
            total_tasks = len(self.task_queue)
            pending_tasks = len([task for task in self.task_queue if task['status'] == 'pending'])
            completed_tasks = len([task for task in self.task_queue if task['status'] == 'completed'])
            failed_tasks = len([task for task in self.task_queue if task['status'] == 'failed'])

            self.total_tasks_label.setText(f"总任务: {total_tasks}")
            self.pending_tasks_label.setText(f"待发送: {pending_tasks}")
            self.completed_tasks_label.setText(f"已完成: {completed_tasks}")
            self.failed_tasks_label.setText(f"失败: {failed_tasks}")

            # 更新进度条
            if total_tasks > 0:
                progress = int((completed_tasks / total_tasks) * 100)
                self.progress_bar.setValue(progress)

            # 更新任务表格
            self.update_task_table()

            # 更新按钮状态
            self.update_button_states()

        except Exception as e:
            logger.error(f"刷新任务队列失败: {e}")

    def update_task_table(self):
        """更新任务表格"""
        try:
            self.task_table.setRowCount(len(self.task_queue))

            for row, task in enumerate(self.task_queue):
                # 收件人 - 支持批量显示
                if 'to_emails' in task:
                    # 批量发送
                    recipients_text = f"{len(task['to_emails'])} 个收件人"
                    if len(task['to_emails']) <= 3:
                        recipients_text = ', '.join(task['to_emails'])
                    else:
                        recipients_text = f"{', '.join(task['to_emails'][:2])}... (+{len(task['to_emails'])-2})"
                else:
                    # 单独发送
                    recipients_text = task.get('to_email', '未知')

                self.task_table.setItem(row, 0, QTableWidgetItem(recipients_text))

                # 主题
                self.task_table.setItem(row, 1, QTableWidgetItem(task.get('subject', '')))

                # 状态
                status_item = QTableWidgetItem(task['status'])
                if task['status'] == 'completed':
                    status_item.setBackground(QColor("#d5f4e6"))
                elif task['status'] == 'failed':
                    status_item.setBackground(QColor("#ffebee"))
                elif task['status'] == 'pending':
                    status_item.setBackground(QColor("#fff3e0"))
                self.task_table.setItem(row, 2, status_item)

                # 优先级
                priority_text = task.get('priority', TaskPriority.NORMAL).value if hasattr(task.get('priority', TaskPriority.NORMAL), 'value') else str(task.get('priority', 'normal'))
                self.task_table.setItem(row, 3, QTableWidgetItem(priority_text))

                # 添加时间
                self.task_table.setItem(row, 4, QTableWidgetItem(task.get('created_at', '')))

                # 操作按钮
                btn_widget = QWidget()
                btn_layout = QHBoxLayout(btn_widget)
                btn_layout.setContentsMargins(2, 2, 2, 2)

                if task['status'] == 'pending':
                    cancel_btn = QPushButton("取消")
                    cancel_btn.setMaximumSize(50, 25)
                    cancel_btn.clicked.connect(lambda checked, idx=row: self.cancel_task(idx))
                    btn_layout.addWidget(cancel_btn)
                elif task['status'] == 'failed':
                    retry_btn = QPushButton("重试")
                    retry_btn.setMaximumSize(50, 25)
                    retry_btn.clicked.connect(lambda checked, idx=row: self.retry_task(idx))
                    btn_layout.addWidget(retry_btn)

                self.task_table.setCellWidget(row, 5, btn_widget)

        except Exception as e:
            logger.error(f"更新任务表格失败: {e}")

    def clear_task_queue(self):
        """清空任务队列"""
        try:
            reply = QMessageBox.question(
                self, "确认", "确定要清空所有任务吗？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.task_queue.clear()
                self.refresh_task_queue()
                self.log_message.emit("🧹 任务队列已清空", "info")
                self.update_flow_status("📝 准备添加任务")

        except Exception as e:
            logger.error(f"清空任务队列失败: {e}")

    def clear_completed_tasks(self):
        """清理已完成任务"""
        try:
            before_count = len(self.task_queue)
            self.task_queue = [task for task in self.task_queue if task['status'] != 'completed']
            after_count = len(self.task_queue)

            self.refresh_task_queue()
            self.log_message.emit(f"🧹 已清理 {before_count - after_count} 个已完成任务", "info")

        except Exception as e:
            logger.error(f"清理已完成任务失败: {e}")

    def cancel_task(self, task_index: int):
        """取消任务"""
        try:
            if 0 <= task_index < len(self.task_queue):
                task = self.task_queue[task_index]
                task['status'] = 'cancelled'
                self.refresh_task_queue()
                self.log_message.emit(f"❌ 任务已取消: {task['to_email']}", "warning")
        except Exception as e:
            logger.error(f"取消任务失败: {e}")

    def retry_task(self, task_index: int):
        """重试任务"""
        try:
            if 0 <= task_index < len(self.task_queue):
                task = self.task_queue[task_index]
                task['status'] = 'pending'
                self.refresh_task_queue()
                self.log_message.emit(f"🔄 任务已重新排队: {task['to_email']}", "info")
        except Exception as e:
            logger.error(f"重试任务失败: {e}")

    def batch_import_tasks(self):
        """批量导入任务"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择邮件数据文件",
                "",
                "Excel文件 (*.xlsx *.xls);;CSV文件 (*.csv);;所有文件 (*)"
            )

            if file_path:
                # 这里实现文件导入逻辑
                QMessageBox.information(self, "提示", "批量导入功能待实现")

        except Exception as e:
            logger.error(f"批量导入任务失败: {e}")

    def on_input_method_changed(self):
        """输入方式改变"""
        if self.manual_input_radio.isChecked():
            self.manual_input_widget.setVisible(True)
            self.data_source_widget_container.setVisible(False)
            self.data_import_widget_container.setVisible(False)
        elif self.data_source_radio.isChecked():
            self.manual_input_widget.setVisible(False)
            self.data_source_widget_container.setVisible(True)
            self.data_import_widget_container.setVisible(False)
        elif self.data_import_radio.isChecked():
            self.manual_input_widget.setVisible(False)
            self.data_source_widget_container.setVisible(False)
            self.data_import_widget_container.setVisible(True)

    def on_template_selected(self, template_name):
        """模板选择"""
        try:
            if template_name == "选择模板...":
                return

            # 获取选中的模板数据
            current_data = self.template_combo.currentData()
            if current_data:
                # 应用模板内容
                if hasattr(current_data, 'subject'):
                    self.subject_edit.setText(current_data.subject)
                if hasattr(current_data, 'content'):
                    self.content_edit.setPlainText(current_data.content)
                if hasattr(current_data, 'content_type'):
                    index = self.content_type_combo.findText(current_data.content_type)
                    if index >= 0:
                        self.content_type_combo.setCurrentIndex(index)

                self.log_message.emit(f"✅ 已应用模板: {template_name}", "info")

        except Exception as e:
            logger.error(f"应用模板失败: {e}")
            self.log_message.emit(f"应用模板失败: {e}", "error")

    def on_template_widget_selected(self, template_data):
        """模板组件选择"""
        try:
            if template_data and isinstance(template_data, dict):
                if 'subject' in template_data:
                    self.subject_edit.setText(template_data['subject'])
                if 'content' in template_data:
                    self.content_edit.setPlainText(template_data['content'])

                self.log_message.emit("✅ 模板已应用", "info")
        except Exception as e:
            logger.error(f"应用模板失败: {e}")

    def on_recipients_selected(self, recipients):
        """收件人选择"""
        try:
            if recipients:
                # 处理不同格式的收件人数据
                emails = []
                for recipient in recipients:
                    if isinstance(recipient, str):
                        emails.append(recipient)
                    elif isinstance(recipient, dict):
                        emails.append(recipient.get('email', str(recipient)))
                    else:
                        # 如果是对象，尝试获取email属性
                        email = getattr(recipient, 'email', str(recipient))
                        emails.append(email)

                emails_text = '\n'.join(emails)
                self.recipient_emails_edit.setPlainText(emails_text)
                self.log_message.emit(f"✅ 已选择 {len(emails)} 个收件人", "info")
        except Exception as e:
            logger.error(f"应用收件人失败: {e}")
            self.log_message.emit(f"应用收件人失败: {e}", "error")

    def refresh_data_sources(self):
        """刷新数据源"""
        try:
            self.log_message.emit("🔄 数据源已刷新", "info")
        except Exception as e:
            logger.error(f"刷新数据源失败: {e}")

    def preview_email(self):
        """预览邮件"""
        try:
            subject = self.subject_edit.text().strip()
            content = self.content_edit.toPlainText().strip()

            if not subject or not content:
                QMessageBox.warning(self, "警告", "请填写邮件主题和内容")
                return

            # 创建预览对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("📧 邮件预览")
            dialog.setModal(True)
            dialog.resize(600, 400)

            layout = QVBoxLayout()

            # 主题显示
            subject_label = QLabel(f"<b>主题:</b> {subject}")
            layout.addWidget(subject_label)

            # 内容显示
            content_text = QTextEdit()
            content_text.setReadOnly(True)
            content_text.setPlainText(content)
            layout.addWidget(content_text)

            # 按钮
            btn_layout = QHBoxLayout()
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            btn_layout.addStretch()
            btn_layout.addWidget(close_btn)
            layout.addLayout(btn_layout)

            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            logger.error(f"预览邮件失败: {e}")

    def save_as_template(self):
        """保存为模板"""
        try:
            subject = self.subject_edit.text().strip()
            content = self.content_edit.toPlainText().strip()

            if not subject or not content:
                QMessageBox.warning(self, "警告", "请填写邮件主题和内容")
                return

            # 获取模板名称
            name, ok = QInputDialog.getText(self, "保存模板", "请输入模板名称:")
            if ok and name.strip():
                QMessageBox.information(self, "成功", f"模板 '{name}' 保存成功")
                self.log_message.emit(f"✅ 模板 '{name}' 已保存", "info")

        except Exception as e:
            logger.error(f"保存模板失败: {e}")

    def update_stats_display(self):
        """更新统计显示"""
        try:
            if self.task_sending_manager:
                status = self.task_sending_manager.get_sending_status()
                queue_stats = status['queue_stats']

                # 更新统计显示
                self.total_tasks_label.setText(f"总任务: {queue_stats['total_tasks']}")
                self.completed_tasks_label.setText(f"已完成: {queue_stats['completed_tasks']}")
                self.failed_tasks_label.setText(f"失败: {queue_stats['failed_tasks']}")

                # 计算进度
                total = queue_stats['total_tasks']
                completed = queue_stats['completed_tasks']

                if total > 0:
                    progress = int((completed / total) * 100)
                    self.progress_bar.setValue(progress)

        except Exception as e:
            logger.error(f"更新统计显示失败: {e}")

    def clear_log(self):
        """清空日志"""
        if self.log_text:
            self.log_text.clear()

    def on_log_message(self, message: str, level: str):
        """处理日志消息"""
        if not self.log_text:
            return

        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 根据级别设置颜色
        color_map = {
            "info": "#2ecc71",
            "warning": "#f39c12",
            "error": "#e74c3c",
            "success": "#27ae60"
        }
        color = color_map.get(level, "#ecf0f1")

        formatted_message = f'<span style="color: {color}">[{timestamp}] {message}</span>'
        self.log_text.append(formatted_message)

        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def on_stats_updated(self, stats: dict):
        """处理统计更新"""
        try:
            # 更新界面统计显示
            pass
        except Exception as e:
            logger.error(f"处理统计更新失败: {e}")

    def on_task_added(self, task_data: dict):
        """处理任务添加"""
        try:
            self.refresh_task_queue()
        except Exception as e:
            logger.error(f"处理任务添加失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 停止发送
            if self.is_sending:
                self.stop_sending()

            # 停止定时器
            if self.stats_timer.isActive():
                self.stats_timer.stop()
            if self.send_timer.isActive():
                self.send_timer.stop()

            event.accept()

        except Exception as e:
            logger.error(f"关闭窗口失败: {e}")
            event.accept()

    def import_excel_data(self):
        """导入Excel数据"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择Excel文件",
                "",
                "Excel文件 (*.xlsx *.xls);;所有文件 (*)"
            )

            if file_path:
                import pandas as pd

                # 读取Excel文件
                df = pd.read_excel(file_path)

                # 查找邮箱列
                email_columns = []
                for col in df.columns:
                    if any(keyword in col.lower() for keyword in ['email', '邮箱', 'mail', '邮件']):
                        email_columns.append(col)

                if not email_columns:
                    # 如果没有找到邮箱列，使用第一列
                    email_columns = [df.columns[0]]

                # 提取邮箱地址
                emails = []
                for col in email_columns:
                    for value in df[col].dropna():
                        email_str = str(value).strip()
                        if '@' in email_str:
                            emails.append(email_str)

                # 去重
                emails = list(set(emails))

                # 显示预览
                preview_text = '\n'.join(emails[:20])  # 只显示前20个
                if len(emails) > 20:
                    preview_text += f'\n... 还有 {len(emails) - 20} 个邮箱'

                self.import_preview_text.setPlainText(preview_text)
                self.imported_emails = emails

                self.log_message.emit(f"✅ 从Excel导入了 {len(emails)} 个邮箱地址", "info")

        except Exception as e:
            logger.error(f"导入Excel数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导入Excel数据失败:\n{e}")

    def import_csv_data(self):
        """导入CSV数据"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择CSV文件",
                "",
                "CSV文件 (*.csv);;所有文件 (*)"
            )

            if file_path:
                import pandas as pd

                # 读取CSV文件
                df = pd.read_csv(file_path, encoding='utf-8')

                # 查找邮箱列
                email_columns = []
                for col in df.columns:
                    if any(keyword in col.lower() for keyword in ['email', '邮箱', 'mail', '邮件']):
                        email_columns.append(col)

                if not email_columns:
                    # 如果没有找到邮箱列，使用第一列
                    email_columns = [df.columns[0]]

                # 提取邮箱地址
                emails = []
                for col in email_columns:
                    for value in df[col].dropna():
                        email_str = str(value).strip()
                        if '@' in email_str:
                            emails.append(email_str)

                # 去重
                emails = list(set(emails))

                # 显示预览
                preview_text = '\n'.join(emails[:20])  # 只显示前20个
                if len(emails) > 20:
                    preview_text += f'\n... 还有 {len(emails) - 20} 个邮箱'

                self.import_preview_text.setPlainText(preview_text)
                self.imported_emails = emails

                self.log_message.emit(f"✅ 从CSV导入了 {len(emails)} 个邮箱地址", "info")

        except Exception as e:
            logger.error(f"导入CSV数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导入CSV数据失败:\n{e}")

    def import_txt_data(self):
        """导入TXT数据"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择TXT文件",
                "",
                "文本文件 (*.txt);;所有文件 (*)"
            )

            if file_path:
                # 读取文本文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 使用正则表达式提取邮箱地址
                import re
                email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
                emails = re.findall(email_pattern, content)

                # 去重
                emails = list(set(emails))

                # 显示预览
                preview_text = '\n'.join(emails[:20])  # 只显示前20个
                if len(emails) > 20:
                    preview_text += f'\n... 还有 {len(emails) - 20} 个邮箱'

                self.import_preview_text.setPlainText(preview_text)
                self.imported_emails = emails

                self.log_message.emit(f"✅ 从TXT导入了 {len(emails)} 个邮箱地址", "info")

        except Exception as e:
            logger.error(f"导入TXT数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导入TXT数据失败:\n{e}")

    def clear_import_data(self):
        """清空导入数据"""
        self.import_preview_text.clear()
        self.imported_emails = []
        self.log_message.emit("🧹 已清空导入数据", "info")

    def apply_import_data(self):
        """应用导入数据到收件人"""
        try:
            if hasattr(self, 'imported_emails') and self.imported_emails:
                emails_text = '\n'.join(self.imported_emails)
                self.recipient_emails_edit.setPlainText(emails_text)
                self.log_message.emit(f"✅ 已应用 {len(self.imported_emails)} 个导入的邮箱地址", "info")
            else:
                QMessageBox.warning(self, "警告", "没有导入的邮箱数据")
        except Exception as e:
            logger.error(f"应用导入数据失败: {e}")
            QMessageBox.critical(self, "错误", f"应用导入数据失败:\n{e}")

    def on_send_mode_changed(self, mode_text):
        """发送模式改变"""
        try:
            is_batch_mode = "批量发送" in mode_text
            self.batch_settings_widget.setVisible(is_batch_mode)

            if is_batch_mode:
                self.log_message.emit("📧 已切换到批量发送模式", "info")
            else:
                self.log_message.emit("📧 已切换到单独发送模式", "info")

        except Exception as e:
            logger.error(f"切换发送模式失败: {e}")

    def on_browser_mode_changed(self, checked):
        """浏览器模式改变"""
        try:
            if checked:
                self.log_message.emit("🌐 已切换到真实浏览器模式", "info")
                QMessageBox.information(
                    self,
                    "浏览器模式",
                    "已切换到真实浏览器模式\n"
                    "下次启动发送器时将使用真实Chrome浏览器\n"
                    "请确保已安装Chrome浏览器和ChromeDriver"
                )
            else:
                self.log_message.emit("🎭 已切换到模拟浏览器模式", "info")
                QMessageBox.information(
                    self,
                    "浏览器模式",
                    "已切换到模拟浏览器模式\n"
                    "下次启动发送器时将使用模拟浏览器\n"
                    "适用于测试和演示"
                )

        except Exception as e:
            logger.error(f"切换浏览器模式失败: {e}")

    def cleanup_browsers(self):
        """清理浏览器资源"""
        try:
            if hasattr(self, 'browser_drivers'):
                for i, driver in enumerate(self.browser_drivers):
                    try:
                        driver.quit()
                        logger.info(f"🧹 浏览器 {i+1} 已关闭")
                    except:
                        pass
                self.browser_drivers.clear()
                logger.info("🧹 所有浏览器已清理")
        except Exception as e:
            logger.error(f"❌ 清理浏览器失败: {e}")

    def _kill_existing_chrome_processes(self):
        """强制终止现有的Chrome进程"""
        try:
            import subprocess
            import psutil

            logger.info("🧹 正在清理现有Chrome进程...")

            # 查找并终止Chrome进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        # 检查是否是我们的Chrome进程（包含user-data-dir参数）
                        cmdline = proc.info['cmdline'] or []
                        if any('chrome_profile_' in arg for arg in cmdline):
                            proc.terminate()
                            logger.info(f"🧹 终止Chrome进程: PID {proc.info['pid']}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # 等待进程终止
            time.sleep(2)
            logger.info("✅ Chrome进程清理完成")

        except Exception as e:
            logger.warning(f"⚠️ 清理Chrome进程失败: {e}")

    def _cleanup_user_data_dirs(self):
        """清理用户数据目录"""
        try:
            import shutil
            import os

            logger.info("🧹 正在清理用户数据目录...")

            # 清理chrome_profile_*目录
            for i in range(10):  # 清理前10个可能的目录
                profile_dir = f"chrome_profile_{i}"
                if os.path.exists(profile_dir):
                    try:
                        shutil.rmtree(profile_dir)
                        logger.info(f"🧹 删除目录: {profile_dir}")
                    except Exception as e:
                        logger.warning(f"⚠️ 删除目录失败 {profile_dir}: {e}")

            logger.info("✅ 用户数据目录清理完成")

        except Exception as e:
            logger.warning(f"⚠️ 清理用户数据目录失败: {e}")

    def _create_browser_with_retry(self, chrome_options, browser_num, max_retries=3):
        """创建浏览器实例，带重试机制"""
        from selenium import webdriver

        for attempt in range(max_retries):
            try:
                logger.info(f"🌐 浏览器 {browser_num} 创建尝试 {attempt + 1}/{max_retries}")

                # 创建浏览器实例
                driver = webdriver.Chrome(options=chrome_options)

                # 设置超时
                driver.set_page_load_timeout(30)
                driver.implicitly_wait(10)

                logger.info(f"✅ 浏览器 {browser_num} 创建成功")
                return driver

            except Exception as e:
                logger.warning(f"⚠️ 浏览器 {browser_num} 创建失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # 等待2秒后重试
                else:
                    logger.error(f"❌ 浏览器 {browser_num} 创建失败，已达到最大重试次数")
                    return None

    def _navigate_to_sina_mail(self, driver, browser_num, max_retries=3):
        """导航到新浪邮箱，带重试机制"""
        for attempt in range(max_retries):
            try:
                logger.info(f"🌐 浏览器 {browser_num} 访问新浪邮箱 (尝试 {attempt + 1}/{max_retries})")

                driver.get("https://mail.sina.com.cn")

                # 等待页面加载
                time.sleep(3)

                # 检查页面是否加载成功
                current_url = driver.current_url
                if "sina.com" in current_url:
                    logger.info(f"✅ 浏览器 {browser_num} 成功访问新浪邮箱")
                    return True
                else:
                    raise Exception(f"页面跳转异常: {current_url}")

            except Exception as e:
                logger.warning(f"⚠️ 浏览器 {browser_num} 访问失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(3)  # 等待3秒后重试
                else:
                    logger.error(f"❌ 浏览器 {browser_num} 无法访问新浪邮箱")
                    return False

    def _apply_cookies_and_login(self, driver, account, browser_num):
        """应用Cookie并验证登录状态"""
        try:
            username = account.email  # 修复：使用account.email而不是account['username']
            logger.info(f"🔑 浏览器 {browser_num} 开始应用Cookie: {username}")

            # 获取Cookie管理器
            from src.core.cookie_manager import CookieManager

            # 创建配置
            config = {
                'performance.max_concurrent_sessions': 100,
                'session.timeout': 3600
            }
            cookie_manager = CookieManager(config)

            # 获取账号的Cookie
            cookie_data = cookie_manager.get_cookies(username)
            if not cookie_data or 'cookies' not in cookie_data:
                logger.warning(f"⚠️ 浏览器 {browser_num} 没有找到Cookie: {username}")
                return False

            cookies = cookie_data['cookies']

            # 确保在正确的域名下
            current_url = driver.current_url
            if "sina.com" not in current_url:
                success = self._navigate_to_sina_mail(driver, browser_num)
                if not success:
                    logger.error(f"❌ 浏览器 {browser_num} 无法访问新浪邮箱")
                    return False

            # 应用Cookie
            logger.info(f"🍪 浏览器 {browser_num} 开始应用 {len(cookies)} 个Cookie")
            for cookie in cookies:
                try:
                    # 确保Cookie格式正确
                    cookie_dict = {
                        'name': cookie.get('name'),
                        'value': cookie.get('value'),
                        'domain': cookie.get('domain', '.sina.com.cn'),
                        'path': cookie.get('path', '/'),
                    }

                    # 添加可选字段
                    if 'secure' in cookie:
                        cookie_dict['secure'] = cookie['secure']
                    if 'httpOnly' in cookie:
                        cookie_dict['httpOnly'] = cookie['httpOnly']

                    driver.add_cookie(cookie_dict)

                except Exception as e:
                    logger.debug(f"跳过无效Cookie: {e}")
                    continue

            logger.info(f"✅ 浏览器 {browser_num} Cookie应用完成")

            # 刷新页面以应用Cookie
            driver.refresh()
            time.sleep(3)

            # 强制导航到新浪邮箱（防止跳转到其他页面）
            current_url = driver.current_url
            if "mail.sina.com.cn" not in current_url:
                logger.warning(f"⚠️ 浏览器 {browser_num} 页面跳转异常: {current_url}")
                logger.info(f"🔄 浏览器 {browser_num} 强制导航到新浪邮箱")
                success = self._navigate_to_sina_mail(driver, browser_num)
                if not success:
                    logger.error(f"❌ 浏览器 {browser_num} 无法导航到新浪邮箱")
                    return False

            # 验证登录状态
            return self._verify_login_status(driver, username, browser_num)

        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} Cookie应用失败: {e}")
            return False

    def _verify_login_status(self, driver, username, browser_num):
        """验证登录状态"""
        try:
            logger.info(f"🔍 浏览器 {browser_num} 验证登录状态: {username}")

            # 检查页面标题和URL
            current_url = driver.current_url
            page_title = driver.title

            logger.info(f"📍 浏览器 {browser_num} 当前URL: {current_url}")
            logger.info(f"📄 浏览器 {browser_num} 页面标题: {page_title}")

            # 检查是否在邮箱主页
            if "mail.sina.com.cn" in current_url and "登录" not in page_title:
                # 尝试查找写信按钮或其他登录标识
                try:
                    # 查找写信按钮
                    write_buttons = driver.find_elements("xpath", "//a[contains(text(), '写信') or contains(@title, '写信')]")
                    if write_buttons:
                        logger.info(f"✅ 浏览器 {browser_num} 登录验证成功: 找到写信按钮")
                        return True

                    # 查找其他登录标识
                    user_elements = driver.find_elements("xpath", "//*[contains(text(), '收件箱') or contains(text(), '发件箱')]")
                    if user_elements:
                        logger.info(f"✅ 浏览器 {browser_num} 登录验证成功: 找到邮箱功能")
                        return True

                except Exception as e:
                    logger.debug(f"查找登录标识时出错: {e}")

            logger.warning(f"⚠️ 浏览器 {browser_num} 可能未登录，需要手动登录")
            return False

        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} 登录状态验证失败: {e}")
            return False

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            self.cleanup_browsers()
            event.accept()
        except:
            event.accept()
