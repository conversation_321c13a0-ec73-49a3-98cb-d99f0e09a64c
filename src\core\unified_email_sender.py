#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一邮件发送器 - 整合所有发送策略
整合了超高速发送、轻量化发送、标准发送等多种策略
"""

import time
from typing import Optional, Dict, Any
from enum import Enum
from loguru import logger

from .sina_ultra_fast_sender_final import SinaUltraFastSenderFinal


class SendingStrategy(Enum):
    """发送策略枚举"""
    ULTRA_FAST = "ultra_fast"      # 超高速发送
    STANDARD = "standard"          # 标准发送
    SAFE = "safe"                  # 安全发送（慢但稳定）


class SendingResult:
    """发送结果类"""
    def __init__(self, success: bool, message: str = "", response_time: float = 0.0, strategy_used: str = ""):
        self.success = success
        self.message = message
        self.response_time = response_time
        self.strategy_used = strategy_used
        self.timestamp = time.time()


class UnifiedEmailSender:
    """
    统一邮件发送器
    
    整合了所有发送策略，提供统一的发送接口
    支持多种发送模式，自动选择最佳策略
    """
    
    def __init__(self, driver, strategy: SendingStrategy = SendingStrategy.ULTRA_FAST):
        """
        初始化统一发送器
        
        Args:
            driver: WebDriver实例
            strategy: 发送策略
        """
        self.driver = driver
        self.strategy = strategy
        self.is_ready = False
        
        # 统计信息
        self.stats = {
            'total_sent': 0,
            'success_count': 0,
            'failed_count': 0,
            'avg_response_time': 0.0,
            'strategy_usage': {}
        }
        
        # 初始化发送器
        self._initialize_senders()
        
        logger.info(f"🚀 统一邮件发送器初始化完成，策略: {strategy.value}")
    
    def _initialize_senders(self):
        """初始化各种发送器"""
        try:
            # 超高速发送器
            self.ultra_fast_sender = SinaUltraFastSenderFinal(self.driver)
            logger.info("✅ 超高速发送器初始化成功")
        except Exception as e:
            logger.error(f"❌ 超高速发送器初始化失败: {e}")
            self.ultra_fast_sender = None
    
    def prepare_for_sending(self) -> bool:
        """准备发送环境"""
        try:
            logger.info("🔧 准备发送环境...")
            
            if self.strategy == SendingStrategy.ULTRA_FAST and self.ultra_fast_sender:
                success = self.ultra_fast_sender.prepare_compose_page()
                if success:
                    self.is_ready = True
                    logger.info("✅ 超高速发送环境准备完成")
                    return True
            
            # 其他策略的准备逻辑
            logger.warning("⚠️ 使用基础准备方式")
            self.is_ready = True
            return True
            
        except Exception as e:
            logger.error(f"❌ 发送环境准备失败: {e}")
            return False
    
    def send_email(self, to_email: str, subject: str, content: str, 
                   content_type: str = "text/plain") -> SendingResult:
        """
        发送邮件 - 统一接口
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            content: 邮件内容
            content_type: 内容类型
            
        Returns:
            SendingResult: 发送结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"📧 开始发送邮件: {to_email}")
            logger.info(f"📝 主题: {subject}")
            logger.info(f"🎯 策略: {self.strategy.value}")
            
            # 确保发送环境已准备
            if not self.is_ready:
                if not self.prepare_for_sending():
                    return SendingResult(False, "发送环境准备失败")
            
            # 智能策略选择：优先使用指定策略，失败时自动尝试其他策略
            logger.info(f"🎯 开始发送，主策略: {self.strategy.value}")

            if self.strategy == SendingStrategy.ULTRA_FAST:
                # 第一步策略：超高速发送（应用成功流程经验）
                result = self._send_ultra_fast(to_email, subject, content)
                if not result.success:
                    logger.info("🔄 第一步策略失败，自动尝试第二步策略...")
                    result = self._send_standard(to_email, subject, content)
                    if not result.success:
                        logger.info("🛡️ 第二步策略失败，自动尝试第三步策略...")
                        result = self._send_safe(to_email, subject, content)

            elif self.strategy == SendingStrategy.STANDARD:
                # 第二步策略：标准发送
                result = self._send_standard(to_email, subject, content)
                if not result.success:
                    logger.info("🛡️ 标准策略失败，尝试安全策略...")
                    result = self._send_safe(to_email, subject, content)

            elif self.strategy == SendingStrategy.SAFE:
                # 第三步策略：安全发送
                result = self._send_safe(to_email, subject, content)

            else:
                result = SendingResult(False, f"不支持的发送策略: {self.strategy.value}")
            
            # 计算响应时间
            response_time = time.time() - start_time
            result.response_time = response_time
            result.strategy_used = self.strategy.value
            
            # 更新统计
            self._update_stats(result)
            
            if result.success:
                logger.info(f"✅ 邮件发送成功: {to_email} ({response_time:.2f}秒)")
            else:
                logger.error(f"❌ 邮件发送失败: {to_email} - {result.message}")
            
            return result
            
        except Exception as e:
            response_time = time.time() - start_time
            error_msg = f"发送异常: {e}"
            logger.error(f"❌ {error_msg}")
            
            result = SendingResult(False, error_msg, response_time, self.strategy.value)
            self._update_stats(result)
            return result
    
    def _send_ultra_fast(self, to_email: str, subject: str, content: str) -> SendingResult:
        """第一步策略：纯粹的5步细节逻辑复刻"""
        try:
            logger.info("⚡ 第一步策略：5步细节逻辑复刻")
            logger.info("📝 5步流程:")
            logger.info("   1. 点击写信按钮")
            logger.info("   2. 填写收件人")
            logger.info("   3. 填写主题")
            logger.info("   4. 填写内容")
            logger.info("   5. 点击发送")

            if not self.ultra_fast_sender:
                return SendingResult(False, "发送器未初始化")

            # 直接执行5步逻辑复刻
            success = self._execute_5_steps_logic(to_email, subject, content)

            if success:
                logger.info("✅ 5步逻辑复刻成功")
                return SendingResult(True, "5步发送成功")
            else:
                logger.warning("⚠️ 5步逻辑复刻失败")
                return SendingResult(False, "5步发送失败")

        except Exception as e:
            logger.error(f"❌ 5步逻辑复刻异常: {e}")
            return SendingResult(False, f"5步发送异常: {e}")

    def _execute_5_steps_logic(self, to_email: str, subject: str, content: str) -> bool:
        """执行纯粹的5步细节逻辑"""
        try:
            # 第1步：点击写信按钮
            logger.info("🖱️ 第1步：点击写信按钮")
            if not self._step1_click_compose():
                logger.error("❌ 第1步失败：点击写信按钮失败")
                return False
            logger.info("✅ 第1步成功：写信按钮已点击")

            # 第2步：填写收件人
            logger.info("📧 第2步：填写收件人")
            if not self._step2_fill_recipient(to_email):
                logger.error("❌ 第2步失败：填写收件人失败")
                return False
            logger.info(f"✅ 第2步成功：收件人已填写 {to_email}")

            # 第3步：填写主题
            logger.info("📝 第3步：填写主题")
            if not self._step3_fill_subject(subject):
                logger.error("❌ 第3步失败：填写主题失败")
                return False
            logger.info(f"✅ 第3步成功：主题已填写 {subject}")

            # 第4步：填写内容
            logger.info("📄 第4步：填写内容")
            if not self._step4_fill_content(content):
                logger.error("❌ 第4步失败：填写内容失败")
                return False
            logger.info("✅ 第4步成功：内容已填写")

            # 第5步：点击发送
            logger.info("🚀 第5步：点击发送")
            if not self._step5_click_send():
                logger.error("❌ 第5步失败：点击发送失败")
                return False
            logger.info("✅ 第5步成功：发送按钮已点击")

            logger.info("🎉 5步逻辑复刻完成")
            return True

        except Exception as e:
            logger.error(f"❌ 5步逻辑执行异常: {e}")
            return False

    def _step1_click_compose(self) -> bool:
        """第1步：点击写信按钮 - 复刻成功逻辑"""
        try:
            # 复刻成功的写信按钮点击逻辑
            return self.ultra_fast_sender.prepare_compose_page()
        except Exception as e:
            logger.error(f"第1步异常: {e}")
            return False

    def _step2_fill_recipient(self, to_email: str) -> bool:
        """第2步：填写收件人 - 复刻成功逻辑"""
        try:
            # 复刻成功的收件人填写逻辑
            driver = self.ultra_fast_sender.driver

            # 使用成功验证的选择器
            to_field = driver.execute_script("""
                var toField = document.querySelector('input[type="text"]') ||
                             document.querySelector('input[name="to"]') ||
                             document.querySelector('input[placeholder*="收件人"]') ||
                             document.querySelectorAll('input[type="text"]')[0];
                return toField;
            """)

            if to_field:
                driver.execute_script("""
                    arguments[0].focus();
                    arguments[0].value = arguments[1];
                    arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                    arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                    arguments[0].dispatchEvent(new Event('blur', {bubbles: true}));
                """, to_field, to_email)
                return True
            return False
        except Exception as e:
            logger.error(f"第2步异常: {e}")
            return False

    def _step3_fill_subject(self, subject: str) -> bool:
        """第3步：填写主题 - 复刻成功逻辑"""
        try:
            # 复刻成功的主题填写逻辑
            driver = self.ultra_fast_sender.driver

            # 使用成功验证的完整选择器逻辑
            subject_field = driver.execute_script("""
                var subjectField =
                    // 最精确的选择器：同时匹配name和class
                    document.querySelector('input[name="subj"][class="input inp_base"]') ||
                    document.querySelector('input[name="subj"][class*="inp_base"]') ||
                    // 基于可见性过滤的选择器
                    (function() {
                        var subjElements = document.querySelectorAll('input[name="subj"]');
                        for (var i = 0; i < subjElements.length; i++) {
                            if (subjElements[i].offsetParent !== null &&
                                subjElements[i].className.includes('inp_base')) {
                                return subjElements[i];
                            }
                        }
                        return null;
                    })() ||
                    // 备用选择器
                    document.querySelector('input[name="subj"]') ||
                    document.querySelector('input[class*="inp_base"]') ||
                    document.querySelector('input[name="subject"]') ||
                    document.querySelector('input[name="title"]') ||
                    document.querySelector('input[name*="subject"]') ||
                    document.querySelector('input[placeholder*="主题"]');
                return subjectField;
            """)

            if subject_field:
                driver.execute_script("""
                    arguments[0].focus();
                    arguments[0].value = arguments[1];
                    arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                    arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                """, subject_field, subject)
                return True
            return False
        except Exception as e:
            logger.error(f"第3步异常: {e}")
            return False

    def _step4_fill_content(self, content: str) -> bool:
        """第4步：填写内容 - 复刻成功逻辑"""
        try:
            # 复刻成功的内容填写逻辑
            driver = self.ultra_fast_sender.driver

            # 方法1：iframe编辑器 - 完整复刻原始逻辑
            try:
                iframe = driver.execute_script("""
                    return document.querySelector('iframe[class="iframe"]') ||
                           document.querySelector('iframe.iframe') ||
                           document.querySelector('iframe[name="content"]') ||
                           document.querySelector('iframe[id="content"]') ||
                           document.querySelector('iframe[name*="editor"]') ||
                           document.querySelector('iframe[id*="editor"]') ||
                           document.querySelector('iframe');
                """)

                if iframe:
                    driver.switch_to.frame(iframe)
                    body = driver.execute_script("return document.body;")
                    if body:
                        driver.execute_script("""
                            arguments[0].innerHTML = arguments[1];
                            arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                        """, body, content)
                        driver.switch_to.default_content()
                        return True
                    driver.switch_to.default_content()
            except:
                driver.switch_to.default_content()

            # 方法2：富文本编辑器
            try:
                content_div = driver.execute_script("""
                    return document.querySelector('div[contenteditable="true"]') ||
                           document.querySelector('div[contenteditable=""]');
                """)

                if content_div:
                    driver.execute_script("""
                        arguments[0].focus();
                        arguments[0].innerHTML = arguments[1];
                        arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                    """, content_div, content)
                    return True
            except:
                pass

            # 方法3：textarea
            try:
                textarea = driver.execute_script("""
                    return document.querySelector('textarea[name="content"]') ||
                           document.querySelector('textarea[name*="content"]') ||
                           document.querySelector('textarea[name*="body"]') ||
                           document.querySelector('textarea');
                """)

                if textarea:
                    driver.execute_script("""
                        arguments[0].focus();
                        arguments[0].value = arguments[1];
                        arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                    """, textarea, content)
                    return True
            except:
                pass

            return False
        except Exception as e:
            logger.error(f"第4步异常: {e}")
            return False

    def _step5_click_send(self) -> bool:
        """第5步：点击发送 - 复刻成功逻辑"""
        try:
            # 复刻成功的发送按钮点击逻辑
            driver = self.ultra_fast_sender.driver

            # 使用成功验证的完整选择器
            send_button = driver.execute_script("""
                var sendButton = document.querySelector('input[type="submit"][value="发送"]') ||
                               document.querySelector('input[value="发送"]') ||
                               document.querySelector('input[type="submit"][value*="发送"]') ||
                               document.querySelector('button[text()="发送"]') ||
                               document.querySelector('button:contains("发送")') ||
                               document.querySelector('button[contains(text(), "发送")]') ||
                               document.querySelector('input[type="submit"]') ||
                               document.querySelector('button[type="submit"]');
                return sendButton;
            """)

            if send_button:
                driver.execute_script("""
                    arguments[0].focus();
                    arguments[0].click();
                """, send_button)

                # 检查发送成功
                import time
                time.sleep(0.3)  # 等待0.3秒检查结果

                success_text = driver.execute_script("""
                    return document.body.innerText.includes('您的邮件已发送') ||
                           document.body.innerText.includes('发送成功') ||
                           document.body.innerText.includes('邮件已发送');
                """)

                return success_text
            return False
        except Exception as e:
            logger.error(f"第5步异常: {e}")
            return False
    
    def _send_standard(self, to_email: str, subject: str, content: str) -> SendingResult:
        """标准发送 - 第二步策略：使用元素操作发送"""
        try:
            logger.info("🔄 第二步策略：标准发送（元素操作模式）")
            logger.info("📝 流程：点击写信 → 元素定位填写 → 发送")

            if not self.ultra_fast_sender:
                return SendingResult(False, "发送器未初始化")

            # 第二步策略：使用元素操作发送（相对较慢但更稳定）
            success = self.ultra_fast_sender.send_email_ultra_fast(to_email, subject, content)

            if success:
                logger.info("✅ 第二步策略成功：标准发送完成")
                return SendingResult(True, "标准发送成功（元素操作模式）")
            else:
                logger.warning("⚠️ 第二步策略失败，将尝试第三步策略")
                return SendingResult(False, "标准发送失败")

        except Exception as e:
            logger.error(f"❌ 第二步策略异常: {e}")
            return SendingResult(False, f"标准发送异常: {e}")
    
    def _send_safe(self, to_email: str, subject: str, content: str) -> SendingResult:
        """安全发送 - 第三步策略：慢但稳定的发送方式"""
        try:
            logger.info("🛡️ 第三步策略：安全发送（慢但稳定）")
            logger.info("📝 流程：点击写信 → 等待稳定 → 谨慎填写 → 确认发送")

            if not self.ultra_fast_sender:
                return SendingResult(False, "发送器未初始化")

            # 第三步策略：安全发送逻辑 - 添加更多等待和检查
            logger.info("⏳ 安全模式：额外等待确保页面稳定...")
            time.sleep(2)  # 额外等待确保稳定性

            # 使用相同的核心发送逻辑，但在安全模式下运行
            success = self.ultra_fast_sender.send_email_ultra_fast(to_email, subject, content)

            if success:
                logger.info("✅ 第三步策略成功：安全发送完成")
                return SendingResult(True, "安全发送成功（慢但稳定模式）")
            else:
                logger.error("❌ 所有策略都失败了")
                return SendingResult(False, "安全发送失败")

        except Exception as e:
            logger.error(f"❌ 第三步策略异常: {e}")
            return SendingResult(False, f"安全发送异常: {e}")
    
    def _update_stats(self, result: SendingResult):
        """更新统计信息"""
        self.stats['total_sent'] += 1
        
        if result.success:
            self.stats['success_count'] += 1
        else:
            self.stats['failed_count'] += 1
        
        # 更新平均响应时间
        if self.stats['total_sent'] > 0:
            total_time = self.stats['avg_response_time'] * (self.stats['total_sent'] - 1) + result.response_time
            self.stats['avg_response_time'] = total_time / self.stats['total_sent']
        
        # 更新策略使用统计
        strategy = result.strategy_used
        if strategy not in self.stats['strategy_usage']:
            self.stats['strategy_usage'][strategy] = 0
        self.stats['strategy_usage'][strategy] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        success_rate = 0.0
        if self.stats['total_sent'] > 0:
            success_rate = (self.stats['success_count'] / self.stats['total_sent']) * 100
        
        return {
            **self.stats,
            'success_rate': round(success_rate, 2)
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_sent': 0,
            'success_count': 0,
            'failed_count': 0,
            'avg_response_time': 0.0,
            'strategy_usage': {}
        }
        logger.info("📊 统计信息已重置")
    
    def change_strategy(self, new_strategy: SendingStrategy):
        """更改发送策略"""
        old_strategy = self.strategy
        self.strategy = new_strategy
        logger.info(f"🔄 发送策略已更改: {old_strategy.value} → {new_strategy.value}")
    
    def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self, 'ultra_fast_sender') and self.ultra_fast_sender:
                # 清理超高速发送器资源
                pass
            
            logger.info("🧹 统一发送器资源清理完成")
        except Exception as e:
            logger.error(f"❌ 资源清理失败: {e}")
