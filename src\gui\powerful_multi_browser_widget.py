#!/usr/bin/env python3
"""
强大的多浏览器发送控件
真正的多账号并发发送，支持智能任务分发、实时监控、账号切换
"""

import time
from typing import List, Dict, Any, Optional, Tuple
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QGroupBox, QLabel, QPushButton, QTextEdit, QTableWidget,
    QTableWidgetItem, QProgressBar, QSpinBox, QCheckBox,
    QTabWidget, QSplitter, QFrame, QScrollArea, QComboBox,
    QMessageBox, QHeaderView, QSlider
)
from PyQt5.QtCore import QTimer, Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette

from src.utils.logger import setup_logger
from src.models.account import Account
from src.core.integrated_email_sender import IntegratedEmailSender, IntegratedSenderConfig
from src.core.sending_monitor import SendingMonitor, AccountStatus

logger = setup_logger("INFO")

class BrowserStatusCard(QFrame):
    """浏览器状态卡片"""
    
    def __init__(self, browser_id: str):
        super().__init__()
        self.browser_id = browser_id
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setFrameStyle(QFrame.Box)
        self.setLineWidth(2)
        self.setMaximumHeight(200)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(5)
        
        # 浏览器ID标题
        title_label = QLabel(f"🌐 {self.browser_id}")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(12)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 状态信息网格
        info_layout = QGridLayout()
        
        # 状态
        self.status_label = QLabel("状态: 未知")
        info_layout.addWidget(QLabel("状态:"), 0, 0)
        info_layout.addWidget(self.status_label, 0, 1)
        
        # 当前账号
        self.account_label = QLabel("账号: 无")
        info_layout.addWidget(QLabel("账号:"), 1, 0)
        info_layout.addWidget(self.account_label, 1, 1)
        
        # 已发送
        self.sent_label = QLabel("已发送: 0")
        info_layout.addWidget(QLabel("已发送:"), 2, 0)
        info_layout.addWidget(self.sent_label, 2, 1)
        
        # 队列大小
        self.queue_label = QLabel("队列: 0")
        info_layout.addWidget(QLabel("队列:"), 3, 0)
        info_layout.addWidget(self.queue_label, 3, 1)
        
        # 成功率
        self.success_rate_label = QLabel("成功率: 100%")
        info_layout.addWidget(QLabel("成功率:"), 4, 0)
        info_layout.addWidget(self.success_rate_label, 4, 1)
        
        layout.addLayout(info_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximum(100)
        layout.addWidget(self.progress_bar)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        self.switch_btn = QPushButton("🔄 切换账号")
        self.switch_btn.setMaximumHeight(30)
        button_layout.addWidget(self.switch_btn)
        layout.addLayout(button_layout)
    
    def update_status(self, status_data: Dict[str, Any]):
        """更新状态显示"""
        # 更新状态
        status = status_data.get('status', 'unknown')
        status_icons = {
            'idle': '🟢 空闲',
            'busy': '🟡 忙碌',
            'error': '🔴 错误',
            'switching': '🔄 切换中'
        }
        status_text = status_icons.get(status, f'❓ {status}')
        self.status_label.setText(status_text)
        
        # 更新账号
        current_account = status_data.get('current_account', '无')
        self.account_label.setText(current_account)
        
        # 更新发送数量
        total_completed = status_data.get('total_tasks_completed', 0)
        self.sent_label.setText(f"{total_completed}")
        
        # 更新队列
        queue_size = status_data.get('current_queue_size', 0)
        self.queue_label.setText(f"{queue_size}")
        
        # 更新成功率
        total_assigned = status_data.get('total_tasks_assigned', 0)
        total_failed = status_data.get('total_tasks_failed', 0)
        if total_assigned > 0:
            success_rate = ((total_assigned - total_failed) / total_assigned) * 100
            self.success_rate_label.setText(f"{success_rate:.1f}%")
        
        # 更新进度条
        if total_assigned > 0:
            progress = (total_completed / total_assigned) * 100
            self.progress_bar.setValue(int(progress))
        
        # 根据状态设置颜色
        if status == 'error':
            self.setStyleSheet("QFrame { border: 2px solid red; }")
        elif status == 'busy':
            self.setStyleSheet("QFrame { border: 2px solid orange; }")
        elif status == 'idle':
            self.setStyleSheet("QFrame { border: 2px solid green; }")
        else:
            self.setStyleSheet("QFrame { border: 2px solid gray; }")

class PowerfulMultiBrowserWidget(QWidget):
    """强大的多浏览器发送控件"""
    
    # 信号定义
    status_updated = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 核心组件
        self.email_sender: Optional[IntegratedEmailSender] = None
        self.sending_monitor: Optional[SendingMonitor] = None
        self.accounts: List[Account] = []
        
        # UI组件
        self.browser_cards: Dict[str, BrowserStatusCard] = {}
        
        # 定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        
        self.setup_ui()
        logger.info("🎨 强大的多浏览器发送控件初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 控制面板标签页
        control_tab = self.create_control_tab()
        tab_widget.addTab(control_tab, "🎛️ 控制面板")
        
        # 浏览器监控标签页
        monitor_tab = self.create_monitor_tab()
        tab_widget.addTab(monitor_tab, "🖥️ 浏览器监控")
        
        # 账号状态标签页
        account_tab = self.create_account_tab()
        tab_widget.addTab(account_tab, "👤 账号状态")
        
        # 任务管理标签页
        task_tab = self.create_task_tab()
        tab_widget.addTab(task_tab, "📋 任务管理")
        
        # 统计分析标签页
        stats_tab = self.create_stats_tab()
        tab_widget.addTab(stats_tab, "📊 统计分析")
        
        layout.addWidget(tab_widget)
        
        # 底部状态栏
        self.create_status_bar(layout)
    
    def create_control_tab(self) -> QWidget:
        """创建控制面板标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 系统配置组
        config_group = QGroupBox("⚙️ 系统配置")
        config_layout = QGridLayout(config_group)
        
        # 浏览器数量
        config_layout.addWidget(QLabel("浏览器数量:"), 0, 0)
        self.browser_count_spin = QSpinBox()
        self.browser_count_spin.setRange(1, 10)
        self.browser_count_spin.setValue(3)
        config_layout.addWidget(self.browser_count_spin, 0, 1)
        
        # 每账号邮件数
        config_layout.addWidget(QLabel("每账号邮件数:"), 0, 2)
        self.emails_per_account_spin = QSpinBox()
        self.emails_per_account_spin.setRange(1, 100)
        self.emails_per_account_spin.setValue(5)
        config_layout.addWidget(self.emails_per_account_spin, 0, 3)
        
        # 发送间隔
        config_layout.addWidget(QLabel("发送间隔(秒):"), 1, 0)
        self.send_interval_spin = QSpinBox()
        self.send_interval_spin.setRange(1, 60)
        self.send_interval_spin.setValue(2)
        config_layout.addWidget(self.send_interval_spin, 1, 1)
        
        # 切换冷却时间
        config_layout.addWidget(QLabel("切换冷却(秒):"), 1, 2)
        self.switch_cooldown_spin = QSpinBox()
        self.switch_cooldown_spin.setRange(1, 30)
        self.switch_cooldown_spin.setValue(3)
        config_layout.addWidget(self.switch_cooldown_spin, 1, 3)
        
        # 功能开关
        switch_layout = QHBoxLayout()
        self.load_balancing_check = QCheckBox("负载均衡")
        self.load_balancing_check.setChecked(True)
        switch_layout.addWidget(self.load_balancing_check)
        
        self.auto_retry_check = QCheckBox("自动重试")
        self.auto_retry_check.setChecked(True)
        switch_layout.addWidget(self.auto_retry_check)
        
        self.minimize_browsers_check = QCheckBox("最小化浏览器")
        self.minimize_browsers_check.setChecked(True)
        switch_layout.addWidget(self.minimize_browsers_check)
        
        config_layout.addLayout(switch_layout, 2, 0, 1, 4)
        
        layout.addWidget(config_group)
        
        # 系统控制组
        control_group = QGroupBox("🎮 系统控制")
        control_layout = QHBoxLayout(control_group)
        
        self.init_btn = QPushButton("🔧 初始化系统")
        self.start_btn = QPushButton("🚀 启动发送")
        self.stop_btn = QPushButton("🛑 停止发送")
        self.reset_btn = QPushButton("🔄 重置系统")
        
        self.init_btn.clicked.connect(self.initialize_system)
        self.start_btn.clicked.connect(self.start_sending)
        self.stop_btn.clicked.connect(self.stop_sending)
        self.reset_btn.clicked.connect(self.reset_system)
        
        # 初始状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)
        
        control_layout.addWidget(self.init_btn)
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.reset_btn)
        
        layout.addWidget(control_group)
        
        # 快速任务添加组
        task_group = QGroupBox("📝 快速添加任务")
        task_layout = QGridLayout(task_group)
        
        task_layout.addWidget(QLabel("收件人:"), 0, 0)
        self.quick_to_edit = QTextEdit()
        self.quick_to_edit.setMaximumHeight(80)
        self.quick_to_edit.setPlaceholderText("输入邮箱地址，每行一个")
        task_layout.addWidget(self.quick_to_edit, 0, 1)
        
        task_layout.addWidget(QLabel("主题:"), 1, 0)
        self.quick_subject_edit = QTextEdit()
        self.quick_subject_edit.setMaximumHeight(40)
        task_layout.addWidget(self.quick_subject_edit, 1, 1)
        
        task_layout.addWidget(QLabel("内容:"), 2, 0)
        self.quick_content_edit = QTextEdit()
        self.quick_content_edit.setMaximumHeight(100)
        task_layout.addWidget(self.quick_content_edit, 2, 1)
        
        button_layout = QHBoxLayout()
        self.add_task_btn = QPushButton("➕ 添加任务")
        self.add_task_btn.clicked.connect(self.add_quick_tasks)
        self.add_task_btn.setEnabled(False)
        
        self.clear_tasks_btn = QPushButton("🗑️ 清空输入")
        self.clear_tasks_btn.clicked.connect(self.clear_task_inputs)
        
        button_layout.addWidget(self.add_task_btn)
        button_layout.addWidget(self.clear_tasks_btn)
        task_layout.addLayout(button_layout, 3, 1)
        
        layout.addWidget(task_group)
        
        return tab
    
    def create_monitor_tab(self) -> QWidget:
        """创建浏览器监控标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 系统概览
        overview_group = QGroupBox("📊 系统概览")
        overview_layout = QGridLayout(overview_group)
        
        self.total_browsers_label = QLabel("总浏览器: 0")
        self.active_browsers_label = QLabel("活跃浏览器: 0")
        self.total_accounts_label = QLabel("总账号: 0")
        self.active_accounts_label = QLabel("活跃账号: 0")
        self.system_status_label = QLabel("系统状态: 未初始化")
        self.utilization_label = QLabel("系统利用率: 0%")
        
        overview_layout.addWidget(self.total_browsers_label, 0, 0)
        overview_layout.addWidget(self.active_browsers_label, 0, 1)
        overview_layout.addWidget(self.total_accounts_label, 0, 2)
        overview_layout.addWidget(self.active_accounts_label, 1, 0)
        overview_layout.addWidget(self.system_status_label, 1, 1)
        overview_layout.addWidget(self.utilization_label, 1, 2)
        
        layout.addWidget(overview_group)
        
        # 浏览器状态卡片区域
        browser_group = QGroupBox("🖥️ 浏览器状态")
        browser_layout = QVBoxLayout(browser_group)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.browser_grid_layout = QGridLayout(scroll_widget)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        
        browser_layout.addWidget(scroll_area)
        layout.addWidget(browser_group)

        return tab

    def create_account_tab(self) -> QWidget:
        """创建账号状态标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 账号统计概览
        account_overview_group = QGroupBox("👤 账号统计概览")
        account_overview_layout = QGridLayout(account_overview_group)

        self.total_sent_label = QLabel("总发送: 0")
        self.total_failed_label = QLabel("总失败: 0")
        self.overall_success_rate_label = QLabel("整体成功率: 100%")
        self.emails_per_minute_label = QLabel("每分钟: 0")
        self.total_switches_label = QLabel("总切换: 0")
        self.switch_success_rate_label = QLabel("切换成功率: 100%")

        account_overview_layout.addWidget(self.total_sent_label, 0, 0)
        account_overview_layout.addWidget(self.total_failed_label, 0, 1)
        account_overview_layout.addWidget(self.overall_success_rate_label, 0, 2)
        account_overview_layout.addWidget(self.emails_per_minute_label, 1, 0)
        account_overview_layout.addWidget(self.total_switches_label, 1, 1)
        account_overview_layout.addWidget(self.switch_success_rate_label, 1, 2)

        layout.addWidget(account_overview_group)

        # 账号详情表格
        account_detail_group = QGroupBox("📋 账号详情")
        account_detail_layout = QVBoxLayout(account_detail_group)

        self.account_table = QTableWidget()
        self.account_table.setColumnCount(8)
        self.account_table.setHorizontalHeaderLabels([
            "账号", "浏览器", "状态", "已发送", "失败", "成功率", "每分钟", "连续错误"
        ])
        self.account_table.horizontalHeader().setStretchLastSection(True)

        account_detail_layout.addWidget(self.account_table)
        layout.addWidget(account_detail_group)

        return tab

    def create_task_tab(self) -> QWidget:
        """创建任务管理标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 任务统计
        task_stats_group = QGroupBox("📊 任务统计")
        task_stats_layout = QGridLayout(task_stats_group)

        self.total_tasks_label = QLabel("总任务: 0")
        self.pending_tasks_label = QLabel("待分配: 0")
        self.assigned_tasks_label = QLabel("已分配: 0")
        self.sending_tasks_label = QLabel("发送中: 0")
        self.completed_tasks_label = QLabel("已完成: 0")
        self.failed_tasks_label = QLabel("失败: 0")

        task_stats_layout.addWidget(self.total_tasks_label, 0, 0)
        task_stats_layout.addWidget(self.pending_tasks_label, 0, 1)
        task_stats_layout.addWidget(self.assigned_tasks_label, 0, 2)
        task_stats_layout.addWidget(self.sending_tasks_label, 1, 0)
        task_stats_layout.addWidget(self.completed_tasks_label, 1, 1)
        task_stats_layout.addWidget(self.failed_tasks_label, 1, 2)

        layout.addWidget(task_stats_group)

        # 任务控制
        task_control_group = QGroupBox("🎮 任务控制")
        task_control_layout = QHBoxLayout(task_control_group)

        self.pause_tasks_btn = QPushButton("⏸️ 暂停任务")
        self.resume_tasks_btn = QPushButton("▶️ 恢复任务")
        self.clear_queue_btn = QPushButton("🗑️ 清空队列")
        self.force_switch_all_btn = QPushButton("🔄 强制全部切换")

        task_control_layout.addWidget(self.pause_tasks_btn)
        task_control_layout.addWidget(self.resume_tasks_btn)
        task_control_layout.addWidget(self.clear_queue_btn)
        task_control_layout.addWidget(self.force_switch_all_btn)

        layout.addWidget(task_control_group)

        return tab

    def create_stats_tab(self) -> QWidget:
        """创建统计分析标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 性能指标
        performance_group = QGroupBox("📈 性能指标")
        performance_layout = QGridLayout(performance_group)

        self.runtime_label = QLabel("运行时间: 00:00:00")
        self.avg_task_time_label = QLabel("平均任务时间: 0s")
        self.peak_emails_per_minute_label = QLabel("峰值速度: 0/分钟")
        self.system_efficiency_label = QLabel("系统效率: 0%")

        performance_layout.addWidget(self.runtime_label, 0, 0)
        performance_layout.addWidget(self.avg_task_time_label, 0, 1)
        performance_layout.addWidget(self.peak_emails_per_minute_label, 1, 0)
        performance_layout.addWidget(self.system_efficiency_label, 1, 1)

        layout.addWidget(performance_group)

        # 错误分析
        error_group = QGroupBox("🚨 错误分析")
        error_layout = QVBoxLayout(error_group)

        self.error_log = QTextEdit()
        self.error_log.setMaximumHeight(150)
        self.error_log.setReadOnly(True)
        error_layout.addWidget(self.error_log)

        layout.addWidget(error_group)

        return tab

    def create_status_bar(self, layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_layout = QHBoxLayout(status_frame)

        self.runtime_status_label = QLabel("运行时间: 00:00:00")
        self.performance_status_label = QLabel("性能: 0 邮件/分钟")
        self.health_status_label = QLabel("系统健康: 🔴")
        self.connection_status_label = QLabel("连接状态: 🔴 未连接")

        status_layout.addWidget(self.runtime_status_label)
        status_layout.addWidget(self.performance_status_label)
        status_layout.addWidget(self.health_status_label)
        status_layout.addWidget(self.connection_status_label)
        status_layout.addStretch()

        layout.addWidget(status_frame)

    def set_accounts(self, accounts: List[Account]):
        """设置账号列表"""
        self.accounts = accounts
        logger.info(f"📋 设置账号列表，数量: {len(accounts)}")

    def initialize_system(self):
        """初始化系统"""
        try:
            if not self.accounts:
                QMessageBox.warning(self, "警告", "请先设置账号列表！")
                return

            logger.info("🔧 开始初始化强大的多浏览器发送系统...")

            # 创建配置
            config = IntegratedSenderConfig(
                max_browsers=self.browser_count_spin.value(),
                emails_per_account=self.emails_per_account_spin.value(),
                send_interval=self.send_interval_spin.value(),
                switch_cooldown=self.switch_cooldown_spin.value(),
                minimize_browsers=self.minimize_browsers_check.isChecked(),
                enable_load_balancing=self.load_balancing_check.isChecked(),
                enable_auto_retry=self.auto_retry_check.isChecked()
            )

            # 创建集成邮件发送器
            self.email_sender = IntegratedEmailSender(config)

            # 创建发送监控器
            self.sending_monitor = SendingMonitor()

            # 设置回调
            self.email_sender.add_status_callback(self._on_sender_status_changed)
            self.sending_monitor.add_alert_callback(self._on_monitor_alert)
            self.sending_monitor.add_stats_callback(self._on_stats_updated)

            # 初始化系统
            if self.email_sender.initialize(self.accounts):
                # 初始化监控器
                account_emails = [acc.email for acc in self.accounts]
                browser_ids = [f"browser_{i+1}" for i in range(config.max_browsers)]

                self.sending_monitor.initialize_accounts(account_emails)
                self.sending_monitor.initialize_browsers(browser_ids)
                self.sending_monitor.start_monitoring()

                logger.info("✅ 系统初始化成功")

                # 更新UI状态
                self.init_btn.setEnabled(False)
                self.start_btn.setEnabled(True)
                self.add_task_btn.setEnabled(True)

                # 创建浏览器状态卡片
                self.create_browser_cards()

                # 启动更新定时器
                self.update_timer.start(1000)  # 每秒更新

                QMessageBox.information(self, "成功", "强大的多浏览器发送系统初始化成功！")
            else:
                logger.error("❌ 系统初始化失败")
                QMessageBox.critical(self, "错误", "系统初始化失败！")

        except Exception as e:
            logger.error(f"❌ 初始化系统异常: {e}")
            QMessageBox.critical(self, "错误", f"初始化失败: {str(e)}")

    def start_sending(self):
        """启动发送"""
        try:
            if not self.email_sender:
                QMessageBox.warning(self, "警告", "请先初始化系统！")
                return

            if self.email_sender.start_sending():
                logger.info("🚀 发送系统启动成功")

                # 更新UI状态
                self.start_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)

                QMessageBox.information(self, "成功", "强大的多浏览器发送系统启动成功！")
            else:
                QMessageBox.critical(self, "错误", "发送系统启动失败！")

        except Exception as e:
            logger.error(f"❌ 启动发送异常: {e}")
            QMessageBox.critical(self, "错误", f"启动失败: {str(e)}")

    def stop_sending(self):
        """停止发送"""
        try:
            if self.email_sender:
                self.email_sender.stop_sending()
                logger.info("🛑 发送系统已停止")

                # 更新UI状态
                self.start_btn.setEnabled(True)
                self.stop_btn.setEnabled(False)

                QMessageBox.information(self, "成功", "发送系统已停止！")

        except Exception as e:
            logger.error(f"❌ 停止发送异常: {e}")
            QMessageBox.critical(self, "错误", f"停止失败: {str(e)}")

    def reset_system(self):
        """重置系统"""
        try:
            # 停止定时器
            self.update_timer.stop()

            # 清理发送器
            if self.email_sender:
                self.email_sender.cleanup()
                self.email_sender = None

            # 清理监控器
            if self.sending_monitor:
                self.sending_monitor.stop_monitoring()
                self.sending_monitor = None

            # 清理浏览器卡片
            self.clear_browser_cards()

            # 重置UI状态
            self.init_btn.setEnabled(True)
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)
            self.add_task_btn.setEnabled(False)

            # 重置显示
            self.reset_display()

            logger.info("🔄 系统重置完成")
            QMessageBox.information(self, "成功", "系统重置完成！")

        except Exception as e:
            logger.error(f"❌ 重置系统异常: {e}")
            QMessageBox.critical(self, "错误", f"重置失败: {str(e)}")

    def add_quick_tasks(self):
        """添加快速任务"""
        try:
            if not self.email_sender or not self.email_sender.is_running:
                QMessageBox.warning(self, "警告", "请先启动发送系统！")
                return

            # 获取输入内容
            to_emails = self.quick_to_edit.toPlainText().strip().split('\n')
            subject = self.quick_subject_edit.toPlainText().strip()
            content = self.quick_content_edit.toPlainText().strip()

            if not to_emails or not subject or not content:
                QMessageBox.warning(self, "警告", "请填写完整的邮件信息！")
                return

            # 过滤空行
            to_emails = [email.strip() for email in to_emails if email.strip()]

            if not to_emails:
                QMessageBox.warning(self, "警告", "请输入有效的邮箱地址！")
                return

            # 添加任务
            email_list = [(email, subject, content) for email in to_emails]
            task_ids = self.email_sender.add_batch_tasks(email_list)

            logger.info(f"📝 添加了 {len(task_ids)} 个任务")
            QMessageBox.information(self, "成功", f"成功添加 {len(task_ids)} 个邮件任务！")

        except Exception as e:
            logger.error(f"❌ 添加任务异常: {e}")
            QMessageBox.critical(self, "错误", f"添加任务失败: {str(e)}")

    def clear_task_inputs(self):
        """清空任务输入"""
        self.quick_to_edit.clear()
        self.quick_subject_edit.clear()
        self.quick_content_edit.clear()

    def create_browser_cards(self):
        """创建浏览器状态卡片"""
        if not self.email_sender:
            return

        # 清理现有卡片
        self.clear_browser_cards()

        # 获取浏览器状态
        browser_status = self.email_sender.get_browser_status()

        # 创建新卡片
        row = 0
        col = 0
        max_cols = 3  # 每行最多3个浏览器

        for browser_id in browser_status.keys():
            card = BrowserStatusCard(browser_id)

            # 连接切换按钮
            card.switch_btn.clicked.connect(
                lambda checked=False, bid=browser_id: self.force_switch_account(bid)
            )

            self.browser_cards[browser_id] = card
            self.browser_grid_layout.addWidget(card, row, col)

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        logger.info(f"🎨 创建了 {len(self.browser_cards)} 个浏览器状态卡片")

    def clear_browser_cards(self):
        """清理浏览器卡片"""
        for card in self.browser_cards.values():
            card.deleteLater()
        self.browser_cards.clear()

    def force_switch_account(self, browser_id: str):
        """强制切换账号"""
        try:
            if self.email_sender:
                success = self.email_sender.force_account_switch(browser_id)
                if success:
                    QMessageBox.information(self, "成功", f"浏览器 {browser_id} 账号切换成功！")
                else:
                    QMessageBox.warning(self, "失败", f"浏览器 {browser_id} 账号切换失败！")
        except Exception as e:
            logger.error(f"❌ 强制切换账号异常: {e}")
            QMessageBox.critical(self, "错误", f"切换失败: {str(e)}")

    def update_display(self):
        """更新显示"""
        try:
            if not self.email_sender or not self.sending_monitor:
                return

            # 获取综合统计
            sender_stats = self.email_sender.get_comprehensive_stats()
            monitor_stats = self.sending_monitor.get_comprehensive_stats()

            # 更新系统概览
            self.update_system_overview(sender_stats, monitor_stats)

            # 更新浏览器状态
            self.update_browser_status(sender_stats, monitor_stats)

            # 更新账号状态
            self.update_account_status(monitor_stats)

            # 更新任务统计
            self.update_task_statistics(sender_stats)

            # 更新性能指标
            self.update_performance_metrics(sender_stats, monitor_stats)

            # 更新状态栏
            self.update_status_bar(sender_stats, monitor_stats)

        except Exception as e:
            logger.error(f"❌ 更新显示异常: {e}")

    def update_system_overview(self, sender_stats: Dict[str, Any], monitor_stats: Dict[str, Any]):
        """更新系统概览"""
        system_metrics = monitor_stats.get('system_metrics', {})

        self.total_browsers_label.setText(f"总浏览器: {system_metrics.get('total_browsers', 0)}")
        self.active_browsers_label.setText(f"活跃浏览器: {system_metrics.get('active_browsers', 0)}")
        self.total_accounts_label.setText(f"总账号: {system_metrics.get('total_accounts', 0)}")
        self.active_accounts_label.setText(f"活跃账号: {system_metrics.get('active_accounts', 0)}")

        is_running = sender_stats.get('system_status', {}).get('is_running', False)
        status_text = "🟢 运行中" if is_running else "🔴 已停止"
        self.system_status_label.setText(f"系统状态: {status_text}")

        utilization = system_metrics.get('system_utilization', 0)
        self.utilization_label.setText(f"系统利用率: {utilization:.1f}%")

    def update_browser_status(self, sender_stats: Dict[str, Any], monitor_stats: Dict[str, Any]):
        """更新浏览器状态"""
        browser_metrics = monitor_stats.get('browser_metrics', {})

        for browser_id, card in self.browser_cards.items():
            if browser_id in browser_metrics:
                card.update_status(browser_metrics[browser_id])

    def update_account_status(self, monitor_stats: Dict[str, Any]):
        """更新账号状态"""
        account_metrics = monitor_stats.get('account_metrics', {})
        system_metrics = monitor_stats.get('system_metrics', {})

        # 更新概览统计
        total_sent = sum(metrics.get('total_sent', 0) for metrics in account_metrics.values())
        total_failed = sum(metrics.get('total_failed', 0) for metrics in account_metrics.values())

        self.total_sent_label.setText(f"总发送: {total_sent}")
        self.total_failed_label.setText(f"总失败: {total_failed}")
        self.overall_success_rate_label.setText(f"整体成功率: {system_metrics.get('overall_success_rate', 100):.1f}%")
        self.emails_per_minute_label.setText(f"每分钟: {system_metrics.get('overall_emails_per_minute', 0):.1f}")
        self.total_switches_label.setText(f"总切换: {system_metrics.get('total_account_switches', 0)}")

        switch_success_rate = 100.0
        if system_metrics.get('total_account_switches', 0) > 0:
            switch_success_rate = (system_metrics.get('successful_account_switches', 0) / system_metrics.get('total_account_switches', 1)) * 100
        self.switch_success_rate_label.setText(f"切换成功率: {switch_success_rate:.1f}%")

        # 更新账号详情表格
        self.update_account_table(account_metrics)

    def update_account_table(self, account_metrics: Dict[str, Any]):
        """更新账号详情表格"""
        self.account_table.setRowCount(len(account_metrics))

        for row, (email, metrics) in enumerate(account_metrics.items()):
            self.account_table.setItem(row, 0, QTableWidgetItem(email))
            self.account_table.setItem(row, 1, QTableWidgetItem(metrics.get('browser_id', '无')))

            status = metrics.get('status', 'idle')
            status_icons = {
                'idle': '🟢 空闲',
                'sending': '🟡 发送中',
                'switching': '🔄 切换中',
                'error': '🔴 错误',
                'disabled': '⚫ 禁用'
            }
            status_text = status_icons.get(status, f'❓ {status}')
            self.account_table.setItem(row, 2, QTableWidgetItem(status_text))

            self.account_table.setItem(row, 3, QTableWidgetItem(str(metrics.get('total_sent', 0))))
            self.account_table.setItem(row, 4, QTableWidgetItem(str(metrics.get('total_failed', 0))))
            self.account_table.setItem(row, 5, QTableWidgetItem(f"{metrics.get('success_rate', 100):.1f}%"))
            self.account_table.setItem(row, 6, QTableWidgetItem(f"{metrics.get('emails_per_minute', 0):.1f}"))
            self.account_table.setItem(row, 7, QTableWidgetItem(str(metrics.get('consecutive_errors', 0))))

    def update_task_statistics(self, sender_stats: Dict[str, Any]):
        """更新任务统计"""
        distributor_stats = sender_stats.get('distributor_stats', {})
        task_stats = distributor_stats.get('task_stats', {})

        self.total_tasks_label.setText(f"总任务: {task_stats.get('total_tasks', 0)}")
        self.pending_tasks_label.setText(f"待分配: {task_stats.get('pending_tasks', 0)}")
        self.assigned_tasks_label.setText(f"已分配: {task_stats.get('assigned_tasks', 0)}")
        self.sending_tasks_label.setText(f"发送中: {task_stats.get('sending_tasks', 0)}")
        self.completed_tasks_label.setText(f"已完成: {task_stats.get('completed_tasks', 0)}")
        self.failed_tasks_label.setText(f"失败: {task_stats.get('failed_tasks', 0)}")

    def update_performance_metrics(self, sender_stats: Dict[str, Any], monitor_stats: Dict[str, Any]):
        """更新性能指标"""
        system_metrics = monitor_stats.get('system_metrics', {})

        # 运行时间
        runtime = system_metrics.get('runtime_seconds', 0)
        hours = int(runtime // 3600)
        minutes = int((runtime % 3600) // 60)
        seconds = int(runtime % 60)
        self.runtime_label.setText(f"运行时间: {hours:02d}:{minutes:02d}:{seconds:02d}")

        # 平均任务时间
        browser_metrics = monitor_stats.get('browser_metrics', {})
        avg_times = [metrics.get('average_task_time', 0) for metrics in browser_metrics.values()]
        avg_task_time = sum(avg_times) / len(avg_times) if avg_times else 0
        self.avg_task_time_label.setText(f"平均任务时间: {avg_task_time:.1f}s")

        # 峰值速度
        peak_speed = system_metrics.get('overall_emails_per_minute', 0)
        self.peak_emails_per_minute_label.setText(f"峰值速度: {peak_speed:.1f}/分钟")

        # 系统效率
        efficiency = system_metrics.get('system_utilization', 0)
        self.system_efficiency_label.setText(f"系统效率: {efficiency:.1f}%")

    def update_status_bar(self, sender_stats: Dict[str, Any], monitor_stats: Dict[str, Any]):
        """更新状态栏"""
        system_metrics = monitor_stats.get('system_metrics', {})

        # 运行时间
        runtime = system_metrics.get('runtime_seconds', 0)
        hours = int(runtime // 3600)
        minutes = int((runtime % 3600) // 60)
        seconds = int(runtime % 60)
        self.runtime_status_label.setText(f"运行时间: {hours:02d}:{minutes:02d}:{seconds:02d}")

        # 性能指标
        emails_per_minute = system_metrics.get('overall_emails_per_minute', 0)
        self.performance_status_label.setText(f"性能: {emails_per_minute:.1f} 邮件/分钟")

        # 系统健康
        is_healthy = self.email_sender.is_system_healthy() if self.email_sender else False
        health_text = "🟢 健康" if is_healthy else "🔴 异常"
        self.health_status_label.setText(f"系统健康: {health_text}")

        # 连接状态
        is_running = sender_stats.get('system_status', {}).get('is_running', False)
        connection_text = "🟢 已连接" if is_running else "🔴 未连接"
        self.connection_status_label.setText(f"连接状态: {connection_text}")

    def reset_display(self):
        """重置显示"""
        # 重置概览
        self.total_browsers_label.setText("总浏览器: 0")
        self.active_browsers_label.setText("活跃浏览器: 0")
        self.total_accounts_label.setText("总账号: 0")
        self.active_accounts_label.setText("活跃账号: 0")
        self.system_status_label.setText("系统状态: 未初始化")
        self.utilization_label.setText("系统利用率: 0%")

        # 重置账号统计
        self.total_sent_label.setText("总发送: 0")
        self.total_failed_label.setText("总失败: 0")
        self.overall_success_rate_label.setText("整体成功率: 100%")
        self.emails_per_minute_label.setText("每分钟: 0")
        self.total_switches_label.setText("总切换: 0")
        self.switch_success_rate_label.setText("切换成功率: 100%")

        # 重置任务统计
        self.total_tasks_label.setText("总任务: 0")
        self.pending_tasks_label.setText("待分配: 0")
        self.assigned_tasks_label.setText("已分配: 0")
        self.sending_tasks_label.setText("发送中: 0")
        self.completed_tasks_label.setText("已完成: 0")
        self.failed_tasks_label.setText("失败: 0")

        # 重置性能指标
        self.runtime_label.setText("运行时间: 00:00:00")
        self.avg_task_time_label.setText("平均任务时间: 0s")
        self.peak_emails_per_minute_label.setText("峰值速度: 0/分钟")
        self.system_efficiency_label.setText("系统效率: 0%")

        # 清空表格
        self.account_table.setRowCount(0)

        # 清空错误日志
        self.error_log.clear()

        # 重置状态栏
        self.runtime_status_label.setText("运行时间: 00:00:00")
        self.performance_status_label.setText("性能: 0 邮件/分钟")
        self.health_status_label.setText("系统健康: 🔴")
        self.connection_status_label.setText("连接状态: 🔴 未连接")

    def _on_sender_status_changed(self, event_type: str, data: Any = None):
        """处理发送器状态变化"""
        logger.debug(f"📊 发送器状态变化: {event_type}")

    def _on_monitor_alert(self, alert_type: str, message: str):
        """处理监控警报"""
        logger.warning(f"⚠️ 监控警报: {alert_type} - {message}")

        # 添加到错误日志
        timestamp = time.strftime("%H:%M:%S")
        self.error_log.append(f"[{timestamp}] {alert_type}: {message}")

        # 滚动到底部
        self.error_log.verticalScrollBar().setValue(
            self.error_log.verticalScrollBar().maximum()
        )

    def _on_stats_updated(self, stats: Dict[str, Any]):
        """处理统计信息更新"""
        # 这里可以添加额外的统计处理逻辑
        pass

    def closeEvent(self, event):
        """关闭事件"""
        try:
            # 停止定时器
            self.update_timer.stop()

            # 清理资源
            if self.email_sender:
                self.email_sender.cleanup()

            if self.sending_monitor:
                self.sending_monitor.stop_monitoring()

            event.accept()

        except Exception as e:
            logger.error(f"❌ 关闭事件异常: {e}")
            event.accept()
