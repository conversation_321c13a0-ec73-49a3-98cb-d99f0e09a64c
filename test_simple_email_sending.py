#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版邮件发送测试
测试发送20封邮件的核心功能
"""

import sys
import os
import time
import tempfile

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import get_logger

logger = get_logger("SimpleEmailTest")

def test_browser_creation():
    """测试浏览器创建"""
    try:
        logger.info("🧪 测试浏览器创建...")
        
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        # 配置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 设置用户数据目录
        temp_dir = tempfile.gettempdir()
        user_data_dir = os.path.join(temp_dir, f"test_chrome_{int(time.time())}")
        chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
        chrome_options.add_argument('--window-position=100,100')
        chrome_options.add_argument('--window-size=1200,800')
        
        # 创建浏览器
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(10)
        
        # 设置反检测
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        driver.execute_script("document.title = '测试浏览器';")
        
        # 导航到新浪邮箱
        logger.info("🌐 导航到新浪邮箱...")
        driver.get("https://mail.sina.com.cn")
        time.sleep(5)
        
        # 检查页面
        current_url = driver.current_url
        page_title = driver.title
        logger.info(f"📍 当前URL: {current_url}")
        logger.info(f"📄 页面标题: {page_title}")
        
        # 测试Cookie应用
        test_cookie_application(driver)
        
        # 保持浏览器打开一段时间供观察
        logger.info("⏰ 浏览器将保持打开30秒供观察...")
        time.sleep(30)
        
        # 关闭浏览器
        driver.quit()
        logger.info("✅ 浏览器测试完成")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 浏览器创建测试失败: {e}")
        return False

def test_cookie_application(driver):
    """测试Cookie应用"""
    try:
        logger.info("🔑 测试Cookie应用...")
        
        # 获取Cookie管理器
        from src.core.cookie_manager import CookieManager
        
        # 创建配置
        config = {
            'performance.max_concurrent_sessions': 100,
            'session.timeout': 3600
        }
        cookie_manager = CookieManager(config)
        
        # 测试账号
        test_username = '<EMAIL>'
        
        # 获取Cookie
        cookie_data = cookie_manager.get_cookies(test_username)
        if not cookie_data or 'cookies' not in cookie_data:
            logger.warning(f"⚠️ 没有找到Cookie: {test_username}")
            return False
        
        cookies = cookie_data['cookies']
        logger.info(f"🍪 找到 {len(cookies)} 个Cookie")
        
        # 应用Cookie
        for cookie in cookies:
            try:
                cookie_dict = {
                    'name': cookie.get('name'),
                    'value': cookie.get('value'),
                    'domain': cookie.get('domain', '.sina.com.cn'),
                    'path': cookie.get('path', '/'),
                }
                
                if 'secure' in cookie:
                    cookie_dict['secure'] = cookie['secure']
                if 'httpOnly' in cookie:
                    cookie_dict['httpOnly'] = cookie['httpOnly']
                
                driver.add_cookie(cookie_dict)
                logger.info(f"✅ 应用Cookie: {cookie.get('name')}")
                
            except Exception as e:
                logger.warning(f"⚠️ 跳过无效Cookie: {e}")
                continue
        
        # 刷新页面
        logger.info("🔄 刷新页面以应用Cookie...")
        driver.refresh()
        time.sleep(5)
        
        # 检查登录状态
        current_url = driver.current_url
        page_title = driver.title
        logger.info(f"📍 刷新后URL: {current_url}")
        logger.info(f"📄 刷新后标题: {page_title}")
        
        # 查找登录标识
        try:
            write_buttons = driver.find_elements("xpath", "//a[contains(text(), '写信') or contains(@title, '写信')]")
            if write_buttons:
                logger.info("✅ 找到写信按钮，Cookie登录可能成功")
                return True
            else:
                logger.warning("⚠️ 未找到写信按钮，可能需要手动登录")
                return False
        except Exception as e:
            logger.warning(f"⚠️ 查找写信按钮失败: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Cookie应用测试失败: {e}")
        return False

def test_email_sending_manager():
    """测试邮件发送管理器"""
    try:
        logger.info("📧 测试邮件发送管理器...")
        
        from src.core.email_sending_manager import EmailSendingManager, SendingConfig, SendingMode
        from src.core.unified_email_sender import SendingStrategy
        from src.core.smart_task_queue import TaskPriority
        
        # 创建发送配置
        config = SendingConfig(
            mode=SendingMode.MANUAL,
            strategy=SendingStrategy.STANDARD,
            concurrent_workers=1,
            send_interval=2.0
        )
        
        # 创建发送管理器
        manager = EmailSendingManager(config)
        
        # 准备测试任务
        test_tasks = []
        for i in range(5):  # 只测试5封邮件
            task = {
                'to_email': f'test{i+1}@qq.com',
                'subject': f'🧪 测试邮件 {i+1}',
                'content': f'<h2>测试邮件 {i+1}</h2><p>这是一封测试邮件。</p>',
                'content_type': 'text/html',
                'task_type': 'test_email'
            }
            test_tasks.append(task)
        
        # 添加任务
        batch_id = manager.add_batch_tasks(
            tasks_data=test_tasks,
            batch_name=f"简单测试_{int(time.time())}",
            priority=TaskPriority.NORMAL
        )
        
        logger.info(f"✅ 添加了 {len(test_tasks)} 个测试任务，批次ID: {batch_id}")
        
        # 获取状态
        status = manager.get_sending_status()
        if status:
            queue_stats = status.get('queue_stats', {})
            total_tasks = queue_stats.get('total_tasks', 0)
            logger.info(f"📊 队列状态: 总任务数 {total_tasks}")
        
        logger.info("✅ 邮件发送管理器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 邮件发送管理器测试失败: {e}")
        return False

def main():
    """主函数"""
    try:
        logger.info("🧪 开始简化版邮件发送测试")
        
        # 测试1: 浏览器创建和Cookie应用
        logger.info("=" * 50)
        logger.info("测试1: 浏览器创建和Cookie应用")
        logger.info("=" * 50)
        
        browser_test_result = test_browser_creation()
        
        # 测试2: 邮件发送管理器
        logger.info("=" * 50)
        logger.info("测试2: 邮件发送管理器")
        logger.info("=" * 50)
        
        manager_test_result = test_email_sending_manager()
        
        # 总结
        logger.info("=" * 50)
        logger.info("测试总结")
        logger.info("=" * 50)
        
        logger.info(f"浏览器测试: {'✅ 通过' if browser_test_result else '❌ 失败'}")
        logger.info(f"发送管理器测试: {'✅ 通过' if manager_test_result else '❌ 失败'}")
        
        overall_success = browser_test_result and manager_test_result
        logger.info(f"总体结果: {'🎉 测试成功' if overall_success else '❌ 测试失败'}")
        
        return 0 if overall_success else 1
        
    except Exception as e:
        logger.error(f"❌ 测试程序异常: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n测试完成，退出码: {exit_code}")
    sys.exit(exit_code)
