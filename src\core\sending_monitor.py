#!/usr/bin/env python3
"""
发送监控和统计系统
实时监控每个账号的发送数量、状态和性能指标
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import defaultdict
from enum import Enum

from src.utils.logger import setup_logger

logger = setup_logger("INFO")

class AccountStatus(Enum):
    """账号状态"""
    IDLE = "idle"
    SENDING = "sending"
    SWITCHING = "switching"
    ERROR = "error"
    DISABLED = "disabled"

@dataclass
class AccountMetrics:
    """账号指标"""
    email: str
    browser_id: Optional[str] = None
    status: AccountStatus = AccountStatus.IDLE
    
    # 发送统计
    total_sent: int = 0
    total_failed: int = 0
    current_session_sent: int = 0  # 当前会话发送数量
    
    # 时间统计
    first_send_time: Optional[float] = None
    last_send_time: Optional[float] = None
    last_switch_time: Optional[float] = None
    total_send_time: float = 0  # 总发送时间
    
    # 性能指标
    average_send_time: float = 0
    success_rate: float = 100.0
    emails_per_minute: float = 0
    
    # 错误信息
    last_error: Optional[str] = None
    error_count: int = 0
    consecutive_errors: int = 0
    
    def update_send_success(self, send_time: float):
        """更新发送成功统计"""
        current_time = time.time()
        
        self.total_sent += 1
        self.current_session_sent += 1
        
        if self.first_send_time is None:
            self.first_send_time = current_time
        
        self.last_send_time = current_time
        self.total_send_time += send_time
        
        # 计算平均发送时间
        if self.total_sent > 0:
            self.average_send_time = self.total_send_time / self.total_sent
        
        # 计算成功率
        total_attempts = self.total_sent + self.total_failed
        if total_attempts > 0:
            self.success_rate = (self.total_sent / total_attempts) * 100
        
        # 计算每分钟发送数量
        if self.first_send_time and current_time > self.first_send_time:
            elapsed_minutes = (current_time - self.first_send_time) / 60
            self.emails_per_minute = self.total_sent / elapsed_minutes
        
        # 重置连续错误计数
        self.consecutive_errors = 0
    
    def update_send_failure(self, error_message: str = None):
        """更新发送失败统计"""
        self.total_failed += 1
        self.error_count += 1
        self.consecutive_errors += 1
        
        if error_message:
            self.last_error = error_message
        
        # 计算成功率
        total_attempts = self.total_sent + self.total_failed
        if total_attempts > 0:
            self.success_rate = (self.total_sent / total_attempts) * 100
    
    def reset_session(self):
        """重置会话统计"""
        self.current_session_sent = 0
        self.consecutive_errors = 0

@dataclass
class BrowserMetrics:
    """浏览器指标"""
    browser_id: str
    current_account: Optional[str] = None
    status: str = "idle"
    
    # 任务统计
    total_tasks_assigned: int = 0
    total_tasks_completed: int = 0
    total_tasks_failed: int = 0
    current_queue_size: int = 0
    
    # 时间统计
    start_time: Optional[float] = None
    last_activity_time: Optional[float] = None
    total_active_time: float = 0
    
    # 性能指标
    tasks_per_minute: float = 0
    average_task_time: float = 0
    utilization_rate: float = 0  # 利用率
    
    # 账号切换统计
    total_switches: int = 0
    successful_switches: int = 0
    failed_switches: int = 0
    
    def update_task_assigned(self):
        """更新任务分配统计"""
        self.total_tasks_assigned += 1
        self.last_activity_time = time.time()
        
        if self.start_time is None:
            self.start_time = time.time()
    
    def update_task_completed(self, task_time: float):
        """更新任务完成统计"""
        self.total_tasks_completed += 1
        self.last_activity_time = time.time()
        
        # 计算平均任务时间
        if self.total_tasks_completed > 0:
            self.average_task_time = (self.average_task_time * (self.total_tasks_completed - 1) + task_time) / self.total_tasks_completed
        
        # 计算每分钟任务数
        if self.start_time:
            elapsed_minutes = (time.time() - self.start_time) / 60
            if elapsed_minutes > 0:
                self.tasks_per_minute = self.total_tasks_completed / elapsed_minutes
    
    def update_task_failed(self):
        """更新任务失败统计"""
        self.total_tasks_failed += 1
        self.last_activity_time = time.time()
    
    def update_switch_attempt(self, success: bool):
        """更新账号切换统计"""
        self.total_switches += 1
        if success:
            self.successful_switches += 1
        else:
            self.failed_switches += 1

@dataclass
class SystemMetrics:
    """系统整体指标"""
    start_time: float = field(default_factory=time.time)
    
    # 总体统计
    total_accounts: int = 0
    active_accounts: int = 0
    total_browsers: int = 0
    active_browsers: int = 0
    
    # 任务统计
    total_tasks: int = 0
    pending_tasks: int = 0
    assigned_tasks: int = 0
    sending_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    
    # 性能指标
    overall_emails_per_minute: float = 0
    overall_success_rate: float = 100.0
    system_utilization: float = 0
    
    # 账号切换统计
    total_account_switches: int = 0
    successful_account_switches: int = 0
    
    def update_overall_metrics(self, account_metrics: Dict[str, AccountMetrics], browser_metrics: Dict[str, BrowserMetrics]):
        """更新整体指标"""
        # 计算活跃账号和浏览器
        self.active_accounts = sum(1 for metrics in account_metrics.values() if metrics.status != AccountStatus.IDLE)
        self.active_browsers = sum(1 for metrics in browser_metrics.values() if metrics.status != "idle")
        
        # 计算总体成功率
        total_sent = sum(metrics.total_sent for metrics in account_metrics.values())
        total_failed = sum(metrics.total_failed for metrics in account_metrics.values())
        total_attempts = total_sent + total_failed
        
        if total_attempts > 0:
            self.overall_success_rate = (total_sent / total_attempts) * 100
        
        # 计算整体每分钟发送数量
        runtime = time.time() - self.start_time
        if runtime > 0:
            self.overall_emails_per_minute = (total_sent / runtime) * 60
        
        # 计算系统利用率
        if self.total_browsers > 0:
            self.system_utilization = (self.active_browsers / self.total_browsers) * 100

class SendingMonitor:
    """发送监控系统"""
    
    def __init__(self):
        # 指标存储
        self.account_metrics: Dict[str, AccountMetrics] = {}
        self.browser_metrics: Dict[str, BrowserMetrics] = {}
        self.system_metrics = SystemMetrics()
        
        # 监控线程
        self.monitor_thread: Optional[threading.Thread] = None
        self.is_monitoring = False
        self.monitor_interval = 1.0  # 监控间隔（秒）
        
        # 回调函数
        self.alert_callbacks: List[Callable] = []
        self.stats_callbacks: List[Callable] = []
        
        # 线程锁
        self.metrics_lock = threading.Lock()
        
        # 警报阈值
        self.alert_thresholds = {
            'max_consecutive_errors': 3,
            'min_success_rate': 80.0,
            'max_task_time': 30.0,
            'max_idle_time': 300.0  # 5分钟
        }
        
        logger.info("📊 发送监控系统初始化完成")
    
    def initialize_accounts(self, accounts: List[str]):
        """初始化账号监控"""
        with self.metrics_lock:
            for email in accounts:
                self.account_metrics[email] = AccountMetrics(email=email)
            
            self.system_metrics.total_accounts = len(accounts)
        
        logger.info(f"📋 初始化 {len(accounts)} 个账号监控")
    
    def initialize_browsers(self, browser_ids: List[str]):
        """初始化浏览器监控"""
        with self.metrics_lock:
            for browser_id in browser_ids:
                self.browser_metrics[browser_id] = BrowserMetrics(browser_id=browser_id)
            
            self.system_metrics.total_browsers = len(browser_ids)
        
        logger.info(f"🖥️ 初始化 {len(browser_ids)} 个浏览器监控")
    
    def start_monitoring(self):
        """启动监控"""
        if self.is_monitoring:
            logger.warning("⚠️ 监控已在运行")
            return
        
        logger.info("🚀 启动发送监控...")
        
        self.is_monitoring = True
        self.system_metrics.start_time = time.time()
        
        self.monitor_thread = threading.Thread(
            target=self._monitor_worker,
            name="SendingMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        logger.info("✅ 发送监控启动完成")
    
    def stop_monitoring(self):
        """停止监控"""
        logger.info("🛑 停止发送监控...")
        
        self.is_monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        logger.info("✅ 发送监控已停止")
    
    def _monitor_worker(self):
        """监控工作线程"""
        logger.info("🔧 监控工作线程开始")
        
        while self.is_monitoring:
            try:
                with self.metrics_lock:
                    # 更新系统整体指标
                    self.system_metrics.update_overall_metrics(self.account_metrics, self.browser_metrics)
                    
                    # 检查警报条件
                    self._check_alerts()
                    
                    # 触发统计回调
                    self._trigger_stats_callback()
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                logger.error(f"❌ 监控工作异常: {e}")
                time.sleep(1)
        
        logger.info("🛑 监控工作线程停止")
    
    def _check_alerts(self):
        """检查警报条件"""
        current_time = time.time()
        
        # 检查账号警报
        for email, metrics in self.account_metrics.items():
            # 连续错误警报
            if metrics.consecutive_errors >= self.alert_thresholds['max_consecutive_errors']:
                self._trigger_alert("consecutive_errors", f"账号 {email} 连续失败 {metrics.consecutive_errors} 次")
            
            # 成功率警报
            if metrics.success_rate < self.alert_thresholds['min_success_rate'] and metrics.total_sent + metrics.total_failed > 10:
                self._trigger_alert("low_success_rate", f"账号 {email} 成功率过低: {metrics.success_rate:.1f}%")
        
        # 检查浏览器警报
        for browser_id, metrics in self.browser_metrics.items():
            # 空闲时间警报
            if metrics.last_activity_time and current_time - metrics.last_activity_time > self.alert_thresholds['max_idle_time']:
                self._trigger_alert("browser_idle", f"浏览器 {browser_id} 空闲时间过长")
            
            # 任务时间警报
            if metrics.average_task_time > self.alert_thresholds['max_task_time']:
                self._trigger_alert("slow_browser", f"浏览器 {browser_id} 任务处理过慢: {metrics.average_task_time:.1f}s")
    
    def _trigger_alert(self, alert_type: str, message: str):
        """触发警报"""
        logger.warning(f"⚠️ 警报: {alert_type} - {message}")
        
        for callback in self.alert_callbacks:
            try:
                callback(alert_type, message)
            except Exception as e:
                logger.error(f"❌ 警报回调异常: {e}")
    
    def _trigger_stats_callback(self):
        """触发统计回调"""
        stats = self.get_comprehensive_stats()
        
        for callback in self.stats_callbacks:
            try:
                callback(stats)
            except Exception as e:
                logger.error(f"❌ 统计回调异常: {e}")
    
    def record_email_sent(self, email: str, browser_id: str, send_time: float):
        """记录邮件发送成功"""
        with self.metrics_lock:
            # 更新账号指标
            if email in self.account_metrics:
                self.account_metrics[email].update_send_success(send_time)
                self.account_metrics[email].browser_id = browser_id
                self.account_metrics[email].status = AccountStatus.IDLE
            
            # 更新浏览器指标
            if browser_id in self.browser_metrics:
                self.browser_metrics[browser_id].update_task_completed(send_time)
                self.browser_metrics[browser_id].current_account = email
        
        logger.debug(f"📊 记录发送成功: {email} (浏览器: {browser_id}, 耗时: {send_time:.2f}s)")
    
    def record_email_failed(self, email: str, browser_id: str, error_message: str = None):
        """记录邮件发送失败"""
        with self.metrics_lock:
            # 更新账号指标
            if email in self.account_metrics:
                self.account_metrics[email].update_send_failure(error_message)
                self.account_metrics[email].browser_id = browser_id
                self.account_metrics[email].status = AccountStatus.ERROR
            
            # 更新浏览器指标
            if browser_id in self.browser_metrics:
                self.browser_metrics[browser_id].update_task_failed()
        
        logger.debug(f"📊 记录发送失败: {email} (浏览器: {browser_id})")
    
    def record_task_assigned(self, browser_id: str):
        """记录任务分配"""
        with self.metrics_lock:
            if browser_id in self.browser_metrics:
                self.browser_metrics[browser_id].update_task_assigned()
    
    def record_account_switch(self, browser_id: str, old_email: str, new_email: str, success: bool):
        """记录账号切换"""
        with self.metrics_lock:
            # 更新浏览器指标
            if browser_id in self.browser_metrics:
                self.browser_metrics[browser_id].update_switch_attempt(success)
                if success:
                    self.browser_metrics[browser_id].current_account = new_email
            
            # 更新账号指标
            if old_email in self.account_metrics:
                self.account_metrics[old_email].last_switch_time = time.time()
                self.account_metrics[old_email].status = AccountStatus.IDLE
            
            if new_email in self.account_metrics:
                self.account_metrics[new_email].browser_id = browser_id
                self.account_metrics[new_email].status = AccountStatus.IDLE
            
            # 更新系统指标
            self.system_metrics.total_account_switches += 1
            if success:
                self.system_metrics.successful_account_switches += 1
        
        logger.debug(f"📊 记录账号切换: {old_email} -> {new_email} (浏览器: {browser_id}, 成功: {success})")
    
    def update_account_status(self, email: str, status: AccountStatus):
        """更新账号状态"""
        with self.metrics_lock:
            if email in self.account_metrics:
                self.account_metrics[email].status = status
    
    def update_browser_status(self, browser_id: str, status: str, queue_size: int = 0):
        """更新浏览器状态"""
        with self.metrics_lock:
            if browser_id in self.browser_metrics:
                self.browser_metrics[browser_id].status = status
                self.browser_metrics[browser_id].current_queue_size = queue_size
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        with self.metrics_lock:
            return {
                'system_metrics': {
                    'runtime_seconds': time.time() - self.system_metrics.start_time,
                    'total_accounts': self.system_metrics.total_accounts,
                    'active_accounts': self.system_metrics.active_accounts,
                    'total_browsers': self.system_metrics.total_browsers,
                    'active_browsers': self.system_metrics.active_browsers,
                    'overall_emails_per_minute': self.system_metrics.overall_emails_per_minute,
                    'overall_success_rate': self.system_metrics.overall_success_rate,
                    'system_utilization': self.system_metrics.system_utilization,
                    'total_account_switches': self.system_metrics.total_account_switches,
                    'successful_account_switches': self.system_metrics.successful_account_switches
                },
                'account_metrics': {
                    email: {
                        'email': metrics.email,
                        'browser_id': metrics.browser_id,
                        'status': metrics.status.value,
                        'total_sent': metrics.total_sent,
                        'total_failed': metrics.total_failed,
                        'current_session_sent': metrics.current_session_sent,
                        'success_rate': metrics.success_rate,
                        'emails_per_minute': metrics.emails_per_minute,
                        'average_send_time': metrics.average_send_time,
                        'consecutive_errors': metrics.consecutive_errors,
                        'last_error': metrics.last_error
                    }
                    for email, metrics in self.account_metrics.items()
                },
                'browser_metrics': {
                    browser_id: {
                        'browser_id': metrics.browser_id,
                        'current_account': metrics.current_account,
                        'status': metrics.status,
                        'total_tasks_assigned': metrics.total_tasks_assigned,
                        'total_tasks_completed': metrics.total_tasks_completed,
                        'total_tasks_failed': metrics.total_tasks_failed,
                        'current_queue_size': metrics.current_queue_size,
                        'tasks_per_minute': metrics.tasks_per_minute,
                        'average_task_time': metrics.average_task_time,
                        'total_switches': metrics.total_switches,
                        'successful_switches': metrics.successful_switches,
                        'failed_switches': metrics.failed_switches
                    }
                    for browser_id, metrics in self.browser_metrics.items()
                }
            }
    
    def add_alert_callback(self, callback: Callable):
        """添加警报回调函数"""
        self.alert_callbacks.append(callback)
    
    def add_stats_callback(self, callback: Callable):
        """添加统计回调函数"""
        self.stats_callbacks.append(callback)
    
    def reset_session_stats(self):
        """重置会话统计"""
        with self.metrics_lock:
            for metrics in self.account_metrics.values():
                metrics.reset_session()
        
        logger.info("🔄 重置会话统计")
    
    def get_account_stats(self, email: str) -> Optional[Dict[str, Any]]:
        """获取指定账号的统计信息"""
        with self.metrics_lock:
            if email in self.account_metrics:
                metrics = self.account_metrics[email]
                return {
                    'email': metrics.email,
                    'browser_id': metrics.browser_id,
                    'status': metrics.status.value,
                    'total_sent': metrics.total_sent,
                    'total_failed': metrics.total_failed,
                    'success_rate': metrics.success_rate,
                    'emails_per_minute': metrics.emails_per_minute
                }
        return None
    
    def get_browser_stats(self, browser_id: str) -> Optional[Dict[str, Any]]:
        """获取指定浏览器的统计信息"""
        with self.metrics_lock:
            if browser_id in self.browser_metrics:
                metrics = self.browser_metrics[browser_id]
                return {
                    'browser_id': metrics.browser_id,
                    'current_account': metrics.current_account,
                    'status': metrics.status,
                    'total_tasks_completed': metrics.total_tasks_completed,
                    'tasks_per_minute': metrics.tasks_per_minute,
                    'total_switches': metrics.total_switches
                }
        return None
