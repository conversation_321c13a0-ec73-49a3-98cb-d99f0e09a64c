#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一阶段速度优化测试
目标：发送时间从18秒优化到5秒以内
"""

import sys
import os
import time
import tempfile
import sqlite3
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import get_logger
from src.core.optimized_ultra_fast_sender import OptimizedUltraFastSender
from src.core.cookie_manager import CookieManager

logger = get_logger("SpeedOptimizationPhase1Test")

class SpeedOptimizationPhase1Test:
    """第一阶段速度优化测试类"""
    
    def __init__(self):
        """初始化测试"""
        self.test_name = "第一阶段速度优化测试"
        self.browser_drivers = []
        self.test_accounts = [
            {'email': '<EMAIL>', 'name': '账号1'},
            {'email': '<EMAIL>', 'name': '账号2'}
        ]
        self.test_recipients = []
        self.test_emails_per_account = 3  # 每个账号发送3封邮件测试速度
        self.speed_target = 5.0  # 目标：5秒以内
        
        logger.info(f"🧪 {self.test_name} 初始化完成")
        logger.info(f"🎯 速度优化目标: {self.speed_target}秒以内")
    
    def load_test_recipients(self):
        """加载测试收件人"""
        try:
            logger.info("📧 加载测试收件人...")
            
            # 连接数据库
            db_path = "data/sina_email_automation.db"
            if not os.path.exists(db_path):
                raise Exception(f"数据库文件不存在: {db_path}")
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询前6个收件人（每个账号3个）
            cursor.execute("""
                SELECT email, name FROM recipient_data 
                WHERE email IS NOT NULL AND email != ''
                LIMIT 6
            """)
            
            rows = cursor.fetchall()
            conn.close()
            
            if not rows:
                raise Exception("没有找到收件人数据")
            
            self.test_recipients = []
            for email, name in rows:
                self.test_recipients.append({
                    'email': email,
                    'name': name or email.split('@')[0]
                })
            
            logger.info(f"✅ 加载了 {len(self.test_recipients)} 个测试收件人")
            for i, recipient in enumerate(self.test_recipients):
                logger.info(f"  收件人{i+1}: {recipient['email']}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载收件人数据失败: {e}")
            return False
    
    def create_optimized_browsers(self):
        """创建优化的浏览器 - 使用最新的45个极速参数"""
        try:
            logger.info("🌐 创建优化浏览器...")
            
            for i, account in enumerate(self.test_accounts):
                logger.info(f"🌐 创建优化浏览器 {i+1}/{len(self.test_accounts)} - {account['email']}")
                
                # 配置Chrome选项 - 最新的45个极速启动参数
                chrome_options = Options()
                
                # 基础优化参数
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                
                # 45个极速启动参数 - 第一阶段优化版本
                ultra_fast_options = [
                    '--single-process',                              # 单进程模式
                    '--no-first-run',                               # 跳过首次运行
                    '--disable-background-downloads',               # 禁用后台下载
                    '--memory-pressure-off',                        # 关闭内存压力检测
                    '--disable-background-timer-throttling',        # 禁用后台定时器节流
                    '--disable-renderer-backgrounding',             # 禁用渲染器后台化
                    '--disable-backgrounding-occluded-windows',     # 禁用被遮挡窗口后台化
                    '--disable-client-side-phishing-detection',     # 禁用客户端钓鱼检测
                    '--disable-sync',                               # 禁用同步
                    '--disable-translate',                          # 禁用翻译
                    '--hide-scrollbars',                            # 隐藏滚动条
                    '--disable-logging',                            # 禁用日志
                    '--disable-extensions',                         # 禁用扩展
                    '--disable-plugins',                            # 禁用插件
                    '--disable-images',                             # 禁用图片加载
                    '--disable-javascript-harmony-shipping',        # 禁用JS harmony
                    '--disable-background-networking',              # 禁用后台网络
                    '--disable-background-mode',                    # 禁用后台模式
                    '--disable-default-apps',                       # 禁用默认应用
                    '--disable-hang-monitor',                       # 禁用挂起监控
                    '--disable-prompt-on-repost',                   # 禁用重新提交提示
                    '--disable-web-security',                       # 禁用Web安全
                    '--disable-features=TranslateUI',               # 禁用翻译UI
                    '--disable-ipc-flooding-protection',            # 禁用IPC洪水保护
                    '--disable-component-extensions-with-background-pages',  # 禁用后台页面组件扩展
                    '--disable-domain-reliability',                 # 禁用域可靠性
                    '--disable-features=VizDisplayCompositor',      # 禁用Viz显示合成器
                    '--disable-breakpad',                           # 禁用崩溃报告
                    '--disable-component-update',                   # 禁用组件更新
                    '--disable-print-preview',                      # 禁用打印预览
                    '--disable-software-rasterizer',               # 禁用软件光栅化
                    '--disable-speech-api',                         # 禁用语音API
                    '--no-default-browser-check',                   # 不检查默认浏览器
                    '--no-pings',                                   # 禁用ping
                    '--no-zygote',                                  # 禁用zygote进程
                    '--disable-gpu-sandbox',                        # 禁用GPU沙盒
                    '--disable-field-trial-config',                # 禁用字段试验配置
                    '--disable-back-forward-cache',                # 禁用前进后退缓存
                    '--enable-features=NetworkService,NetworkServiceInProcess',  # 启用网络服务
                    '--force-color-profile=srgb',                  # 强制颜色配置
                    '--metrics-recording-only',                     # 仅记录指标
                    '--disable-dev-tools',                         # 禁用开发者工具
                    '--disable-gpu',                               # 禁用GPU
                    '--disable-audio-output',                      # 禁用音频输出
                    '--disable-notifications'                      # 禁用通知
                ]
                
                for option in ultra_fast_options:
                    chrome_options.add_argument(option)
                
                # 设置用户数据目录
                temp_dir = tempfile.gettempdir()
                user_data_dir = os.path.join(temp_dir, f"test_speed_chrome_{i}_{int(time.time())}")
                chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
                
                # 设置窗口位置
                window_x = i * 450
                window_y = i * 100
                chrome_options.add_argument(f'--window-position={window_x},{window_y}')
                chrome_options.add_argument('--window-size=1300,900')
                
                # 启用性能日志
                chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
                
                # 创建浏览器
                browser_start_time = time.time()
                driver = webdriver.Chrome(options=chrome_options)
                browser_startup_time = time.time() - browser_start_time
                
                driver.set_page_load_timeout(15)  # 减少页面加载超时
                driver.implicitly_wait(2)         # 减少隐式等待
                
                # 设置反检测
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                driver.execute_script(f"document.title = '速度优化测试 - {account['name']}';")
                
                # 导航到新浪邮箱
                page_load_start = time.time()
                driver.get("https://mail.sina.com.cn")
                page_load_time = time.time() - page_load_start
                
                # 应用Cookie
                cookie_start_time = time.time()
                success = self._apply_cookies_and_login(driver, account, i+1)
                cookie_time = time.time() - cookie_start_time
                
                if success:
                    logger.info(f"🔑 浏览器 {i+1} Cookie登录成功: {account['email']}")
                else:
                    logger.warning(f"⚠️ 浏览器 {i+1} Cookie登录失败: {account['email']}")
                
                # 记录启动性能
                logger.info(f"⚡ 浏览器 {i+1} 性能统计:")
                logger.info(f"   浏览器启动: {browser_startup_time:.2f}秒")
                logger.info(f"   页面加载: {page_load_time:.2f}秒")
                logger.info(f"   Cookie应用: {cookie_time:.2f}秒")
                logger.info(f"   总启动时间: {browser_startup_time + page_load_time + cookie_time:.2f}秒")
                
                self.browser_drivers.append({
                    'driver': driver,
                    'account': account,
                    'browser_num': i+1,
                    'startup_time': browser_startup_time + page_load_time + cookie_time
                })
                
                logger.info(f"✅ 优化浏览器 {i+1} 创建成功")
                time.sleep(0.5)  # 减少浏览器间隔时间
            
            logger.info(f"✅ 创建了 {len(self.browser_drivers)} 个优化浏览器")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建优化浏览器失败: {e}")
            return False
    
    def _apply_cookies_and_login(self, driver, account, browser_num):
        """应用Cookie并验证登录状态"""
        try:
            username = account['email']
            logger.info(f"🔑 浏览器 {browser_num} 开始应用Cookie: {username}")
            
            # 获取Cookie管理器
            config = {
                'performance.max_concurrent_sessions': 100,
                'session.timeout': 3600
            }
            cookie_manager = CookieManager(config)
            
            # 获取账号的Cookie
            cookie_data = cookie_manager.get_cookies(username)
            if not cookie_data or 'cookies' not in cookie_data:
                logger.warning(f"⚠️ 浏览器 {browser_num} 没有找到Cookie: {username}")
                return False
            
            cookies = cookie_data['cookies']
            logger.info(f"🍪 浏览器 {browser_num} 开始应用 {len(cookies)} 个Cookie")
            
            # 快速应用Cookie
            for cookie in cookies:
                try:
                    cookie_dict = {
                        'name': cookie.get('name'),
                        'value': cookie.get('value'),
                        'domain': cookie.get('domain', '.sina.com.cn'),
                        'path': cookie.get('path', '/'),
                    }
                    
                    if 'secure' in cookie:
                        cookie_dict['secure'] = cookie['secure']
                    if 'httpOnly' in cookie:
                        cookie_dict['httpOnly'] = cookie['httpOnly']
                    
                    driver.add_cookie(cookie_dict)
                    
                except Exception as e:
                    logger.debug(f"跳过无效Cookie: {e}")
                    continue
            
            # 快速刷新页面
            driver.refresh()
            time.sleep(1)  # 减少刷新等待时间
            
            # 验证登录状态
            return self._verify_login_status(driver, username, browser_num)
            
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} Cookie应用失败: {e}")
            return False
    
    def _verify_login_status(self, driver, username, browser_num):
        """快速验证登录状态"""
        try:
            current_url = driver.current_url
            page_title = driver.title
            
            # 检查是否在邮箱主页
            if "mail.sina.com.cn" in current_url and "登录" not in page_title:
                # 快速查找写信按钮
                try:
                    write_buttons = driver.find_elements("xpath", "//a[contains(text(), '写信') or contains(@title, '写信')]")
                    if write_buttons:
                        logger.info(f"✅ 浏览器 {browser_num} 登录验证成功: 找到写信按钮")
                        return True
                except Exception as e:
                    logger.debug(f"查找登录标识时出错: {e}")
            
            logger.warning(f"⚠️ 浏览器 {browser_num} 可能未登录")
            return False
            
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} 登录状态验证失败: {e}")
            return False
    
    def test_speed_optimization(self):
        """测试速度优化效果"""
        try:
            logger.info("🚀 开始第一阶段速度优化测试...")
            
            total_tests = 0
            total_success = 0
            speed_results = []
            
            # 为每个浏览器分配收件人
            for browser_info in self.browser_drivers:
                driver = browser_info['driver']
                account = browser_info['account']
                browser_num = browser_info['browser_num']
                
                logger.info("=" * 60)
                logger.info(f"🌐 浏览器 {browser_num} ({account['email']}) 开始速度优化测试")
                logger.info("=" * 60)
                
                # 创建优化的超高速发送器
                optimized_sender = OptimizedUltraFastSender(driver)
                
                # 计算该浏览器要发送的邮件
                start_idx = (browser_num - 1) * self.test_emails_per_account
                end_idx = start_idx + self.test_emails_per_account
                recipients_for_browser = self.test_recipients[start_idx:end_idx]
                
                logger.info(f"📧 浏览器 {browser_num} 将发送 {len(recipients_for_browser)} 封邮件")
                
                # 发送每封邮件
                for email_idx, recipient in enumerate(recipients_for_browser):
                    email_num = email_idx + 1
                    total_tests += 1
                    
                    logger.info(f"📮 浏览器 {browser_num} 开始发送第 {email_num} 封邮件到: {recipient['email']}")
                    
                    # 构建邮件内容
                    subject = f"⚡ 速度优化测试邮件 {email_num} - 来自{account['email']}"
                    content = f"""
                    <div style="font-family: Arial, sans-serif;">
                        <h3>⚡ 第一阶段速度优化测试</h3>
                        <p>尊敬的 {recipient['name']}，</p>
                        <p>这是一封来自速度优化系统的测试邮件。</p>
                        <ul>
                            <li>发送账号: {account['email']}</li>
                            <li>邮件编号: {email_num}</li>
                            <li>发送时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</li>
                            <li>浏览器编号: {browser_num}</li>
                            <li>优化目标: 5秒以内完成发送</li>
                        </ul>
                        <p><strong>如果您收到此邮件，说明我们的速度优化系统运行正常！</strong></p>
                        <p style="color: #666; font-size: 12px;">此邮件由速度优化测试系统发送，请勿回复。</p>
                    </div>
                    """
                    
                    # 使用优化发送器发送邮件
                    result = optimized_sender.send_email_optimized(
                        recipient['email'], 
                        subject, 
                        content
                    )
                    
                    # 记录速度结果
                    speed_result = {
                        'browser_num': browser_num,
                        'email_num': email_num,
                        'account': account['email'],
                        'recipient': recipient['email'],
                        'result': result,
                        'target_met': result.get('total_time', float('inf')) <= self.speed_target
                    }
                    speed_results.append(speed_result)
                    
                    if result.get('success'):
                        total_success += 1
                        total_time = result.get('total_time', 0)
                        strategy = result.get('strategy_used', 'unknown')
                        confidence = result.get('verification_confidence', 0)
                        
                        # 检查是否达到速度目标
                        if total_time <= self.speed_target:
                            logger.info(f"🎯 浏览器 {browser_num} 第 {email_num} 封邮件速度目标达成！")
                            logger.info(f"   ✅ 发送时间: {total_time:.2f}秒 (目标: {self.speed_target}秒)")
                        else:
                            logger.warning(f"⚠️ 浏览器 {browser_num} 第 {email_num} 封邮件未达速度目标")
                            logger.warning(f"   ⏰ 发送时间: {total_time:.2f}秒 (目标: {self.speed_target}秒)")
                        
                        logger.info(f"   策略: {strategy}")
                        logger.info(f"   置信度: {confidence:.2f}")
                        
                    else:
                        error = result.get('error', '未知错误')
                        total_time = result.get('total_time', 0)
                        logger.error(f"❌ 浏览器 {browser_num} 第 {email_num} 封邮件发送失败: {error}")
                        logger.error(f"   总耗时: {total_time:.2f}秒")
                    
                    # 邮件间隔
                    time.sleep(1)  # 减少邮件间隔时间
                
                # 显示该浏览器的性能统计
                performance_report = optimized_sender.get_performance_report()
                logger.info(f"📊 浏览器 {browser_num} 性能报告:")
                for key, value in performance_report.items():
                    logger.info(f"   {key}: {value}")
                
                logger.info(f"🎉 浏览器 {browser_num} ({account['email']}) 速度测试完成")
            
            # 显示总体速度优化结果
            self._analyze_speed_optimization_results(speed_results, total_tests, total_success)
            
            logger.info("🎉 第一阶段速度优化测试完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 速度优化测试失败: {e}")
            return False
    
    def _analyze_speed_optimization_results(self, results, total_tests, total_success):
        """分析速度优化结果"""
        try:
            logger.info("=" * 60)
            logger.info("📊 第一阶段速度优化测试总结")
            logger.info("=" * 60)
            
            # 基本统计
            success_rate = (total_success / total_tests) * 100 if total_tests > 0 else 0
            logger.info(f"📧 总测试邮件: {total_tests} 封")
            logger.info(f"✅ 发送成功: {total_success} 封")
            logger.info(f"📈 成功率: {success_rate:.1f}%")
            
            # 速度分析
            successful_results = [r for r in results if r['result'].get('success')]
            if successful_results:
                times = [r['result'].get('total_time', 0) for r in successful_results]
                target_met_count = sum(1 for r in results if r['target_met'])
                
                avg_time = sum(times) / len(times)
                min_time = min(times)
                max_time = max(times)
                target_achievement_rate = (target_met_count / len(successful_results)) * 100
                
                logger.info(f"⚡ 速度统计:")
                logger.info(f"   平均发送时间: {avg_time:.2f}秒")
                logger.info(f"   最快发送时间: {min_time:.2f}秒")
                logger.info(f"   最慢发送时间: {max_time:.2f}秒")
                logger.info(f"   速度目标达成率: {target_achievement_rate:.1f}% (目标: {self.speed_target}秒)")
                
                # 速度改进分析
                baseline_time = 18.52  # 之前的基准时间
                improvement = ((baseline_time - avg_time) / baseline_time) * 100
                logger.info(f"   速度改进: {improvement:.1f}% (从{baseline_time}秒优化到{avg_time:.2f}秒)")
                
                # 策略分析
                strategy_stats = {}
                for result in successful_results:
                    strategy = result['result'].get('strategy_used', 'unknown')
                    if strategy not in strategy_stats:
                        strategy_stats[strategy] = {'count': 0, 'total_time': 0}
                    strategy_stats[strategy]['count'] += 1
                    strategy_stats[strategy]['total_time'] += result['result'].get('total_time', 0)
                
                logger.info(f"📊 策略效果分析:")
                for strategy, stats in strategy_stats.items():
                    avg_strategy_time = stats['total_time'] / stats['count'] if stats['count'] > 0 else 0
                    logger.info(f"   {strategy}: {stats['count']}次, 平均: {avg_strategy_time:.2f}秒")
                
                # 目标达成情况
                if target_achievement_rate >= 80:
                    logger.info("🎯 速度优化目标基本达成！")
                elif target_achievement_rate >= 50:
                    logger.info("⚠️ 速度优化有进展，但需要进一步优化")
                else:
                    logger.warning("❌ 速度优化目标未达成，需要重新设计策略")
            
        except Exception as e:
            logger.error(f"❌ 分析速度优化结果失败: {e}")
    
    def run_complete_test(self):
        """运行完整的速度优化测试"""
        try:
            logger.info(f"🧪 开始 {self.test_name}")
            
            # 1. 加载测试数据
            logger.info("=" * 50)
            logger.info("步骤1: 加载测试数据")
            logger.info("=" * 50)
            
            if not self.load_test_recipients():
                return False
            
            # 2. 创建优化浏览器
            logger.info("=" * 50)
            logger.info("步骤2: 创建优化浏览器")
            logger.info("=" * 50)
            
            if not self.create_optimized_browsers():
                return False
            
            # 3. 等待用户确认
            logger.info("=" * 50)
            logger.info("测试信息确认")
            logger.info("=" * 50)
            
            logger.info(f"⚡ 将测试第一阶段速度优化")
            logger.info(f"🎯 速度目标: {self.speed_target}秒以内")
            logger.info(f"🌐 使用 {len(self.browser_drivers)} 个优化浏览器")
            logger.info(f"📮 每个浏览器发送 {self.test_emails_per_account} 封邮件")
            logger.info(f"👥 总共发送 {len(self.browser_drivers) * self.test_emails_per_account} 封邮件")
            
            logger.info("⏰ 测试将在3秒后开始...")
            time.sleep(3)
            
            # 4. 执行速度优化测试
            logger.info("=" * 50)
            logger.info("步骤3: 执行速度优化测试")
            logger.info("=" * 50)
            
            success = self.test_speed_optimization()
            
            if success:
                logger.info("🎉 第一阶段速度优化测试成功！")
            else:
                logger.error("❌ 第一阶段速度优化测试失败！")
            
            # 5. 保持浏览器打开供观察
            logger.info("⏰ 浏览器将保持打开20秒供观察...")
            time.sleep(20)
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}")
            return False
            
        finally:
            # 清理浏览器
            self.cleanup_browsers()
    
    def cleanup_browsers(self):
        """清理浏览器"""
        try:
            logger.info("🧹 清理测试浏览器...")
            
            for i, browser_info in enumerate(self.browser_drivers):
                try:
                    browser_info['driver'].quit()
                    logger.info(f"✅ 浏览器 {i+1} 已关闭")
                except Exception as e:
                    logger.warning(f"⚠️ 关闭浏览器 {i+1} 失败: {e}")
            
            self.browser_drivers.clear()
            logger.info("✅ 浏览器清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理浏览器失败: {e}")

def main():
    """主函数"""
    try:
        logger.info("🧪 启动第一阶段速度优化测试程序")
        
        # 创建测试实例
        test = SpeedOptimizationPhase1Test()
        
        # 运行完整测试
        success = test.run_complete_test()
        
        if success:
            logger.info("🎉 测试成功完成！")
            return 0
        else:
            logger.error("❌ 测试失败！")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 测试程序异常: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n测试完成，退出码: {exit_code}")
    sys.exit(exit_code)
