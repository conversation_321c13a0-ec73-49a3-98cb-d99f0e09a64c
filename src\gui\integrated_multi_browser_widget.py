#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成多浏览器发送器
融合智能任务管理系统，实现先添加任务再发送的流程
"""

import sys
import os
import time
import threading
from typing import List, Dict, Any, Optional
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from loguru import logger

# 导入智能任务管理系统
try:
    from src.core.email_sending_manager import EmailSendingManager, SendingConfig, SendingMode
    from src.core.smart_task_queue import TaskPriority
    from src.core.unified_email_sender import SendingStrategy
    from src.core.sender_factory import get_sender_factory
    from src.models.database import DatabaseManager
    from src.models.account import Account
except ImportError as e:
    logger.error(f"导入智能任务管理系统失败: {e}")


class IntegratedMultiBrowserWidget(QWidget):
    """集成多浏览器发送器界面"""
    
    # 信号定义
    stats_updated = pyqtSignal(dict)
    log_message = pyqtSignal(str, str)  # message, level
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        super().__init__()
        
        # 数据库管理器
        if db_manager is None:
            from src.utils.config_manager import ConfigManager
            config_manager = ConfigManager()
            db_path = config_manager.get_database_path()
            self.db_manager = DatabaseManager(str(db_path))
        else:
            self.db_manager = db_manager
        
        # 智能任务管理系统
        self.sending_manager = None
        self.accounts: List[Account] = []
        self.is_sending = False
        
        # 界面组件
        self.account_table = None
        self.task_stats_labels = {}
        self.log_text = None
        
        # 定时器
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_stats_display)
        
        self.init_ui()
        self.init_task_management()
        self.load_accounts()
        
        logger.info("🌐 集成多浏览器发送器初始化完成")
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("🌐 集成多浏览器邮件发送系统")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)
        
        # 设置样式
        self.setStyleSheet("""
            QWidget {
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 9pt;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                font-size: 10pt;
                font-weight: bold;
            }
            QPushButton {
                background-color: #3498db;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 账号管理标签页
        account_tab = self.create_account_tab()
        tab_widget.addTab(account_tab, "👤 账号管理")
        
        # 任务管理标签页
        task_tab = self.create_task_tab()
        tab_widget.addTab(task_tab, "📋 任务管理")
        
        # 发送控制标签页
        control_tab = self.create_control_tab()
        tab_widget.addTab(control_tab, "🚀 发送控制")
        
        # 统计监控标签页
        stats_tab = self.create_stats_tab()
        tab_widget.addTab(stats_tab, "📊 统计监控")
        
        main_layout.addWidget(tab_widget)
        
        # 底部状态栏
        status_layout = self.create_status_bar()
        main_layout.addLayout(status_layout)
    
    def create_account_tab(self) -> QWidget:
        """创建账号管理标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 账号管理组
        account_group = QGroupBox("📧 邮箱账号管理")
        account_layout = QVBoxLayout(account_group)
        
        # 账号表格
        self.account_table = QTableWidget()
        self.account_table.setColumnCount(6)
        self.account_table.setHorizontalHeaderLabels([
            "邮箱地址", "状态", "发送数量", "成功率", "最后使用", "操作"
        ])
        
        # 设置表格属性
        header = self.account_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        
        account_layout.addWidget(self.account_table)
        
        # 账号操作按钮
        account_btn_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("🔄 刷新账号")
        refresh_btn.clicked.connect(self.load_accounts)
        account_btn_layout.addWidget(refresh_btn)
        
        test_btn = QPushButton("🧪 测试账号")
        test_btn.clicked.connect(self.test_accounts)
        account_btn_layout.addWidget(test_btn)
        
        account_btn_layout.addStretch()
        account_layout.addLayout(account_btn_layout)
        
        layout.addWidget(account_group)
        
        # 账号配置组
        config_group = QGroupBox("⚙️ 发送配置")
        config_layout = QFormLayout(config_group)
        
        # 发送策略
        self.strategy_combo = QComboBox()
        self.strategy_combo.addItems(["超高速发送", "标准发送", "安全发送"])
        config_layout.addRow("发送策略:", self.strategy_combo)
        
        # 并发数
        self.concurrent_spin = QSpinBox()
        self.concurrent_spin.setRange(1, 10)
        self.concurrent_spin.setValue(3)
        config_layout.addRow("并发数:", self.concurrent_spin)
        
        # 发送间隔
        self.interval_spin = QDoubleSpinBox()
        self.interval_spin.setRange(0.5, 10.0)
        self.interval_spin.setValue(2.0)
        self.interval_spin.setSuffix(" 秒")
        config_layout.addRow("发送间隔:", self.interval_spin)
        
        # 账号轮换
        self.rotate_accounts_check = QCheckBox("启用账号轮换")
        self.rotate_accounts_check.setChecked(True)
        config_layout.addRow("", self.rotate_accounts_check)
        
        layout.addWidget(config_group)
        
        return tab
    
    def create_task_tab(self) -> QWidget:
        """创建任务管理标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 任务添加组
        add_group = QGroupBox("📝 添加邮件任务")
        add_layout = QVBoxLayout(add_group)
        
        # 创建子标签页
        task_tab_widget = QTabWidget()
        
        # 单个任务标签页
        single_tab = self.create_single_task_tab()
        task_tab_widget.addTab(single_tab, "单个任务")
        
        # 批量任务标签页
        batch_tab = self.create_batch_task_tab()
        task_tab_widget.addTab(batch_tab, "批量任务")
        
        # 文件导入标签页
        file_tab = self.create_file_import_tab()
        task_tab_widget.addTab(file_tab, "文件导入")
        
        add_layout.addWidget(task_tab_widget)
        layout.addWidget(add_group)
        
        # 任务列表组
        list_group = QGroupBox("📋 任务列表")
        list_layout = QVBoxLayout(list_group)
        
        # 任务表格
        self.task_table = QTableWidget()
        self.task_table.setColumnCount(6)
        self.task_table.setHorizontalHeaderLabels([
            "收件人", "主题", "状态", "优先级", "创建时间", "操作"
        ])
        
        # 设置表格属性
        task_header = self.task_table.horizontalHeader()
        task_header.setStretchLastSection(True)
        task_header.setSectionResizeMode(0, QHeaderView.Stretch)
        
        list_layout.addWidget(self.task_table)
        
        # 任务操作按钮
        task_btn_layout = QHBoxLayout()
        
        refresh_task_btn = QPushButton("🔄 刷新任务")
        refresh_task_btn.clicked.connect(self.refresh_task_list)
        task_btn_layout.addWidget(refresh_task_btn)
        
        clear_completed_btn = QPushButton("🧹 清理已完成")
        clear_completed_btn.clicked.connect(self.clear_completed_tasks)
        task_btn_layout.addWidget(clear_completed_btn)
        
        task_btn_layout.addStretch()
        list_layout.addLayout(task_btn_layout)
        
        layout.addWidget(list_group)
        
        return tab
    
    def create_single_task_tab(self) -> QWidget:
        """创建单个任务标签页"""
        tab = QWidget()
        layout = QFormLayout(tab)
        
        # 收件人
        self.single_email_edit = QLineEdit()
        self.single_email_edit.setPlaceholderText("输入收件人邮箱地址")
        layout.addRow("收件人:", self.single_email_edit)
        
        # 主题
        self.single_subject_edit = QLineEdit()
        self.single_subject_edit.setPlaceholderText("输入邮件主题")
        layout.addRow("主题:", self.single_subject_edit)
        
        # 内容
        self.single_content_edit = QTextEdit()
        self.single_content_edit.setPlaceholderText("输入邮件内容")
        self.single_content_edit.setMaximumHeight(150)
        layout.addRow("内容:", self.single_content_edit)
        
        # 优先级
        self.single_priority_combo = QComboBox()
        self.single_priority_combo.addItems(["低", "普通", "高", "紧急", "关键"])
        self.single_priority_combo.setCurrentText("普通")
        layout.addRow("优先级:", self.single_priority_combo)
        
        # 添加按钮
        add_btn = QPushButton("➕ 添加任务")
        add_btn.clicked.connect(self.add_single_task)
        layout.addRow("", add_btn)
        
        return tab
    
    def create_batch_task_tab(self) -> QWidget:
        """创建批量任务标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 说明
        info_label = QLabel("请输入批量邮件信息，每行一个，格式：邮箱,主题,内容")
        info_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(info_label)
        
        # 批量输入
        self.batch_text_edit = QTextEdit()
        self.batch_text_edit.setPlaceholderText(
            "示例：\n"
            "<EMAIL>,欢迎邮件,欢迎加入我们！\n"
            "<EMAIL>,通知邮件,重要通知内容"
        )
        layout.addWidget(self.batch_text_edit)
        
        # 批量设置
        settings_layout = QHBoxLayout()
        
        # 批次名称
        settings_layout.addWidget(QLabel("批次名称:"))
        self.batch_name_edit = QLineEdit()
        self.batch_name_edit.setPlaceholderText("可选，留空自动生成")
        settings_layout.addWidget(self.batch_name_edit)
        
        # 优先级
        settings_layout.addWidget(QLabel("优先级:"))
        self.batch_priority_combo = QComboBox()
        self.batch_priority_combo.addItems(["低", "普通", "高", "紧急", "关键"])
        self.batch_priority_combo.setCurrentText("普通")
        settings_layout.addWidget(self.batch_priority_combo)
        
        # 添加按钮
        batch_add_btn = QPushButton("📦 添加批次")
        batch_add_btn.clicked.connect(self.add_batch_tasks)
        settings_layout.addWidget(batch_add_btn)
        
        layout.addLayout(settings_layout)
        
        return tab
    
    def create_file_import_tab(self) -> QWidget:
        """创建文件导入标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 文件选择
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("选择文件:"))
        
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("选择CSV或Excel文件")
        file_layout.addWidget(self.file_path_edit)
        
        browse_btn = QPushButton("📁 浏览")
        browse_btn.clicked.connect(self.browse_file)
        file_layout.addWidget(browse_btn)
        
        layout.addLayout(file_layout)
        
        # 模板设置
        template_group = QGroupBox("📧 邮件模板")
        template_layout = QFormLayout(template_group)
        
        self.subject_template_edit = QLineEdit()
        self.subject_template_edit.setText("邮件主题")
        self.subject_template_edit.setPlaceholderText("支持变量替换，如：Hello {name}")
        template_layout.addRow("主题模板:", self.subject_template_edit)
        
        self.content_template_edit = QTextEdit()
        self.content_template_edit.setPlainText("邮件内容")
        self.content_template_edit.setPlaceholderText("支持变量替换，如：Dear {name}, welcome to {company}!")
        self.content_template_edit.setMaximumHeight(100)
        template_layout.addRow("内容模板:", self.content_template_edit)
        
        layout.addWidget(template_group)
        
        # 导入设置
        import_layout = QHBoxLayout()
        
        import_layout.addWidget(QLabel("分批大小:"))
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(100, 10000)
        self.batch_size_spin.setValue(1000)
        import_layout.addWidget(self.batch_size_spin)
        
        import_layout.addStretch()
        
        import_btn = QPushButton("📥 导入文件")
        import_btn.clicked.connect(self.import_file)
        import_layout.addWidget(import_btn)
        
        layout.addLayout(import_layout)
        
        return tab

    def create_control_tab(self) -> QWidget:
        """创建发送控制标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 发送控制组
        control_group = QGroupBox("🚀 发送控制")
        control_layout = QVBoxLayout(control_group)

        # 发送状态显示
        status_layout = QHBoxLayout()

        self.status_label = QLabel("系统状态: 空闲")
        self.status_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        status_layout.addWidget(self.status_label)

        status_layout.addStretch()

        self.progress_label = QLabel("进度: 0%")
        status_layout.addWidget(self.progress_label)

        control_layout.addLayout(status_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        control_layout.addWidget(self.progress_bar)

        # 控制按钮
        btn_layout = QHBoxLayout()

        self.start_btn = QPushButton("🚀 开始发送")
        self.start_btn.clicked.connect(self.start_sending)
        self.start_btn.setStyleSheet("background-color: #27ae60;")
        btn_layout.addWidget(self.start_btn)

        self.pause_btn = QPushButton("⏸️ 暂停发送")
        self.pause_btn.clicked.connect(self.pause_sending)
        self.pause_btn.setEnabled(False)
        btn_layout.addWidget(self.pause_btn)

        self.resume_btn = QPushButton("▶️ 恢复发送")
        self.resume_btn.clicked.connect(self.resume_sending)
        self.resume_btn.setEnabled(False)
        btn_layout.addWidget(self.resume_btn)

        self.stop_btn = QPushButton("🛑 停止发送")
        self.stop_btn.clicked.connect(self.stop_sending)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("background-color: #e74c3c;")
        btn_layout.addWidget(self.stop_btn)

        control_layout.addLayout(btn_layout)
        layout.addWidget(control_group)

        # 发送统计组
        stats_group = QGroupBox("📊 发送统计")
        stats_layout = QGridLayout(stats_group)

        # 统计标签
        stats_labels = [
            ("总任务数", "total_tasks"),
            ("已完成", "completed_tasks"),
            ("失败任务", "failed_tasks"),
            ("发送速度", "send_speed"),
            ("成功率", "success_rate"),
            ("剩余时间", "remaining_time")
        ]

        for i, (label_text, key) in enumerate(stats_labels):
            row, col = i // 2, (i % 2) * 2

            label = QLabel(f"{label_text}:")
            stats_layout.addWidget(label, row, col)

            value_label = QLabel("0")
            value_label.setStyleSheet("font-weight: bold; color: #3498db;")
            self.task_stats_labels[key] = value_label
            stats_layout.addWidget(value_label, row, col + 1)

        layout.addWidget(stats_group)

        return tab

    def create_stats_tab(self) -> QWidget:
        """创建统计监控标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 账号统计组
        account_stats_group = QGroupBox("👤 账号发送统计")
        account_stats_layout = QVBoxLayout(account_stats_group)

        self.account_stats_table = QTableWidget()
        self.account_stats_table.setColumnCount(5)
        self.account_stats_table.setHorizontalHeaderLabels([
            "邮箱地址", "发送数量", "成功数量", "成功率", "平均响应时间"
        ])

        account_stats_header = self.account_stats_table.horizontalHeader()
        account_stats_header.setStretchLastSection(True)

        account_stats_layout.addWidget(self.account_stats_table)
        layout.addWidget(account_stats_group)

        # 日志显示组
        log_group = QGroupBox("📝 发送日志")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        self.log_text.setStyleSheet("background-color: #2c3e50; color: #ecf0f1; font-family: 'Consolas', monospace;")
        log_layout.addWidget(self.log_text)

        # 日志控制按钮
        log_btn_layout = QHBoxLayout()

        clear_log_btn = QPushButton("🧹 清空日志")
        clear_log_btn.clicked.connect(self.clear_log)
        log_btn_layout.addWidget(clear_log_btn)

        log_btn_layout.addStretch()
        log_layout.addLayout(log_btn_layout)

        layout.addWidget(log_group)

        return tab

    def create_status_bar(self) -> QHBoxLayout:
        """创建状态栏"""
        layout = QHBoxLayout()

        # 连接状态
        self.connection_label = QLabel("🔗 连接状态: 未连接")
        layout.addWidget(self.connection_label)

        layout.addStretch()

        # 时间显示
        self.time_label = QLabel()
        self.update_time_display()
        layout.addWidget(self.time_label)

        # 定时更新时间
        time_timer = QTimer()
        time_timer.timeout.connect(self.update_time_display)
        time_timer.start(1000)  # 每秒更新

        return layout

    def init_task_management(self):
        """初始化任务管理系统"""
        try:
            # 创建发送配置
            config = SendingConfig(
                mode=SendingMode.MANUAL,
                strategy=SendingStrategy.ULTRA_FAST,
                concurrent_workers=3,
                send_interval=2.0
            )

            # 创建邮件发送管理器
            self.sending_manager = EmailSendingManager(config)

            # 设置发送器工厂
            factory = get_sender_factory()
            factory.initialize()
            self.sending_manager.set_sender_factory(lambda: factory.create_sender(config.strategy))

            logger.info("✅ 任务管理系统初始化完成")

        except Exception as e:
            logger.error(f"❌ 任务管理系统初始化失败: {e}")
            self.log_message.emit(f"任务管理系统初始化失败: {e}", "error")

    def load_accounts(self):
        """加载邮箱账号"""
        try:
            # 使用AccountManager加载账号
            from src.models.account import AccountManager
            account_manager = AccountManager(self.db_manager)
            self.accounts = account_manager.get_all_accounts()

            # 更新账号表格
            self.update_account_table()

            self.log_message.emit(f"已加载 {len(self.accounts)} 个邮箱账号", "info")

        except Exception as e:
            logger.error(f"加载账号失败: {e}")
            self.log_message.emit(f"加载账号失败: {e}", "error")

    def update_account_table(self):
        """更新账号表格"""
        if not self.account_table:
            return

        self.account_table.setRowCount(len(self.accounts))

        for row, account in enumerate(self.accounts):
            # 邮箱地址
            self.account_table.setItem(row, 0, QTableWidgetItem(account.email))

            # 状态
            status = "正常" if account.is_active else "禁用"
            status_item = QTableWidgetItem(status)
            if account.is_active:
                status_item.setBackground(QColor("#d5f4e6"))
            else:
                status_item.setBackground(QColor("#ffeaa7"))
            self.account_table.setItem(row, 1, status_item)

            # 发送数量（模拟数据）
            self.account_table.setItem(row, 2, QTableWidgetItem("0"))

            # 成功率（模拟数据）
            self.account_table.setItem(row, 3, QTableWidgetItem("0%"))

            # 最后使用
            self.account_table.setItem(row, 4, QTableWidgetItem("未使用"))

            # 操作按钮
            btn_widget = QWidget()
            btn_layout = QHBoxLayout(btn_widget)
            btn_layout.setContentsMargins(2, 2, 2, 2)

            test_btn = QPushButton("测试")
            test_btn.setMaximumSize(50, 25)
            test_btn.clicked.connect(lambda checked, acc=account: self.test_single_account(acc))
            btn_layout.addWidget(test_btn)

            self.account_table.setCellWidget(row, 5, btn_widget)

    def add_single_task(self):
        """添加单个任务"""
        try:
            # 获取输入数据
            email = self.single_email_edit.text().strip()
            subject = self.single_subject_edit.text().strip()
            content = self.single_content_edit.toPlainText().strip()
            priority_text = self.single_priority_combo.currentText()

            # 验证输入
            if not email or not subject or not content:
                QMessageBox.warning(self, "警告", "请填写完整的邮件信息")
                return

            # 转换优先级
            priority_map = {
                "低": TaskPriority.LOW,
                "普通": TaskPriority.NORMAL,
                "高": TaskPriority.HIGH,
                "紧急": TaskPriority.URGENT,
                "关键": TaskPriority.CRITICAL
            }
            priority = priority_map.get(priority_text, TaskPriority.NORMAL)

            # 添加任务
            task_id = self.sending_manager.add_single_task(
                to_email=email,
                subject=subject,
                content=content,
                priority=priority
            )

            # 清空输入框
            self.single_email_edit.clear()
            self.single_subject_edit.clear()
            self.single_content_edit.clear()

            # 刷新任务列表
            self.refresh_task_list()

            self.log_message.emit(f"✅ 添加单个任务成功: {email}", "info")
            QMessageBox.information(self, "成功", f"任务添加成功！\n任务ID: {task_id}")

        except Exception as e:
            logger.error(f"添加单个任务失败: {e}")
            self.log_message.emit(f"添加单个任务失败: {e}", "error")
            QMessageBox.critical(self, "错误", f"添加任务失败:\n{e}")

    def add_batch_tasks(self):
        """添加批量任务"""
        try:
            # 获取批量数据
            batch_text = self.batch_text_edit.toPlainText().strip()
            if not batch_text:
                QMessageBox.warning(self, "警告", "请输入批量邮件数据")
                return

            # 解析批量数据
            tasks_data = []
            lines = batch_text.split('\n')

            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue

                parts = line.split(',')
                if len(parts) < 3:
                    QMessageBox.warning(self, "格式错误", f"第 {line_num} 行格式错误，应为：邮箱,主题,内容")
                    return

                email = parts[0].strip()
                subject = parts[1].strip()
                content = ','.join(parts[2:]).strip()  # 内容可能包含逗号

                tasks_data.append({
                    'to_email': email,
                    'subject': subject,
                    'content': content,
                    'content_type': 'text/plain'
                })

            if not tasks_data:
                QMessageBox.warning(self, "警告", "没有有效的邮件数据")
                return

            # 获取批次设置
            batch_name = self.batch_name_edit.text().strip()
            if not batch_name:
                batch_name = f"批量任务_{int(time.time())}"

            priority_text = self.batch_priority_combo.currentText()
            priority_map = {
                "低": TaskPriority.LOW,
                "普通": TaskPriority.NORMAL,
                "高": TaskPriority.HIGH,
                "紧急": TaskPriority.URGENT,
                "关键": TaskPriority.CRITICAL
            }
            priority = priority_map.get(priority_text, TaskPriority.NORMAL)

            # 添加批量任务
            batch_id = self.sending_manager.add_batch_tasks(
                tasks_data=tasks_data,
                batch_name=batch_name,
                priority=priority
            )

            # 清空输入
            self.batch_text_edit.clear()
            self.batch_name_edit.clear()

            # 刷新任务列表
            self.refresh_task_list()

            self.log_message.emit(f"✅ 添加批量任务成功: {len(tasks_data)} 个任务", "info")
            QMessageBox.information(self, "成功", f"批量任务添加成功！\n批次ID: {batch_id}\n任务数量: {len(tasks_data)}")

        except Exception as e:
            logger.error(f"添加批量任务失败: {e}")
            self.log_message.emit(f"添加批量任务失败: {e}", "error")
            QMessageBox.critical(self, "错误", f"添加批量任务失败:\n{e}")

    def browse_file(self):
        """浏览文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择邮件数据文件",
            "",
            "Excel文件 (*.xlsx *.xls);;CSV文件 (*.csv);;所有文件 (*)"
        )

        if file_path:
            self.file_path_edit.setText(file_path)

    def import_file(self):
        """导入文件"""
        try:
            file_path = self.file_path_edit.text().strip()
            if not file_path:
                QMessageBox.warning(self, "警告", "请选择要导入的文件")
                return

            # 检查文件是否存在
            import os
            if not os.path.exists(file_path):
                QMessageBox.warning(self, "警告", "文件不存在")
                return

            # 获取模板设置
            subject_template = self.subject_template_edit.text().strip()
            content_template = self.content_template_edit.toPlainText().strip()
            batch_size = self.batch_size_spin.value()

            if not subject_template or not content_template:
                QMessageBox.warning(self, "警告", "请设置邮件模板")
                return

            # 显示进度对话框
            progress_dialog = QProgressDialog("正在导入文件...", "取消", 0, 100, self)
            progress_dialog.setWindowModality(Qt.WindowModal)
            progress_dialog.show()

            # 在新线程中处理文件导入
            def import_worker():
                try:
                    # 这里应该调用批处理器来处理大文件
                    # 暂时模拟处理过程
                    import pandas as pd

                    # 读取文件
                    if file_path.endswith('.csv'):
                        df = pd.read_csv(file_path)
                    else:
                        df = pd.read_excel(file_path)

                    total_rows = len(df)

                    # 检查必需列
                    if 'email' not in df.columns:
                        QMessageBox.critical(self, "错误", "文件必须包含 'email' 列")
                        return

                    # 分批处理
                    batch_count = 0
                    for start_idx in range(0, total_rows, batch_size):
                        if progress_dialog.wasCanceled():
                            break

                        end_idx = min(start_idx + batch_size, total_rows)
                        batch_df = df.iloc[start_idx:end_idx]

                        # 创建任务数据
                        tasks_data = []
                        for _, row in batch_df.iterrows():
                            # 替换模板变量
                            subject = subject_template
                            content = content_template

                            for col in df.columns:
                                if col in row and pd.notna(row[col]):
                                    subject = subject.replace(f'{{{col}}}', str(row[col]))
                                    content = content.replace(f'{{{col}}}', str(row[col]))

                            tasks_data.append({
                                'to_email': row['email'],
                                'subject': subject,
                                'content': content,
                                'content_type': 'text/plain'
                            })

                        # 添加批次
                        batch_name = f"文件导入批次_{batch_count + 1}"
                        self.sending_manager.add_batch_tasks(
                            tasks_data=tasks_data,
                            batch_name=batch_name,
                            priority=TaskPriority.NORMAL
                        )

                        batch_count += 1

                        # 更新进度
                        progress = int((end_idx / total_rows) * 100)
                        progress_dialog.setValue(progress)

                    progress_dialog.close()

                    # 刷新任务列表
                    self.refresh_task_list()

                    QMessageBox.information(self, "成功", f"文件导入成功！\n总记录数: {total_rows}\n分批数: {batch_count}")
                    self.log_message.emit(f"✅ 文件导入成功: {total_rows} 条记录，{batch_count} 个批次", "info")

                except Exception as e:
                    progress_dialog.close()
                    logger.error(f"文件导入失败: {e}")
                    QMessageBox.critical(self, "错误", f"文件导入失败:\n{e}")

            # 启动导入线程
            import_thread = threading.Thread(target=import_worker, daemon=True)
            import_thread.start()

        except Exception as e:
            logger.error(f"导入文件失败: {e}")
            QMessageBox.critical(self, "错误", f"导入文件失败:\n{e}")

    def start_sending(self):
        """开始发送"""
        try:
            if not self.sending_manager:
                QMessageBox.warning(self, "警告", "任务管理系统未初始化")
                return

            # 检查是否有任务
            status = self.sending_manager.get_sending_status()
            if status['queue_stats']['total_tasks'] == 0:
                QMessageBox.warning(self, "警告", "没有待发送的任务，请先添加任务")
                return

            # 检查是否有可用账号
            if not self.accounts:
                QMessageBox.warning(self, "警告", "没有可用的邮箱账号，请先加载账号")
                return

            # 更新发送配置
            self.update_sending_config()

            # 开始发送
            success = self.sending_manager.start_sending()

            if success:
                self.is_sending = True
                self.update_button_states()
                self.stats_timer.start(2000)  # 每2秒更新统计

                self.log_message.emit("🚀 邮件发送已开始", "info")
                self.status_label.setText("系统状态: 发送中")
            else:
                QMessageBox.warning(self, "警告", "启动发送失败")

        except Exception as e:
            logger.error(f"开始发送失败: {e}")
            QMessageBox.critical(self, "错误", f"开始发送失败:\n{e}")

    def pause_sending(self):
        """暂停发送"""
        try:
            if self.sending_manager:
                success = self.sending_manager.pause_sending()
                if success:
                    self.update_button_states()
                    self.log_message.emit("⏸️ 邮件发送已暂停", "info")
                    self.status_label.setText("系统状态: 已暂停")
        except Exception as e:
            logger.error(f"暂停发送失败: {e}")

    def resume_sending(self):
        """恢复发送"""
        try:
            if self.sending_manager:
                success = self.sending_manager.resume_sending()
                if success:
                    self.update_button_states()
                    self.log_message.emit("▶️ 邮件发送已恢复", "info")
                    self.status_label.setText("系统状态: 发送中")
        except Exception as e:
            logger.error(f"恢复发送失败: {e}")

    def stop_sending(self):
        """停止发送"""
        try:
            if self.sending_manager:
                success = self.sending_manager.stop_sending()
                if success:
                    self.is_sending = False
                    self.stats_timer.stop()
                    self.update_button_states()
                    self.log_message.emit("🛑 邮件发送已停止", "info")
                    self.status_label.setText("系统状态: 空闲")
                    self.progress_bar.setValue(0)
        except Exception as e:
            logger.error(f"停止发送失败: {e}")

    def update_sending_config(self):
        """更新发送配置"""
        try:
            # 获取界面配置
            strategy_text = self.strategy_combo.currentText()
            strategy_map = {
                "超高速发送": SendingStrategy.ULTRA_FAST,
                "标准发送": SendingStrategy.STANDARD,
                "安全发送": SendingStrategy.SAFE
            }
            strategy = strategy_map.get(strategy_text, SendingStrategy.STANDARD)

            concurrent_workers = self.concurrent_spin.value()
            send_interval = self.interval_spin.value()

            # 更新配置
            config = SendingConfig(
                mode=SendingMode.MANUAL,
                strategy=strategy,
                concurrent_workers=concurrent_workers,
                send_interval=send_interval
            )

            self.sending_manager.update_config(config)

        except Exception as e:
            logger.error(f"更新发送配置失败: {e}")

    def update_button_states(self):
        """更新按钮状态"""
        if not self.sending_manager:
            return

        status = self.sending_manager.get_sending_status()
        sending_status = status['status']

        if sending_status == 'idle':
            self.start_btn.setEnabled(True)
            self.pause_btn.setEnabled(False)
            self.resume_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)
        elif sending_status == 'sending':
            self.start_btn.setEnabled(False)
            self.pause_btn.setEnabled(True)
            self.resume_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
        elif sending_status == 'paused':
            self.start_btn.setEnabled(False)
            self.pause_btn.setEnabled(False)
            self.resume_btn.setEnabled(True)
            self.stop_btn.setEnabled(True)
        else:
            self.start_btn.setEnabled(True)
            self.pause_btn.setEnabled(False)
            self.resume_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

    def refresh_task_list(self):
        """刷新任务列表"""
        try:
            if not self.sending_manager:
                return

            # 获取批次列表
            batches = self.sending_manager.get_batch_list()

            # 更新任务表格
            self.task_table.setRowCount(len(batches))

            for row, batch in enumerate(batches):
                # 收件人（显示批次名称）
                self.task_table.setItem(row, 0, QTableWidgetItem(batch['name']))

                # 主题（显示任务数量）
                self.task_table.setItem(row, 1, QTableWidgetItem(f"{batch['total_tasks']} 个任务"))

                # 状态
                status_item = QTableWidgetItem(batch['status'])
                if batch['status'] == 'completed':
                    status_item.setBackground(QColor("#d5f4e6"))
                elif batch['status'] == 'processing':
                    status_item.setBackground(QColor("#fff3cd"))
                else:
                    status_item.setBackground(QColor("#f8f9fa"))
                self.task_table.setItem(row, 2, status_item)

                # 优先级
                self.task_table.setItem(row, 3, QTableWidgetItem(batch.get('priority', 'normal')))

                # 创建时间
                self.task_table.setItem(row, 4, QTableWidgetItem(batch.get('created_at', '')))

                # 操作按钮
                btn_widget = QWidget()
                btn_layout = QHBoxLayout(btn_widget)
                btn_layout.setContentsMargins(2, 2, 2, 2)

                view_btn = QPushButton("查看")
                view_btn.setMaximumSize(50, 25)
                btn_layout.addWidget(view_btn)

                self.task_table.setCellWidget(row, 5, btn_widget)

        except Exception as e:
            logger.error(f"刷新任务列表失败: {e}")

    def clear_completed_tasks(self):
        """清理已完成任务"""
        try:
            reply = QMessageBox.question(
                self, "确认", "确定要清理所有已完成的任务吗？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 这里应该调用清理方法
                self.refresh_task_list()
                self.log_message.emit("🧹 已完成任务清理完成", "info")

        except Exception as e:
            logger.error(f"清理已完成任务失败: {e}")

    def test_accounts(self):
        """测试所有账号"""
        try:
            if not self.accounts:
                QMessageBox.warning(self, "警告", "没有可测试的账号")
                return

            # 显示测试进度
            progress_dialog = QProgressDialog("正在测试账号...", "取消", 0, len(self.accounts), self)
            progress_dialog.setWindowModality(Qt.WindowModal)

            def test_worker():
                for i, account in enumerate(self.accounts):
                    if progress_dialog.wasCanceled():
                        break

                    # 模拟测试过程
                    time.sleep(0.5)
                    progress_dialog.setValue(i + 1)

                progress_dialog.close()
                QMessageBox.information(self, "完成", "账号测试完成")

            test_thread = threading.Thread(target=test_worker, daemon=True)
            test_thread.start()

        except Exception as e:
            logger.error(f"测试账号失败: {e}")

    def test_single_account(self, account):
        """测试单个账号"""
        try:
            # 模拟测试单个账号
            QMessageBox.information(self, "测试结果", f"账号 {account.email} 测试成功")
            self.log_message.emit(f"✅ 账号测试成功: {account.email}", "info")

        except Exception as e:
            logger.error(f"测试账号失败: {e}")
            QMessageBox.critical(self, "测试失败", f"账号 {account.email} 测试失败:\n{e}")

    def update_stats_display(self):
        """更新统计显示"""
        try:
            if not self.sending_manager:
                return

            status = self.sending_manager.get_sending_status()
            queue_stats = status['queue_stats']

            # 更新统计标签
            self.task_stats_labels['total_tasks'].setText(str(queue_stats['total_tasks']))
            self.task_stats_labels['completed_tasks'].setText(str(queue_stats['completed_tasks']))
            self.task_stats_labels['failed_tasks'].setText(str(queue_stats['failed_tasks']))

            # 计算进度
            total = queue_stats['total_tasks']
            completed = queue_stats['completed_tasks']

            if total > 0:
                progress = int((completed / total) * 100)
                self.progress_bar.setValue(progress)
                self.progress_label.setText(f"进度: {progress}%")

                # 计算成功率
                if completed > 0:
                    success_rate = int(((completed - queue_stats['failed_tasks']) / completed) * 100)
                    self.task_stats_labels['success_rate'].setText(f"{success_rate}%")

            # 更新发送速度（模拟）
            self.task_stats_labels['send_speed'].setText("0 封/分钟")
            self.task_stats_labels['remaining_time'].setText("计算中...")

        except Exception as e:
            logger.error(f"更新统计显示失败: {e}")

    def update_time_display(self):
        """更新时间显示"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"🕒 {current_time}")

    def clear_log(self):
        """清空日志"""
        if self.log_text:
            self.log_text.clear()

    def on_log_message(self, message: str, level: str):
        """处理日志消息"""
        if not self.log_text:
            return

        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 根据级别设置颜色
        color_map = {
            "info": "#2ecc71",
            "warning": "#f39c12",
            "error": "#e74c3c",
            "success": "#27ae60"
        }
        color = color_map.get(level, "#ecf0f1")

        formatted_message = f'<span style="color: {color}">[{timestamp}] {message}</span>'
        self.log_text.append(formatted_message)

        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 停止发送
            if self.is_sending:
                self.stop_sending()

            # 停止定时器
            if self.stats_timer.isActive():
                self.stats_timer.stop()

            event.accept()

        except Exception as e:
            logger.error(f"关闭窗口失败: {e}")
            event.accept()
