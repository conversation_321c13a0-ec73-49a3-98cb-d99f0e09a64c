#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务管理界面
提供直观的任务添加、分配、监控和控制功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import time
from typing import Dict, Any, Optional
from datetime import datetime
from loguru import logger

try:
    from ..core.email_sending_manager import EmailSendingManager, SendingConfig, SendingMode, SendingStatus
    from ..core.smart_task_queue import TaskPriority
    from ..core.unified_email_sender import SendingStrategy
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent
    sys.path.insert(0, str(project_root))

    from src.core.email_sending_manager import EmailSendingManager, SendingConfig, SendingMode, SendingStatus
    from src.core.smart_task_queue import TaskPriority
    from src.core.unified_email_sender import SendingStrategy


class TaskManagementWindow:
    """任务管理界面"""
    
    def __init__(self, parent=None):
        """初始化任务管理界面"""
        self.parent = parent
        self.window = None
        
        # 初始化邮件发送管理器
        config = SendingConfig(
            mode=SendingMode.MANUAL,
            strategy=SendingStrategy.ULTRA_FAST,
            concurrent_workers=3,
            send_interval=2.0
        )
        self.sending_manager = EmailSendingManager(config)
        
        # 设置回调
        self.sending_manager.on_sending_started = self._on_sending_started
        self.sending_manager.on_sending_paused = self._on_sending_paused
        self.sending_manager.on_sending_resumed = self._on_sending_resumed
        self.sending_manager.on_sending_completed = self._on_sending_completed
        self.sending_manager.on_status_changed = self._on_status_changed
        
        # 界面变量（将在show方法中初始化）
        self.status_var = None
        self.progress_var = None
        self.speed_var = None
        self.total_tasks_var = None
        self.completed_tasks_var = None
        self.failed_tasks_var = None
        
        # 更新线程
        self.update_thread = None
        self.is_updating = False
        
        logger.info("📋 任务管理界面初始化完成")

    def _init_variables(self):
        """初始化界面变量"""
        self.status_var = tk.StringVar(value="空闲")
        self.progress_var = tk.StringVar(value="0%")
        self.speed_var = tk.StringVar(value="0 封/分钟")
        self.total_tasks_var = tk.StringVar(value="0")
        self.completed_tasks_var = tk.StringVar(value="0")
        self.failed_tasks_var = tk.StringVar(value="0")
    
    def show(self):
        """显示任务管理界面"""
        try:
            # 检查是否已有窗口存在
            if self.window and self.window.winfo_exists():
                self.window.lift()
                self.window.focus_force()
                return

            # 创建新窗口
            self.window = tk.Tk()  # 直接创建主窗口，不依赖parent
            self.window.title("📋 智能任务管理系统")
            self.window.geometry("1200x800")
            self.window.configure(bg='#f0f0f0')

            # 设置窗口属性
            self.window.resizable(True, True)

            # 初始化界面变量
            self._init_variables()

            self._create_widgets()
            self._setup_sender_factory()
            self._start_update_thread()

            # 窗口关闭事件
            self.window.protocol("WM_DELETE_WINDOW", self._on_window_close)

            # 显示窗口
            self.window.lift()
            self.window.focus_force()

            logger.info("✅ 智能任务管理窗口已显示")

        except Exception as e:
            logger.error(f"❌ 显示任务管理窗口失败: {e}")
            import traceback
            traceback.print_exc()

            # 显示错误消息
            try:
                import tkinter.messagebox as messagebox
                messagebox.showerror("错误", f"启动任务管理系统失败:\n{e}")
            except:
                print(f"启动任务管理系统失败: {e}")

    def _setup_sender_factory(self):
        """设置发送器工厂"""
        try:
            from src.core.sender_factory import get_sender_factory

            factory = get_sender_factory()
            factory.initialize()  # 使用模拟驱动器

            self.sending_manager.set_sender_factory(lambda: factory.create_sender(self.config.strategy))
            logger.info("✅ 发送器工厂设置成功")

        except Exception as e:
            logger.error(f"❌ 发送器工厂设置失败: {e}")
            # 设置一个简单的模拟工厂
            def mock_factory():
                try:
                    from src.core.unified_email_sender import UnifiedEmailSender
                    from src.core.sender_factory import MockWebDriver
                    return UnifiedEmailSender(MockWebDriver(), self.config.strategy)
                except Exception as ex:
                    logger.error(f"❌ 模拟工厂创建失败: {ex}")
                    return None

            self.sending_manager.set_sender_factory(mock_factory)
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建各个区域
        self._create_header_frame(main_frame)
        self._create_task_input_frame(main_frame)
        self._create_batch_management_frame(main_frame)
        self._create_sending_control_frame(main_frame)
        self._create_status_frame(main_frame)
        self._create_progress_frame(main_frame)
    
    def _create_header_frame(self, parent):
        """创建标题框架"""
        header_frame = ttk.LabelFrame(parent, text="📋 智能任务管理系统", padding=10)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 状态显示
        status_frame = ttk.Frame(header_frame)
        status_frame.pack(fill=tk.X)
        
        ttk.Label(status_frame, text="系统状态:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                                font=('Arial', 10), foreground='blue')
        status_label.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(status_frame, text="处理进度:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        progress_label = ttk.Label(status_frame, textvariable=self.progress_var, 
                                  font=('Arial', 10), foreground='green')
        progress_label.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(status_frame, text="发送速度:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        speed_label = ttk.Label(status_frame, textvariable=self.speed_var, 
                               font=('Arial', 10), foreground='orange')
        speed_label.pack(side=tk.LEFT, padx=(5, 0))
    
    def _create_task_input_frame(self, parent):
        """创建任务输入框架"""
        input_frame = ttk.LabelFrame(parent, text="📝 任务添加", padding=10)
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 创建笔记本控件
        notebook = ttk.Notebook(input_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 单个任务标签页
        single_frame = ttk.Frame(notebook)
        notebook.add(single_frame, text="单个任务")
        self._create_single_task_frame(single_frame)
        
        # 批量导入标签页
        batch_frame = ttk.Frame(notebook)
        notebook.add(batch_frame, text="批量导入")
        self._create_batch_import_frame(batch_frame)
        
        # 文件导入标签页
        file_frame = ttk.Frame(notebook)
        notebook.add(file_frame, text="文件导入")
        self._create_file_import_frame(file_frame)
    
    def _create_single_task_frame(self, parent):
        """创建单个任务输入框架"""
        # 收件人
        ttk.Label(parent, text="收件人邮箱:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.single_email_entry = ttk.Entry(parent, width=50)
        self.single_email_entry.grid(row=0, column=1, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)
        
        # 主题
        ttk.Label(parent, text="邮件主题:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.single_subject_entry = ttk.Entry(parent, width=50)
        self.single_subject_entry.grid(row=1, column=1, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)
        
        # 内容
        ttk.Label(parent, text="邮件内容:").grid(row=2, column=0, sticky=tk.NW, padx=5, pady=5)
        self.single_content_text = tk.Text(parent, width=50, height=5)
        self.single_content_text.grid(row=2, column=1, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)
        
        # 优先级
        ttk.Label(parent, text="优先级:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.single_priority_combo = ttk.Combobox(parent, values=["低", "普通", "高", "紧急", "关键"], 
                                                 state="readonly", width=15)
        self.single_priority_combo.set("普通")
        self.single_priority_combo.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 添加按钮
        add_single_btn = ttk.Button(parent, text="➕ 添加任务", 
                                   command=self._add_single_task)
        add_single_btn.grid(row=3, column=2, sticky=tk.E, padx=5, pady=5)
        
        # 配置列权重
        parent.columnconfigure(1, weight=1)
    
    def _create_batch_import_frame(self, parent):
        """创建批量导入框架"""
        # 说明
        info_label = ttk.Label(parent, text="请在下方文本框中输入邮件信息，每行一个，格式：邮箱,主题,内容")
        info_label.pack(anchor=tk.W, padx=5, pady=5)
        
        # 批量输入文本框
        self.batch_text = tk.Text(parent, height=10)
        self.batch_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 控制框架
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 批次名称
        ttk.Label(control_frame, text="批次名称:").pack(side=tk.LEFT)
        self.batch_name_entry = ttk.Entry(control_frame, width=20)
        self.batch_name_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        # 优先级
        ttk.Label(control_frame, text="优先级:").pack(side=tk.LEFT)
        self.batch_priority_combo = ttk.Combobox(control_frame, values=["低", "普通", "高", "紧急", "关键"], 
                                                state="readonly", width=10)
        self.batch_priority_combo.set("普通")
        self.batch_priority_combo.pack(side=tk.LEFT, padx=(5, 20))
        
        # 添加按钮
        add_batch_btn = ttk.Button(control_frame, text="📦 添加批次", 
                                  command=self._add_batch_tasks)
        add_batch_btn.pack(side=tk.RIGHT)
    
    def _create_file_import_frame(self, parent):
        """创建文件导入框架"""
        # 文件选择
        file_frame = ttk.Frame(parent)
        file_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(file_frame, text="选择文件:").pack(side=tk.LEFT)
        self.file_path_var = tk.StringVar()
        file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, width=50)
        file_entry.pack(side=tk.LEFT, padx=(5, 5), fill=tk.X, expand=True)
        
        browse_btn = ttk.Button(file_frame, text="📁 浏览", command=self._browse_file)
        browse_btn.pack(side=tk.RIGHT)
        
        # 模板设置
        template_frame = ttk.LabelFrame(parent, text="邮件模板设置", padding=10)
        template_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # 主题模板
        ttk.Label(template_frame, text="主题模板:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.subject_template_entry = ttk.Entry(template_frame, width=60)
        self.subject_template_entry.insert(0, "邮件主题")
        self.subject_template_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        
        # 内容模板
        ttk.Label(template_frame, text="内容模板:").grid(row=1, column=0, sticky=tk.NW, padx=5, pady=5)
        self.content_template_text = tk.Text(template_frame, width=60, height=5)
        self.content_template_text.insert(tk.END, "邮件内容")
        self.content_template_text.grid(row=1, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        
        # 配置列权重
        template_frame.columnconfigure(1, weight=1)
        
        # 导入设置
        import_frame = ttk.Frame(parent)
        import_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 分批大小
        ttk.Label(import_frame, text="分批大小:").pack(side=tk.LEFT)
        self.batch_size_var = tk.StringVar(value="1000")
        batch_size_entry = ttk.Entry(import_frame, textvariable=self.batch_size_var, width=10)
        batch_size_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        # 导入按钮
        import_btn = ttk.Button(import_frame, text="📥 导入文件", 
                               command=self._import_file)
        import_btn.pack(side=tk.RIGHT)
    
    def _create_batch_management_frame(self, parent):
        """创建批次管理框架"""
        batch_frame = ttk.LabelFrame(parent, text="📦 批次管理", padding=10)
        batch_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 批次列表
        columns = ("批次ID", "名称", "状态", "总任务", "已分配", "已完成", "失败", "进度")
        self.batch_tree = ttk.Treeview(batch_frame, columns=columns, show="headings", height=8)
        
        # 设置列标题和宽度
        for col in columns:
            self.batch_tree.heading(col, text=col)
            self.batch_tree.column(col, width=100)
        
        # 滚动条
        batch_scrollbar = ttk.Scrollbar(batch_frame, orient=tk.VERTICAL, command=self.batch_tree.yview)
        self.batch_tree.configure(yscrollcommand=batch_scrollbar.set)
        
        # 布局
        self.batch_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        batch_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 批次操作按钮
        batch_btn_frame = ttk.Frame(batch_frame)
        batch_btn_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(batch_btn_frame, text="🔄 刷新", 
                  command=self._refresh_batch_list).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_btn_frame, text="⏸️ 暂停批次", 
                  command=self._pause_batch).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_btn_frame, text="▶️ 恢复批次", 
                  command=self._resume_batch).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_btn_frame, text="🚫 取消批次", 
                  command=self._cancel_batch).pack(side=tk.LEFT, padx=(0, 5))
    
    def _create_sending_control_frame(self, parent):
        """创建发送控制框架"""
        control_frame = ttk.LabelFrame(parent, text="🚀 发送控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 发送配置
        config_frame = ttk.Frame(control_frame)
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 发送策略
        ttk.Label(config_frame, text="发送策略:").pack(side=tk.LEFT)
        self.strategy_combo = ttk.Combobox(config_frame, values=["超高速", "标准", "安全"], 
                                          state="readonly", width=10)
        self.strategy_combo.set("超高速")
        self.strategy_combo.pack(side=tk.LEFT, padx=(5, 20))
        
        # 并发数
        ttk.Label(config_frame, text="并发数:").pack(side=tk.LEFT)
        self.workers_var = tk.StringVar(value="3")
        workers_entry = ttk.Entry(config_frame, textvariable=self.workers_var, width=5)
        workers_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        # 发送间隔
        ttk.Label(config_frame, text="间隔(秒):").pack(side=tk.LEFT)
        self.interval_var = tk.StringVar(value="2.0")
        interval_entry = ttk.Entry(config_frame, textvariable=self.interval_var, width=8)
        interval_entry.pack(side=tk.LEFT, padx=(5, 0))
        
        # 控制按钮
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X)
        
        self.start_btn = ttk.Button(btn_frame, text="🚀 开始发送", 
                                   command=self._start_sending)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.pause_btn = ttk.Button(btn_frame, text="⏸️ 暂停发送", 
                                   command=self._pause_sending, state=tk.DISABLED)
        self.pause_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.resume_btn = ttk.Button(btn_frame, text="▶️ 恢复发送", 
                                    command=self._resume_sending, state=tk.DISABLED)
        self.resume_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = ttk.Button(btn_frame, text="🛑 停止发送", 
                                  command=self._stop_sending, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
    
    def _create_status_frame(self, parent):
        """创建状态显示框架"""
        status_frame = ttk.LabelFrame(parent, text="📊 发送统计", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 统计信息
        stats_frame = ttk.Frame(status_frame)
        stats_frame.pack(fill=tk.X)
        
        # 总任务数
        ttk.Label(stats_frame, text="总任务:").grid(row=0, column=0, sticky=tk.W, padx=5)
        ttk.Label(stats_frame, textvariable=self.total_tasks_var, 
                 foreground='blue').grid(row=0, column=1, sticky=tk.W, padx=5)
        
        # 已完成
        ttk.Label(stats_frame, text="已完成:").grid(row=0, column=2, sticky=tk.W, padx=5)
        ttk.Label(stats_frame, textvariable=self.completed_tasks_var, 
                 foreground='green').grid(row=0, column=3, sticky=tk.W, padx=5)
        
        # 失败数
        ttk.Label(stats_frame, text="失败:").grid(row=0, column=4, sticky=tk.W, padx=5)
        ttk.Label(stats_frame, textvariable=self.failed_tasks_var, 
                 foreground='red').grid(row=0, column=5, sticky=tk.W, padx=5)
    
    def _create_progress_frame(self, parent):
        """创建进度显示框架"""
        progress_frame = ttk.LabelFrame(parent, text="📈 发送进度", padding=10)
        progress_frame.pack(fill=tk.X)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))
        
        # 进度信息
        progress_info_frame = ttk.Frame(progress_frame)
        progress_info_frame.pack(fill=tk.X)
        
        self.progress_label = ttk.Label(progress_info_frame, text="准备就绪")
        self.progress_label.pack(side=tk.LEFT)
        
        self.eta_label = ttk.Label(progress_info_frame, text="")
        self.eta_label.pack(side=tk.RIGHT)

    def _add_single_task(self):
        """添加单个任务"""
        try:
            email = self.single_email_entry.get().strip()
            subject = self.single_subject_entry.get().strip()
            content = self.single_content_text.get(1.0, tk.END).strip()

            if not email or not subject or not content:
                messagebox.showwarning("警告", "请填写完整的邮件信息")
                return

            # 转换优先级
            priority_map = {"低": TaskPriority.LOW, "普通": TaskPriority.NORMAL,
                           "高": TaskPriority.HIGH, "紧急": TaskPriority.URGENT,
                           "关键": TaskPriority.CRITICAL}
            priority = priority_map.get(self.single_priority_combo.get(), TaskPriority.NORMAL)

            # 添加任务
            task_id = self.sending_manager.add_single_task(email, subject, content, priority)

            if task_id:
                messagebox.showinfo("成功", f"任务添加成功！\n任务ID: {task_id}")
                # 清空输入框
                self.single_email_entry.delete(0, tk.END)
                self.single_subject_entry.delete(0, tk.END)
                self.single_content_text.delete(1.0, tk.END)
            else:
                messagebox.showerror("错误", "任务添加失败")

        except Exception as e:
            logger.error(f"❌ 添加单个任务失败: {e}")
            messagebox.showerror("错误", f"添加任务失败: {e}")

    def _add_batch_tasks(self):
        """添加批量任务"""
        try:
            batch_text = self.batch_text.get(1.0, tk.END).strip()
            if not batch_text:
                messagebox.showwarning("警告", "请输入批量任务数据")
                return

            batch_name = self.batch_name_entry.get().strip()
            if not batch_name:
                batch_name = f"批次_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 转换优先级
            priority_map = {"低": TaskPriority.LOW, "普通": TaskPriority.NORMAL,
                           "高": TaskPriority.HIGH, "紧急": TaskPriority.URGENT,
                           "关键": TaskPriority.CRITICAL}
            priority = priority_map.get(self.batch_priority_combo.get(), TaskPriority.NORMAL)

            # 解析批量数据
            tasks_data = []
            lines = batch_text.split('\n')

            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue

                parts = line.split(',')
                if len(parts) < 3:
                    messagebox.showwarning("警告", f"第{line_num}行格式错误，应为：邮箱,主题,内容")
                    return

                email = parts[0].strip()
                subject = parts[1].strip()
                content = ','.join(parts[2:]).strip()  # 内容可能包含逗号

                tasks_data.append({
                    'to_email': email,
                    'subject': subject,
                    'content': content,
                    'content_type': 'text/plain'
                })

            if not tasks_data:
                messagebox.showwarning("警告", "没有有效的任务数据")
                return

            # 添加批次
            batch_id = self.sending_manager.add_batch_tasks(tasks_data, batch_name, priority)

            if batch_id:
                messagebox.showinfo("成功", f"批次添加成功！\n批次ID: {batch_id}\n任务数量: {len(tasks_data)}")
                # 清空输入
                self.batch_text.delete(1.0, tk.END)
                self.batch_name_entry.delete(0, tk.END)
            else:
                messagebox.showerror("错误", "批次添加失败")

        except Exception as e:
            logger.error(f"❌ 添加批量任务失败: {e}")
            messagebox.showerror("错误", f"添加批量任务失败: {e}")

    def _browse_file(self):
        """浏览文件"""
        file_path = filedialog.askopenfilename(
            title="选择收件人文件",
            filetypes=[
                ("Excel文件", "*.xlsx *.xls"),
                ("CSV文件", "*.csv"),
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.file_path_var.set(file_path)

    def _import_file(self):
        """导入文件"""
        try:
            file_path = self.file_path_var.get().strip()
            if not file_path:
                messagebox.showwarning("警告", "请选择要导入的文件")
                return

            subject_template = self.subject_template_entry.get().strip()
            content_template = self.content_template_text.get(1.0, tk.END).strip()

            if not subject_template:
                subject_template = "邮件主题"
            if not content_template:
                content_template = "邮件内容"

            # 设置批次大小
            try:
                batch_size = int(self.batch_size_var.get())
                if batch_size <= 0:
                    raise ValueError("批次大小必须大于0")
            except ValueError:
                messagebox.showwarning("警告", "请输入有效的批次大小")
                return

            # 更新批次处理器配置
            self.sending_manager.batch_processor.config.batch_size = batch_size

            # 导入文件
            success = self.sending_manager.load_recipients_from_file(
                file_path, subject_template, content_template
            )

            if success:
                messagebox.showinfo("成功", "文件导入成功！\n请点击'开始分批处理'来处理大数据量")

                # 启动分批处理
                if messagebox.askyesno("确认", "是否立即开始分批处理？"):
                    self._start_batch_processing()
            else:
                messagebox.showerror("错误", "文件导入失败，请检查文件格式")

        except Exception as e:
            logger.error(f"❌ 文件导入失败: {e}")
            messagebox.showerror("错误", f"文件导入失败: {e}")

    def _start_batch_processing(self):
        """开始分批处理"""
        try:
            success = self.sending_manager.start_batch_processing()
            if success:
                messagebox.showinfo("成功", "分批处理已启动！\n系统将自动分批添加任务到队列")
            else:
                messagebox.showerror("错误", "分批处理启动失败")
        except Exception as e:
            logger.error(f"❌ 启动分批处理失败: {e}")
            messagebox.showerror("错误", f"启动分批处理失败: {e}")

    def _refresh_batch_list(self):
        """刷新批次列表"""
        try:
            # 清空现有数据
            for item in self.batch_tree.get_children():
                self.batch_tree.delete(item)

            # 获取批次信息
            batches = self.sending_manager.get_batch_list()

            for batch in batches:
                self.batch_tree.insert("", tk.END, values=(
                    batch['batch_id'][:20] + "..." if len(batch['batch_id']) > 20 else batch['batch_id'],
                    batch['name'],
                    batch['status'],
                    batch['total_tasks'],
                    batch['allocated_tasks'],
                    batch['completed_tasks'],
                    batch['failed_tasks'],
                    f"{batch['progress']:.1f}%"
                ))

        except Exception as e:
            logger.error(f"❌ 刷新批次列表失败: {e}")

    def _pause_batch(self):
        """暂停批次"""
        selected = self.batch_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要暂停的批次")
            return

        try:
            item = self.batch_tree.item(selected[0])
            batch_id = item['values'][0]

            success = self.sending_manager.task_queue.pause_batch(batch_id)
            if success:
                messagebox.showinfo("成功", "批次已暂停")
                self._refresh_batch_list()
            else:
                messagebox.showerror("错误", "暂停批次失败")
        except Exception as e:
            logger.error(f"❌ 暂停批次失败: {e}")
            messagebox.showerror("错误", f"暂停批次失败: {e}")

    def _resume_batch(self):
        """恢复批次"""
        selected = self.batch_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要恢复的批次")
            return

        try:
            item = self.batch_tree.item(selected[0])
            batch_id = item['values'][0]

            success = self.sending_manager.task_queue.resume_batch(batch_id)
            if success:
                messagebox.showinfo("成功", "批次已恢复")
                self._refresh_batch_list()
            else:
                messagebox.showerror("错误", "恢复批次失败")
        except Exception as e:
            logger.error(f"❌ 恢复批次失败: {e}")
            messagebox.showerror("错误", f"恢复批次失败: {e}")

    def _cancel_batch(self):
        """取消批次"""
        selected = self.batch_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要取消的批次")
            return

        if not messagebox.askyesno("确认", "确定要取消选中的批次吗？"):
            return

        try:
            item = self.batch_tree.item(selected[0])
            batch_id = item['values'][0]

            success = self.sending_manager.task_queue.cancel_batch(batch_id)
            if success:
                messagebox.showinfo("成功", "批次已取消")
                self._refresh_batch_list()
            else:
                messagebox.showerror("错误", "取消批次失败")
        except Exception as e:
            logger.error(f"❌ 取消批次失败: {e}")
            messagebox.showerror("错误", f"取消批次失败: {e}")

    def _start_sending(self):
        """开始发送"""
        try:
            # 更新配置
            self._update_sending_config()

            # 检查是否有任务
            status = self.sending_manager.get_sending_status()
            if status['queue_stats']['total_tasks'] == 0:
                messagebox.showwarning("警告", "没有待发送的任务，请先添加任务")
                return

            # 设置发送器工厂（这里需要根据实际情况设置）
            if not self.sending_manager.sender_factory:
                messagebox.showwarning("警告", "请先配置发送器")
                return

            success = self.sending_manager.start_sending()
            if success:
                self._update_button_states("sending")
            else:
                messagebox.showerror("错误", "发送启动失败")

        except Exception as e:
            logger.error(f"❌ 启动发送失败: {e}")
            messagebox.showerror("错误", f"启动发送失败: {e}")

    def _pause_sending(self):
        """暂停发送"""
        try:
            self.sending_manager.pause_sending()
            self._update_button_states("paused")
        except Exception as e:
            logger.error(f"❌ 暂停发送失败: {e}")
            messagebox.showerror("错误", f"暂停发送失败: {e}")

    def _resume_sending(self):
        """恢复发送"""
        try:
            self.sending_manager.resume_sending()
            self._update_button_states("sending")
        except Exception as e:
            logger.error(f"❌ 恢复发送失败: {e}")
            messagebox.showerror("错误", f"恢复发送失败: {e}")

    def _stop_sending(self):
        """停止发送"""
        if not messagebox.askyesno("确认", "确定要停止发送吗？"):
            return

        try:
            self.sending_manager.stop_sending()
            self._update_button_states("idle")
        except Exception as e:
            logger.error(f"❌ 停止发送失败: {e}")
            messagebox.showerror("错误", f"停止发送失败: {e}")

    def _update_sending_config(self):
        """更新发送配置"""
        try:
            # 发送策略
            strategy_map = {"超高速": SendingStrategy.ULTRA_FAST,
                           "标准": SendingStrategy.STANDARD,
                           "安全": SendingStrategy.SAFE}
            strategy = strategy_map.get(self.strategy_combo.get(), SendingStrategy.ULTRA_FAST)
            self.sending_manager.config.strategy = strategy

            # 并发数
            workers = int(self.workers_var.get())
            if workers > 0:
                self.sending_manager.config.concurrent_workers = workers

            # 发送间隔
            interval = float(self.interval_var.get())
            if interval >= 0:
                self.sending_manager.config.send_interval = interval

        except ValueError as e:
            logger.warning(f"⚠️ 配置参数错误: {e}")

    def _update_button_states(self, state: str):
        """更新按钮状态"""
        if state == "idle":
            self.start_btn.config(state=tk.NORMAL)
            self.pause_btn.config(state=tk.DISABLED)
            self.resume_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.DISABLED)
        elif state == "sending":
            self.start_btn.config(state=tk.DISABLED)
            self.pause_btn.config(state=tk.NORMAL)
            self.resume_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
        elif state == "paused":
            self.start_btn.config(state=tk.DISABLED)
            self.pause_btn.config(state=tk.DISABLED)
            self.resume_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.NORMAL)

    def _start_update_thread(self):
        """启动更新线程"""
        self.is_updating = True
        self.update_thread = threading.Thread(target=self._update_worker, daemon=True)
        self.update_thread.start()

    def _update_worker(self):
        """更新工作线程"""
        while self.is_updating:
            try:
                # 检查窗口是否还存在
                if self.window and self.window.winfo_exists():
                    self.window.after(0, self._update_display)
                else:
                    break
                time.sleep(2)  # 每2秒更新一次
            except Exception as e:
                logger.error(f"❌ 更新线程异常: {e}")
                time.sleep(5)

    def _update_display(self):
        """更新显示"""
        try:
            status = self.sending_manager.get_sending_status()

            # 更新状态
            self.status_var.set(self._get_status_text(status['status']))

            # 更新统计
            queue_stats = status['queue_stats']
            self.total_tasks_var.set(str(queue_stats['total_tasks']))
            self.completed_tasks_var.set(str(queue_stats['completed_tasks']))
            self.failed_tasks_var.set(str(queue_stats['failed_tasks']))

            # 更新进度
            total = queue_stats['total_tasks']
            completed = queue_stats['completed_tasks'] + queue_stats['failed_tasks']

            if total > 0:
                progress = (completed / total) * 100
                self.progress_var.set(f"{progress:.1f}%")
                self.progress_bar['value'] = progress
            else:
                self.progress_var.set("0%")
                self.progress_bar['value'] = 0

            # 更新速度
            sending_stats = status['sending_stats']
            speed = sending_stats.get('current_speed', 0)
            self.speed_var.set(f"{speed:.1f} 封/分钟")

            # 更新进度标签
            if status['is_sending']:
                remaining = total - completed
                if speed > 0 and remaining > 0:
                    eta_minutes = remaining / speed
                    self.progress_label.config(text=f"正在发送... 剩余 {remaining} 个任务")
                    self.eta_label.config(text=f"预计 {eta_minutes:.1f} 分钟完成")
                else:
                    self.progress_label.config(text="正在发送...")
                    self.eta_label.config(text="")
            else:
                self.progress_label.config(text=self._get_status_text(status['status']))
                self.eta_label.config(text="")

            # 自动刷新批次列表
            if hasattr(self, 'batch_tree'):
                self._refresh_batch_list()

        except Exception as e:
            logger.error(f"❌ 更新显示失败: {e}")

    def _get_status_text(self, status: str) -> str:
        """获取状态文本"""
        status_map = {
            "idle": "空闲",
            "preparing": "准备中",
            "sending": "发送中",
            "paused": "已暂停",
            "completed": "已完成",
            "error": "错误"
        }
        return status_map.get(status, status)

    # 回调函数
    def _on_sending_started(self):
        """发送开始回调"""
        self.window.after(0, lambda: self._update_button_states("sending"))

    def _on_sending_paused(self):
        """发送暂停回调"""
        self.window.after(0, lambda: self._update_button_states("paused"))

    def _on_sending_resumed(self):
        """发送恢复回调"""
        self.window.after(0, lambda: self._update_button_states("sending"))

    def _on_sending_completed(self):
        """发送完成回调"""
        self.window.after(0, lambda: [
            self._update_button_states("idle"),
            messagebox.showinfo("完成", "邮件发送已完成！")
        ])

    def _on_status_changed(self, old_status, new_status):
        """状态变更回调"""
        logger.info(f"📊 发送状态变更: {old_status.value} → {new_status.value}")

    def _on_window_close(self):
        """窗口关闭事件"""
        try:
            self.is_updating = False

            # 停止发送
            if self.sending_manager.is_sending:
                if messagebox.askyesno("确认", "发送正在进行中，确定要关闭吗？"):
                    self.sending_manager.stop_sending()
                else:
                    return

            # 等待更新线程结束
            if self.update_thread and self.update_thread.is_alive():
                self.update_thread.join(timeout=2)

            self.window.destroy()

        except Exception as e:
            logger.error(f"❌ 关闭窗口失败: {e}")
            self.window.destroy()


# 测试函数
def main():
    """测试任务管理界面"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    app = TaskManagementWindow()
    app.show()

    root.mainloop()


if __name__ == "__main__":
    main()
