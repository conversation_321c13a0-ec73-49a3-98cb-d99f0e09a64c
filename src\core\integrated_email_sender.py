#!/usr/bin/env python3
"""
集成邮件发送器
整合任务分配器、浏览器管理器和邮件发送功能
"""

import time
import threading
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass

from src.utils.logger import setup_logger
from src.models.account import Account
from src.core.powerful_task_distributor import PowerfulTaskDistributor, DistributorConfig, EmailTask
from src.core.browser_instance_manager import BrowserInstanceManager, BrowserConfig
from src.core.sina_ultra_fast_sender_final import SinaUltraFastSenderFinal

logger = setup_logger("INFO")

@dataclass
class IntegratedSenderConfig:
    """集成发送器配置"""
    # 浏览器配置
    max_browsers: int = 3
    window_width: int = 800
    window_height: int = 600
    minimize_browsers: bool = True
    headless: bool = False
    
    # 发送配置
    emails_per_account: int = 5
    send_interval: float = 2.0
    switch_cooldown: float = 3.0
    task_timeout: float = 30.0
    max_queue_size_per_browser: int = 10
    
    # 功能开关
    enable_load_balancing: bool = True
    enable_auto_retry: bool = True
    enable_cookie_login: bool = True

class IntegratedEmailSender:
    """集成邮件发送器 - 强大的多浏览器并发发送系统"""
    
    def __init__(self, config: IntegratedSenderConfig):
        self.config = config
        
        # 创建浏览器配置
        browser_config = BrowserConfig(
            max_browsers=config.max_browsers,
            window_width=config.window_width,
            window_height=config.window_height,
            minimize_browsers=config.minimize_browsers,
            headless=config.headless
        )
        
        # 创建分发器配置
        distributor_config = DistributorConfig(
            max_browsers=config.max_browsers,
            emails_per_account=config.emails_per_account,
            send_interval=config.send_interval,
            switch_cooldown=config.switch_cooldown,
            task_timeout=config.task_timeout,
            max_queue_size_per_browser=config.max_queue_size_per_browser,
            enable_load_balancing=config.enable_load_balancing,
            enable_auto_retry=config.enable_auto_retry
        )
        
        # 初始化核心组件
        self.browser_manager = BrowserInstanceManager(browser_config)
        self.task_distributor = PowerfulTaskDistributor(distributor_config)
        
        # 邮件发送器缓存
        self.email_senders: Dict[str, SinaUltraFastSenderFinal] = {}
        
        # 状态管理
        self.is_initialized = False
        self.is_running = False
        self.accounts: List[Account] = []
        
        # 统计信息
        self.start_time = 0
        self.total_sent = 0
        self.total_failed = 0
        
        # 回调函数
        self.status_callbacks: List[Callable] = []
        
        # 设置任务分发器的回调
        self._setup_distributor_callbacks()
        
        logger.info("🚀 集成邮件发送器初始化完成")
    
    def _setup_distributor_callbacks(self):
        """设置任务分发器的回调函数"""
        # 任务状态回调
        self.task_distributor.add_task_callback(self._on_task_status_changed)
        
        # 账号切换回调
        self.task_distributor.add_switch_callback(self._on_account_switched)
        
        # 统计回调
        self.task_distributor.add_stats_callback(self._on_stats_updated)
    
    def initialize(self, accounts: List[Account]) -> bool:
        """初始化发送系统"""
        try:
            logger.info(f"🔧 初始化集成发送系统，账号数量: {len(accounts)}")
            
            if not accounts:
                logger.error("❌ 没有提供账号信息")
                return False
            
            self.accounts = accounts.copy()
            
            # 1. 创建浏览器实例
            logger.info("🔧 创建浏览器实例...")
            browser_instances = self.browser_manager.create_browser_instances(self.config.max_browsers)
            
            if not browser_instances:
                logger.error("❌ 浏览器实例创建失败")
                return False
            
            # 2. 为每个浏览器登录账号
            logger.info("🔧 为浏览器登录账号...")
            self._login_browsers_with_accounts(browser_instances, accounts)
            
            # 3. 初始化任务分发器
            logger.info("🔧 初始化任务分发器...")
            if not self.task_distributor.initialize(accounts, browser_instances):
                logger.error("❌ 任务分发器初始化失败")
                return False
            
            # 4. 创建邮件发送器
            logger.info("🔧 创建邮件发送器...")
            self._create_email_senders(browser_instances)
            
            self.is_initialized = True
            logger.info("✅ 集成发送系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 集成发送系统初始化失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def _login_browsers_with_accounts(self, browser_instances: Dict, accounts: List[Account]):
        """为浏览器登录账号"""
        browser_ids = list(browser_instances.keys())
        
        for i, (browser_id, browser_instance) in enumerate(browser_instances.items()):
            if i < len(accounts):
                account = accounts[i]
                success = self.browser_manager.login_browser_with_account(browser_id, account)
                if success:
                    logger.info(f"✅ 浏览器 {browser_id} 登录成功: {account.email}")
                else:
                    logger.warning(f"⚠️ 浏览器 {browser_id} 登录失败: {account.email}")
            else:
                logger.info(f"ℹ️ 浏览器 {browser_id} 没有对应账号")
    
    def _create_email_senders(self, browser_instances: Dict):
        """创建邮件发送器"""
        for browser_id, browser_instance in browser_instances.items():
            try:
                sender = SinaUltraFastSenderFinal(browser_instance.driver)
                self.email_senders[browser_id] = sender
                logger.info(f"✅ 创建邮件发送器: {browser_id}")
            except Exception as e:
                logger.error(f"❌ 创建邮件发送器失败: {browser_id} - {e}")
    
    def start_sending(self) -> bool:
        """启动发送系统"""
        try:
            if not self.is_initialized:
                logger.error("❌ 系统未初始化，无法启动发送")
                return False
            
            if self.is_running:
                logger.warning("⚠️ 发送系统已在运行")
                return False
            
            logger.info("🚀 启动集成发送系统...")
            
            # 启动任务分发器
            if not self.task_distributor.start_distribution():
                logger.error("❌ 任务分发器启动失败")
                return False
            
            self.is_running = True
            self.start_time = time.time()
            
            logger.info("✅ 集成发送系统启动完成")
            
            # 触发状态回调
            self._trigger_status_callback("started")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动集成发送失败: {e}")
            return False
    
    def stop_sending(self):
        """停止发送系统"""
        logger.info("🛑 停止集成发送系统...")
        
        self.is_running = False
        
        # 停止任务分发器
        self.task_distributor.stop_distribution()
        
        logger.info("✅ 集成发送系统已停止")
        
        # 触发状态回调
        self._trigger_status_callback("stopped")
    
    def add_email_task(self, to_email: str, subject: str, content: str, priority: int = 1) -> str:
        """添加邮件任务"""
        if not self.is_running:
            logger.error("❌ 发送系统未运行，无法添加任务")
            return ""
        
        task_id = self.task_distributor.add_email_task(to_email, subject, content, priority)
        logger.info(f"📝 添加邮件任务: {task_id} -> {to_email}")
        return task_id
    
    def add_batch_tasks(self, email_list: List[Tuple[str, str, str]], priority: int = 1) -> List[str]:
        """批量添加邮件任务"""
        if not self.is_running:
            logger.error("❌ 发送系统未运行，无法添加任务")
            return []
        
        task_ids = self.task_distributor.add_batch_tasks(email_list, priority)
        logger.info(f"📝 批量添加 {len(email_list)} 个邮件任务")
        return task_ids
    
    def _on_task_status_changed(self, event_type: str, task: EmailTask):
        """处理任务状态变化"""
        try:
            if event_type == "sending":
                # 执行实际的邮件发送
                self._execute_email_sending(task)
            elif event_type == "completed":
                self.total_sent += 1
            elif event_type == "failed":
                self.total_failed += 1
                
        except Exception as e:
            logger.error(f"❌ 处理任务状态变化异常: {e}")
    
    def _execute_email_sending(self, task: EmailTask):
        """执行实际的邮件发送"""
        try:
            if not task.assigned_browser:
                logger.error(f"❌ 任务 {task.task_id} 没有分配浏览器")
                return False
            
            # 获取对应的邮件发送器
            sender = self.email_senders.get(task.assigned_browser)
            if not sender:
                logger.error(f"❌ 浏览器 {task.assigned_browser} 没有邮件发送器")
                return False
            
            # 执行发送
            logger.info(f"📧 发送邮件: {task.task_id} -> {task.to_email} (浏览器: {task.assigned_browser})")
            
            success = sender.send_email_ultra_fast(
                to_email=task.to_email,
                subject=task.subject,
                content=task.content
            )
            
            if success:
                logger.info(f"✅ 邮件发送成功: {task.task_id}")
            else:
                logger.error(f"❌ 邮件发送失败: {task.task_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 执行邮件发送异常: {e}")
            task.error_message = str(e)
            return False
    
    def _on_account_switched(self, browser_id: str, old_account: str, new_account: str, success: bool):
        """处理账号切换事件"""
        if success:
            logger.info(f"🔄 账号切换成功: 浏览器 {browser_id}, {old_account} -> {new_account}")
            
            # 重新登录浏览器
            account = next((acc for acc in self.accounts if acc.email == new_account), None)
            if account:
                self.browser_manager.switch_browser_account(browser_id, account)
        else:
            logger.error(f"❌ 账号切换失败: 浏览器 {browser_id}, {old_account} -> {new_account}")
    
    def _on_stats_updated(self, stats: Dict[str, Any]):
        """处理统计信息更新"""
        # 触发状态回调
        self._trigger_status_callback("stats_updated", stats)
    
    def _trigger_status_callback(self, event_type: str, data: Any = None):
        """触发状态回调"""
        for callback in self.status_callbacks:
            try:
                callback(event_type, data)
            except Exception as e:
                logger.error(f"❌ 状态回调异常: {e}")
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        # 获取任务分发器统计
        distributor_stats = self.task_distributor.get_comprehensive_stats()
        
        # 获取浏览器管理器统计
        browser_stats = self.browser_manager.get_instance_stats()
        
        # 计算运行时间
        runtime = time.time() - self.start_time if self.start_time > 0 else 0
        
        return {
            'system_status': {
                'is_initialized': self.is_initialized,
                'is_running': self.is_running,
                'runtime_seconds': runtime,
                'start_time': self.start_time
            },
            'sending_stats': {
                'total_sent': self.total_sent,
                'total_failed': self.total_failed,
                'success_rate': (self.total_sent / max(self.total_sent + self.total_failed, 1)) * 100
            },
            'distributor_stats': distributor_stats,
            'browser_stats': browser_stats
        }
    
    def get_browser_status(self) -> Dict[str, Dict]:
        """获取浏览器状态"""
        return self.task_distributor.get_comprehensive_stats().get('browser_details', {})
    
    def force_account_switch(self, browser_id: str) -> bool:
        """强制切换指定浏览器的账号"""
        return self.task_distributor.force_account_switch(browser_id)
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        return self.task_distributor.get_task_status(task_id)
    
    def add_status_callback(self, callback: Callable):
        """添加状态回调函数"""
        self.status_callbacks.append(callback)
    
    def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理集成发送器资源...")
        
        # 停止发送
        if self.is_running:
            self.stop_sending()
        
        # 清理任务分发器
        self.task_distributor.cleanup()
        
        # 清理浏览器管理器
        self.browser_manager.cleanup_all_instances()
        
        # 清理邮件发送器
        self.email_senders.clear()
        
        logger.info("✅ 资源清理完成")
    
    def is_system_healthy(self) -> bool:
        """检查系统健康状态"""
        if not self.is_initialized or not self.is_running:
            return False
        
        # 检查是否有活跃的浏览器
        stats = self.get_comprehensive_stats()
        active_browsers = stats['distributor_stats']['browser_stats']['active_browsers']
        
        return active_browsers > 0
