#!/usr/bin/env python3
"""
并发多浏览器管理器 - 真正的多账号并发发送
每个浏览器独立工作，支持多个账号同时发送邮件
"""

import time
import threading
import queue
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict
import uuid

from src.utils.logger import setup_logger
from src.models.account import Account
from src.models.browser_instance import BrowserInstance
from src.core.multi_browser_manager import MultiBrowserManager, SendingConfig
from src.core.ultra_speed_cookie_manager import UltraSpeedCookieManager
from src.core.sina_ultra_fast_sender_final import SinaUltraFastSenderFinal

logger = setup_logger("INFO")

@dataclass
class BrowserWorkUnit:
    """浏览器工作单元 - 每个浏览器的独立工作环境"""
    browser_id: str
    browser_instance: BrowserInstance
    assigned_accounts: List[Account] = field(default_factory=list)
    current_account_index: int = 0
    current_account: Optional[Account] = None
    sent_count_per_account: Dict[str, int] = field(default_factory=dict)  # 每个账号的发送计数
    task_queue: queue.Queue = field(default_factory=queue.Queue)
    is_working: bool = False
    worker_thread: Optional[threading.Thread] = None
    last_send_time: float = 0
    total_sent: int = 0
    total_failed: int = 0
    
    def get_current_account_sent_count(self) -> int:
        """获取当前账号的发送数量"""
        if self.current_account:
            return self.sent_count_per_account.get(self.current_account.email, 0)
        return 0
    
    def increment_sent_count(self):
        """增加当前账号的发送计数"""
        if self.current_account:
            current_count = self.sent_count_per_account.get(self.current_account.email, 0)
            self.sent_count_per_account[self.current_account.email] = current_count + 1
            self.total_sent += 1
    
    def should_switch_account(self, emails_per_account: int) -> bool:
        """判断是否需要切换账号"""
        if not self.current_account:
            return True
        return self.get_current_account_sent_count() >= emails_per_account

@dataclass
class EmailTask:
    """邮件发送任务"""
    task_id: str
    to_email: str
    subject: str
    content: str
    priority: int = 1
    created_time: float = field(default_factory=time.time)
    retry_count: int = 0
    max_retries: int = 2

@dataclass
class ConcurrentStats:
    """并发发送统计"""
    total_browsers: int = 0
    active_browsers: int = 0
    total_accounts: int = 0
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    start_time: float = 0
    emails_per_minute: float = 0
    account_switch_count: int = 0
    browser_stats: Dict[str, Dict] = field(default_factory=dict)

class ConcurrentMultiBrowserManager:
    """并发多浏览器管理器 - 真正的多账号并发发送"""
    
    def __init__(self, config: SendingConfig):
        self.config = config
        self.browser_manager = MultiBrowserManager(config)
        
        # 浏览器工作单元
        self.browser_work_units: Dict[str, BrowserWorkUnit] = {}
        
        # 全局任务分发器
        self.global_task_queue = queue.Queue()
        self.task_distributor_thread: Optional[threading.Thread] = None
        
        # 统计和状态
        self.stats = ConcurrentStats()
        self.is_running = False
        self.is_stopping = False
        
        # 账号池管理
        self.all_accounts: List[Account] = []
        self.account_assignment_lock = threading.Lock()
        
        logger.info("🚀 并发多浏览器管理器初始化完成")
    
    def initialize(self, accounts: List[Account]) -> bool:
        """初始化并发多浏览器系统"""
        try:
            logger.info(f"🔧 初始化并发多浏览器系统，账号数量: {len(accounts)}")
            
            # 1. 初始化浏览器管理器
            if not self.browser_manager.initialize_browsers():
                logger.error("❌ 浏览器管理器初始化失败")
                return False
            
            # 2. 保存账号列表
            self.all_accounts = accounts.copy()
            
            # 3. 创建浏览器工作单元
            self._create_browser_work_units()
            
            # 4. 分配账号到浏览器
            self._assign_accounts_to_browsers()
            
            # 5. 初始化每个浏览器的第一个账号
            self._initialize_browser_accounts()
            
            # 6. 更新统计信息
            self.stats.total_browsers = len(self.browser_work_units)
            self.stats.total_accounts = len(accounts)
            
            logger.info(f"✅ 并发多浏览器系统初始化完成")
            logger.info(f"📊 浏览器数量: {self.stats.total_browsers}, 账号数量: {self.stats.total_accounts}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 并发多浏览器系统初始化失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def _create_browser_work_units(self):
        """创建浏览器工作单元"""
        logger.info("🔧 创建浏览器工作单元...")
        
        for browser_id, browser_instance in self.browser_manager.browsers.items():
            work_unit = BrowserWorkUnit(
                browser_id=browser_id,
                browser_instance=browser_instance
            )
            self.browser_work_units[browser_id] = work_unit
            logger.info(f"✅ 创建工作单元: {browser_id}")
    
    def _assign_accounts_to_browsers(self):
        """智能分配账号到浏览器"""
        logger.info("🔧 智能分配账号到浏览器...")
        
        browser_count = len(self.browser_work_units)
        if browser_count == 0:
            logger.error("❌ 没有可用的浏览器工作单元")
            return
        
        # 平均分配账号到每个浏览器
        accounts_per_browser = len(self.all_accounts) // browser_count
        remaining_accounts = len(self.all_accounts) % browser_count
        
        account_index = 0
        for i, (browser_id, work_unit) in enumerate(self.browser_work_units.items()):
            # 计算这个浏览器应该分配的账号数量
            accounts_for_this_browser = accounts_per_browser
            if i < remaining_accounts:
                accounts_for_this_browser += 1
            
            # 分配账号
            for j in range(accounts_for_this_browser):
                if account_index < len(self.all_accounts):
                    work_unit.assigned_accounts.append(self.all_accounts[account_index])
                    account_index += 1
            
            logger.info(f"📋 浏览器 {browser_id} 分配账号数量: {len(work_unit.assigned_accounts)}")
            for account in work_unit.assigned_accounts:
                logger.info(f"  - {account.email}")
    
    def _initialize_browser_accounts(self):
        """初始化每个浏览器的第一个账号"""
        logger.info("🔧 初始化每个浏览器的第一个账号...")
        
        for browser_id, work_unit in self.browser_work_units.items():
            if work_unit.assigned_accounts:
                # 设置第一个账号为当前账号
                work_unit.current_account = work_unit.assigned_accounts[0]
                work_unit.current_account_index = 0
                
                # 初始化发送计数
                for account in work_unit.assigned_accounts:
                    work_unit.sent_count_per_account[account.email] = 0
                
                logger.info(f"✅ 浏览器 {browser_id} 初始账号: {work_unit.current_account.email}")
            else:
                logger.warning(f"⚠️ 浏览器 {browser_id} 没有分配到账号")
    
    def start_concurrent_sending(self) -> bool:
        """启动并发发送"""
        try:
            if self.is_running:
                logger.warning("⚠️ 并发发送已在运行")
                return False
            
            logger.info("🚀 启动并发发送系统...")
            
            self.is_running = True
            self.is_stopping = False
            self.stats.start_time = time.time()
            
            # 1. 启动任务分发器
            self._start_task_distributor()
            
            # 2. 启动每个浏览器的工作线程
            self._start_browser_workers()
            
            logger.info("✅ 并发发送系统启动完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动并发发送失败: {e}")
            return False
    
    def _start_task_distributor(self):
        """启动任务分发器"""
        logger.info("🔧 启动任务分发器...")
        
        self.task_distributor_thread = threading.Thread(
            target=self._task_distributor_worker,
            name="TaskDistributor",
            daemon=True
        )
        self.task_distributor_thread.start()
        logger.info("✅ 任务分发器启动完成")
    
    def _start_browser_workers(self):
        """启动所有浏览器工作线程"""
        logger.info("🔧 启动浏览器工作线程...")
        
        for browser_id, work_unit in self.browser_work_units.items():
            if work_unit.assigned_accounts:  # 只启动有账号的浏览器
                work_unit.worker_thread = threading.Thread(
                    target=self._browser_worker,
                    args=(work_unit,),
                    name=f"BrowserWorker-{browser_id}",
                    daemon=True
                )
                work_unit.worker_thread.start()
                work_unit.is_working = True
                self.stats.active_browsers += 1
                logger.info(f"✅ 启动浏览器工作线程: {browser_id}")
        
        logger.info(f"✅ 所有浏览器工作线程启动完成，活跃浏览器: {self.stats.active_browsers}")

    def _task_distributor_worker(self):
        """任务分发器工作线程 - 智能分发任务到最合适的浏览器"""
        logger.info("🔧 任务分发器开始工作")

        while self.is_running and not self.is_stopping:
            try:
                # 从全局队列获取任务
                try:
                    task = self.global_task_queue.get(timeout=1.0)
                except queue.Empty:
                    continue

                # 找到最合适的浏览器
                best_browser = self._find_best_browser_for_task(task)

                if best_browser:
                    # 将任务分发到选定的浏览器
                    best_browser.task_queue.put(task)
                    logger.info(f"📤 任务分发: {task.task_id} -> {best_browser.browser_id}")
                else:
                    # 没有可用浏览器，将任务放回全局队列
                    self.global_task_queue.put(task)
                    time.sleep(0.5)  # 等待浏览器可用

                self.global_task_queue.task_done()

            except Exception as e:
                logger.error(f"❌ 任务分发器异常: {e}")
                time.sleep(1)

        logger.info("🛑 任务分发器停止工作")

    def _find_best_browser_for_task(self, task: EmailTask) -> Optional[BrowserWorkUnit]:
        """找到最适合处理任务的浏览器"""
        best_browser = None
        min_queue_size = float('inf')

        for work_unit in self.browser_work_units.values():
            # 检查浏览器是否可用
            if not work_unit.is_working or not work_unit.current_account:
                continue

            # 检查是否需要切换账号
            if work_unit.should_switch_account(self.config.emails_per_account):
                # 尝试切换到下一个账号
                if not self._switch_to_next_account(work_unit):
                    continue  # 没有更多账号可用

            # 选择队列最短的浏览器
            queue_size = work_unit.task_queue.qsize()
            if queue_size < min_queue_size:
                min_queue_size = queue_size
                best_browser = work_unit

        return best_browser

    def _browser_worker(self, work_unit: BrowserWorkUnit):
        """浏览器工作线程 - 处理分配给该浏览器的任务"""
        worker_name = f"Worker-{work_unit.browser_id}"
        logger.info(f"🔧 {worker_name} 开始工作")

        consecutive_failures = 0
        max_consecutive_failures = 3

        while self.is_running and not self.is_stopping and work_unit.is_working:
            try:
                # 从浏览器专用队列获取任务
                try:
                    task = work_unit.task_queue.get(timeout=1.0)
                except queue.Empty:
                    continue

                # 检查是否需要切换账号
                if work_unit.should_switch_account(self.config.emails_per_account):
                    logger.info(f"🔄 {worker_name} 需要切换账号 (已发送 {work_unit.get_current_account_sent_count()} 封)")
                    if not self._switch_to_next_account(work_unit):
                        logger.warning(f"⚠️ {worker_name} 没有更多账号可用，将任务放回全局队列")
                        self.global_task_queue.put(task)
                        work_unit.task_queue.task_done()
                        time.sleep(5)  # 等待其他浏览器处理
                        continue

                # 检查发送间隔
                current_time = time.time()
                if current_time - work_unit.last_send_time < self.config.send_interval:
                    # 还没到发送时间，将任务放回队列
                    work_unit.task_queue.put(task)
                    work_unit.task_queue.task_done()
                    time.sleep(0.1)
                    continue

                # 执行发送任务
                logger.info(f"🚀 {worker_name} 处理任务: {task.task_id} -> {task.to_email} (账号: {work_unit.current_account.email})")

                start_time = time.time()
                success = self._send_email_with_browser(work_unit, task)
                elapsed = time.time() - start_time

                if success:
                    # 发送成功
                    work_unit.increment_sent_count()
                    work_unit.last_send_time = current_time
                    consecutive_failures = 0
                    self.stats.completed_tasks += 1

                    logger.info(f"✅ {worker_name} 任务完成: {task.task_id} ({elapsed:.2f}s) "
                              f"[账号 {work_unit.current_account.email} 已发送 {work_unit.get_current_account_sent_count()} 封]")
                else:
                    # 发送失败
                    work_unit.total_failed += 1
                    consecutive_failures += 1
                    self.stats.failed_tasks += 1

                    logger.error(f"❌ {worker_name} 任务失败: {task.task_id}")

                    # 重试逻辑
                    if task.retry_count < task.max_retries:
                        task.retry_count += 1
                        self.global_task_queue.put(task)  # 放回全局队列重试
                        logger.info(f"🔄 任务重试: {task.task_id} (第 {task.retry_count} 次)")

                work_unit.task_queue.task_done()

                # 检查连续失败
                if consecutive_failures >= max_consecutive_failures:
                    logger.error(f"❌ {worker_name} 连续失败 {consecutive_failures} 次，暂停工作")
                    time.sleep(10)
                    consecutive_failures = 0

            except Exception as e:
                logger.error(f"❌ {worker_name} 处理任务异常: {e}")
                consecutive_failures += 1
                try:
                    work_unit.task_queue.task_done()
                except:
                    pass

        logger.info(f"🛑 {worker_name} 停止工作")

    def _switch_to_next_account(self, work_unit: BrowserWorkUnit) -> bool:
        """切换到下一个可用账号"""
        try:
            if not work_unit.assigned_accounts:
                return False

            # 找到下一个账号
            next_index = (work_unit.current_account_index + 1) % len(work_unit.assigned_accounts)

            # 检查是否已经轮询完所有账号
            if next_index == 0 and work_unit.current_account_index != 0:
                # 已经轮询完一轮，检查是否还有未达到发送限制的账号
                available_accounts = []
                for i, account in enumerate(work_unit.assigned_accounts):
                    sent_count = work_unit.sent_count_per_account.get(account.email, 0)
                    if sent_count < self.config.emails_per_account:
                        available_accounts.append((i, account))

                if not available_accounts:
                    logger.warning(f"⚠️ 浏览器 {work_unit.browser_id} 所有账号都已达到发送限制")
                    return False

                # 选择第一个可用账号
                next_index, next_account = available_accounts[0]
            else:
                next_account = work_unit.assigned_accounts[next_index]

            old_account = work_unit.current_account.email if work_unit.current_account else "None"

            # 执行账号切换
            success = self._perform_account_switch(work_unit, next_account)

            if success:
                work_unit.current_account = next_account
                work_unit.current_account_index = next_index
                self.stats.account_switch_count += 1

                logger.info(f"🔄 浏览器 {work_unit.browser_id} 账号切换成功: {old_account} -> {next_account.email}")
                return True
            else:
                logger.error(f"❌ 浏览器 {work_unit.browser_id} 账号切换失败: {old_account} -> {next_account.email}")
                return False

        except Exception as e:
            logger.error(f"❌ 账号切换异常: {e}")
            return False

    def _perform_account_switch(self, work_unit: BrowserWorkUnit, new_account: Account) -> bool:
        """执行实际的账号切换操作"""
        try:
            # 使用无缝账号切换器
            if hasattr(self.browser_manager, 'seamless_switcher'):
                result = self.browser_manager.seamless_switcher.seamless_switch_account(
                    work_unit.browser_instance,
                    new_account
                )
                return result.success if hasattr(result, 'success') else result
            else:
                # 回退到传统Cookie切换
                return self.browser_manager.load_account_cookies(work_unit.browser_instance, new_account)

        except Exception as e:
            logger.error(f"❌ 执行账号切换失败: {e}")
            return False

    def _send_email_with_browser(self, work_unit: BrowserWorkUnit, task: EmailTask) -> bool:
        """使用指定浏览器发送邮件"""
        try:
            # 获取或创建发送器
            sender = self._get_or_create_sender(work_unit)
            if not sender:
                return False

            # 执行发送 - 使用第一步策略（5步逻辑复刻）
            from src.core.unified_email_sender import UnifiedEmailSender, SendingStrategy

            # 创建统一发送器，使用第一步策略
            unified_sender = UnifiedEmailSender(work_unit.browser_instance.driver, SendingStrategy.ULTRA_FAST)

            # 使用第一步策略发送（严格按照5步顺序）
            result = unified_sender.send_email(
                to_email=task.to_email,
                subject=task.subject,
                content=task.content
            )

            return result.success

        except Exception as e:
            logger.error(f"❌ 发送邮件异常: {e}")
            return False

    def _get_or_create_sender(self, work_unit: BrowserWorkUnit) -> Optional[SinaUltraFastSenderFinal]:
        """获取或创建发送器"""
        try:
            # 这里可以缓存发送器实例
            return SinaUltraFastSenderFinal(work_unit.browser_instance.driver)
        except Exception as e:
            logger.error(f"❌ 创建发送器失败: {e}")
            return None

    def add_email_task(self, to_email: str, subject: str, content: str, priority: int = 1) -> str:
        """添加邮件任务到全局队列"""
        task_id = f"task_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"

        task = EmailTask(
            task_id=task_id,
            to_email=to_email,
            subject=subject,
            content=content,
            priority=priority
        )

        self.global_task_queue.put(task)
        self.stats.total_tasks += 1

        logger.info(f"📝 添加邮件任务: {task_id} -> {to_email}")
        return task_id

    def add_batch_tasks(self, email_list: List[Tuple[str, str, str]]) -> List[str]:
        """批量添加邮件任务"""
        task_ids = []

        for to_email, subject, content in email_list:
            task_id = self.add_email_task(to_email, subject, content)
            task_ids.append(task_id)

        logger.info(f"📝 批量添加 {len(email_list)} 个邮件任务")
        return task_ids

    def stop_concurrent_sending(self):
        """停止并发发送"""
        logger.info("🛑 停止并发发送系统...")

        self.is_stopping = True
        self.is_running = False

        # 等待任务分发器停止
        if self.task_distributor_thread and self.task_distributor_thread.is_alive():
            self.task_distributor_thread.join(timeout=5)

        # 等待所有浏览器工作线程停止
        for work_unit in self.browser_work_units.values():
            if work_unit.worker_thread and work_unit.worker_thread.is_alive():
                work_unit.is_working = False
                work_unit.worker_thread.join(timeout=5)

        logger.info("✅ 并发发送系统已停止")

    def get_concurrent_stats(self) -> Dict[str, Any]:
        """获取并发发送统计信息"""
        # 计算运行时间
        runtime = time.time() - self.stats.start_time if self.stats.start_time > 0 else 0

        # 计算每分钟发送数量
        if runtime > 0:
            self.stats.emails_per_minute = (self.stats.completed_tasks / runtime) * 60

        # 收集每个浏览器的统计
        browser_stats = {}
        for browser_id, work_unit in self.browser_work_units.items():
            browser_stats[browser_id] = {
                'current_account': work_unit.current_account.email if work_unit.current_account else None,
                'total_sent': work_unit.total_sent,
                'total_failed': work_unit.total_failed,
                'queue_size': work_unit.task_queue.qsize(),
                'is_working': work_unit.is_working,
                'account_sent_counts': work_unit.sent_count_per_account.copy()
            }

        self.stats.browser_stats = browser_stats

        return {
            'total_browsers': self.stats.total_browsers,
            'active_browsers': self.stats.active_browsers,
            'total_accounts': self.stats.total_accounts,
            'total_tasks': self.stats.total_tasks,
            'completed_tasks': self.stats.completed_tasks,
            'failed_tasks': self.stats.failed_tasks,
            'emails_per_minute': self.stats.emails_per_minute,
            'account_switch_count': self.stats.account_switch_count,
            'runtime_seconds': runtime,
            'browser_stats': browser_stats,
            'global_queue_size': self.global_task_queue.qsize()
        }

    def get_browser_status(self) -> Dict[str, Dict]:
        """获取所有浏览器的状态"""
        status = {}

        for browser_id, work_unit in self.browser_work_units.items():
            status[browser_id] = {
                'browser_id': browser_id,
                'is_working': work_unit.is_working,
                'current_account': work_unit.current_account.email if work_unit.current_account else None,
                'assigned_accounts': [acc.email for acc in work_unit.assigned_accounts],
                'current_account_sent': work_unit.get_current_account_sent_count(),
                'total_sent': work_unit.total_sent,
                'total_failed': work_unit.total_failed,
                'queue_size': work_unit.task_queue.qsize(),
                'last_send_time': work_unit.last_send_time,
                'needs_account_switch': work_unit.should_switch_account(self.config.emails_per_account)
            }

        return status

    def force_account_switch(self, browser_id: str) -> bool:
        """强制指定浏览器切换账号"""
        if browser_id not in self.browser_work_units:
            logger.error(f"❌ 浏览器 {browser_id} 不存在")
            return False

        work_unit = self.browser_work_units[browser_id]
        return self._switch_to_next_account(work_unit)

    def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理并发多浏览器管理器资源...")

        # 停止发送
        self.stop_concurrent_sending()

        # 清理浏览器管理器
        if hasattr(self.browser_manager, 'cleanup'):
            self.browser_manager.cleanup()

        logger.info("✅ 资源清理完成")
