#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无缝账号切换器
实现Cookie管理和IP代理切换的无缝账号切换，无需点击退出账号
"""

import time
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from .cookie_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .proxy_manager import ProxyManager
from .ultra_speed_cookie_manager import UltraSpeedCookieManager
from src.models.account import Account
from src.models.browser_instance import BrowserInstance

logger = logging.getLogger(__name__)

@dataclass
class AccountSwitchResult:
    """账号切换结果"""
    success: bool
    old_account: Optional[str] = None
    new_account: Optional[str] = None
    old_proxy: Optional[str] = None
    new_proxy: Optional[str] = None
    switch_time: float = 0.0
    error_message: Optional[str] = None

class SeamlessAccountSwitcher:
    """无缝账号切换器 - 通过Cookie和代理切换实现无缝切换"""
    
    def __init__(self, cookie_manager: <PERSON><PERSON><PERSON>, proxy_manager: Proxy<PERSON>anager):
        self.cookie_manager = cookie_manager
        self.proxy_manager = proxy_manager
        self.ultra_cookie_manager = UltraSpeedCookieManager(cookie_manager)
        
        # 账号切换统计
        self.switch_stats = {
            'total_switches': 0,
            'successful_switches': 0,
            'failed_switches': 0,
            'avg_switch_time': 0.0
        }
        
        logger.info("🔄 无缝账号切换器初始化完成")
    
    def seamless_switch_account(self, browser_instance: BrowserInstance, 
                              new_account: Account, 
                              switch_proxy: bool = True) -> AccountSwitchResult:
        """
        无缝切换账号 - 核心方法
        
        Args:
            browser_instance: 浏览器实例
            new_account: 新账号
            switch_proxy: 是否同时切换代理
            
        Returns:
            AccountSwitchResult: 切换结果
        """
        start_time = time.time()
        old_account = browser_instance.current_account.email if browser_instance.current_account else None
        old_proxy = browser_instance.proxy_info.proxy_url if browser_instance.proxy_info else None
        
        try:
            logger.info(f"🔄 开始无缝切换账号: {old_account} -> {new_account.email}")
            
            # 第一步：切换代理IP（如果需要）
            new_proxy = None
            if switch_proxy:
                proxy_result = self._switch_proxy_for_account(browser_instance, new_account)
                if not proxy_result.success:
                    return AccountSwitchResult(
                        success=False,
                        old_account=old_account,
                        new_account=new_account.email,
                        old_proxy=old_proxy,
                        error_message=f"代理切换失败: {proxy_result.error_message}"
                    )
                new_proxy = proxy_result.new_proxy
            
            # 第二步：清除当前页面的Cookie和会话
            self._clear_browser_session(browser_instance)
            
            # 第三步：加载新账号的Cookie
            cookie_result = self._load_account_cookies(browser_instance, new_account)
            if not cookie_result.success:
                return AccountSwitchResult(
                    success=False,
                    old_account=old_account,
                    new_account=new_account.email,
                    old_proxy=old_proxy,
                    new_proxy=new_proxy,
                    error_message=f"Cookie加载失败: {cookie_result.error_message}"
                )
            
            # 第四步：验证账号切换是否成功
            verification_result = self._verify_account_switch(browser_instance, new_account)
            if not verification_result.success:
                return AccountSwitchResult(
                    success=False,
                    old_account=old_account,
                    new_account=new_account.email,
                    old_proxy=old_proxy,
                    new_proxy=new_proxy,
                    error_message=f"账号验证失败: {verification_result.error_message}"
                )
            
            # 第五步：更新浏览器实例状态
            self._update_browser_instance(browser_instance, new_account)
            
            # 记录切换成功
            switch_time = time.time() - start_time
            self._record_switch_success(switch_time)
            
            logger.info(f"✅ 无缝账号切换成功: {old_account} -> {new_account.email} ({switch_time:.2f}s)")
            
            return AccountSwitchResult(
                success=True,
                old_account=old_account,
                new_account=new_account.email,
                old_proxy=old_proxy,
                new_proxy=new_proxy,
                switch_time=switch_time
            )
            
        except Exception as e:
            switch_time = time.time() - start_time
            self._record_switch_failure()
            
            error_msg = f"无缝账号切换异常: {e}"
            logger.error(error_msg)
            
            return AccountSwitchResult(
                success=False,
                old_account=old_account,
                new_account=new_account.email,
                old_proxy=old_proxy,
                switch_time=switch_time,
                error_message=error_msg
            )
    
    def _switch_proxy_for_account(self, browser_instance: BrowserInstance, 
                                 new_account: Account) -> AccountSwitchResult:
        """为账号切换代理IP"""
        try:
            logger.info(f"🌐 为账号切换代理: {new_account.email}")
            
            # 获取当前代理
            old_proxy = browser_instance.proxy_info.proxy_url if browser_instance.proxy_info else None
            
            # 为新账号轮换代理
            if self.proxy_manager.rotate_proxy_for_account(new_account.email, "account_switch"):
                # 获取新代理信息
                new_proxy_info = self.proxy_manager.get_proxy_for_account(new_account.email)
                if new_proxy_info:
                    # 更新浏览器代理设置
                    self._update_browser_proxy(browser_instance, new_proxy_info)
                    
                    logger.info(f"✅ 代理切换成功: {old_proxy} -> {new_proxy_info.proxy_url}")
                    return AccountSwitchResult(
                        success=True,
                        old_proxy=old_proxy,
                        new_proxy=new_proxy_info.proxy_url
                    )
            
            return AccountSwitchResult(
                success=False,
                error_message="无法获取新代理"
            )
            
        except Exception as e:
            return AccountSwitchResult(
                success=False,
                error_message=str(e)
            )
    
    def _clear_browser_session(self, browser_instance: BrowserInstance):
        """清除浏览器会话 - 不退出账号，只清除Cookie和缓存"""
        try:
            logger.info("🧹 清除浏览器会话...")
            
            driver = browser_instance.driver
            
            # 清除所有Cookie
            driver.delete_all_cookies()
            
            # 清除本地存储
            try:
                driver.execute_script("localStorage.clear();")
                driver.execute_script("sessionStorage.clear();")
            except:
                pass  # 某些页面可能不支持
            
            # 清除缓存（通过刷新页面）
            driver.refresh()
            time.sleep(1)
            
            logger.info("✅ 浏览器会话清除完成")
            
        except Exception as e:
            logger.warning(f"⚠️ 清除浏览器会话部分失败: {e}")
    
    def _load_account_cookies(self, browser_instance: BrowserInstance, 
                            new_account: Account) -> AccountSwitchResult:
        """加载新账号的Cookie"""
        try:
            logger.info(f"🍪 加载账号Cookie: {new_account.email}")
            
            # 使用超极速Cookie管理器加载Cookie
            success = self.ultra_cookie_manager.ultra_speed_cookie_login(
                browser_instance.driver,
                new_account.email
            )
            
            if success:
                logger.info(f"✅ Cookie加载成功: {new_account.email}")
                return AccountSwitchResult(success=True)
            else:
                return AccountSwitchResult(
                    success=False,
                    error_message="Cookie加载失败"
                )
                
        except Exception as e:
            return AccountSwitchResult(
                success=False,
                error_message=str(e)
            )
    
    def _verify_account_switch(self, browser_instance: BrowserInstance, 
                             new_account: Account) -> AccountSwitchResult:
        """验证账号切换是否成功"""
        try:
            logger.info(f"🔍 验证账号切换: {new_account.email}")
            
            driver = browser_instance.driver
            
            # 导航到邮箱主页验证登录状态
            driver.get("https://mail.sina.com.cn/")
            time.sleep(2)
            
            # 检查是否成功登录
            try:
                # 查找用户信息元素
                user_elements = [
                    "//span[contains(@class, 'username')]",
                    "//div[contains(@class, 'user-info')]",
                    "//a[contains(@href, 'logout')]",
                    "//span[contains(text(), '@')]"
                ]
                
                for selector in user_elements:
                    try:
                        element = driver.find_element("xpath", selector)
                        if element and element.is_displayed():
                            logger.info(f"✅ 账号验证成功: {new_account.email}")
                            return AccountSwitchResult(success=True)
                    except:
                        continue
                
                # 如果没有找到用户信息，可能需要重新登录
                return AccountSwitchResult(
                    success=False,
                    error_message="未找到登录状态标识"
                )
                
            except Exception as e:
                return AccountSwitchResult(
                    success=False,
                    error_message=f"验证过程异常: {e}"
                )
                
        except Exception as e:
            return AccountSwitchResult(
                success=False,
                error_message=str(e)
            )
    
    def _update_browser_proxy(self, browser_instance: BrowserInstance, proxy_info):
        """更新浏览器代理设置"""
        try:
            # 注意：这里需要重启浏览器才能应用新代理
            # 或者使用支持动态代理切换的方法
            browser_instance.proxy_info = proxy_info
            logger.info(f"✅ 浏览器代理已更新: {proxy_info.proxy_url}")
            
        except Exception as e:
            logger.warning(f"⚠️ 更新浏览器代理失败: {e}")
    
    def _update_browser_instance(self, browser_instance: BrowserInstance, new_account: Account):
        """更新浏览器实例状态"""
        browser_instance.current_account = new_account
        browser_instance.sent_count = 0  # 重置发送计数
        browser_instance.last_activity = time.time()
        browser_instance.status = "ready"
        
        logger.info(f"✅ 浏览器实例状态已更新: {new_account.email}")
    
    def _record_switch_success(self, switch_time: float):
        """记录切换成功"""
        self.switch_stats['total_switches'] += 1
        self.switch_stats['successful_switches'] += 1
        
        # 更新平均切换时间
        total_successful = self.switch_stats['successful_switches']
        current_avg = self.switch_stats['avg_switch_time']
        self.switch_stats['avg_switch_time'] = (
            (current_avg * (total_successful - 1) + switch_time) / total_successful
        )
    
    def _record_switch_failure(self):
        """记录切换失败"""
        self.switch_stats['total_switches'] += 1
        self.switch_stats['failed_switches'] += 1
    
    def get_switch_stats(self) -> dict:
        """获取切换统计"""
        return self.switch_stats.copy()
    
    def can_switch_seamlessly(self, browser_instance: BrowserInstance, 
                            new_account: Account) -> bool:
        """检查是否可以进行无缝切换"""
        try:
            # 检查Cookie是否存在
            if not self.cookie_manager.has_valid_cookies(new_account.email):
                logger.warning(f"⚠️ 账号 {new_account.email} 没有有效Cookie")
                return False
            
            # 检查代理是否可用
            proxy_info = self.proxy_manager.get_proxy_for_account(new_account.email)
            if not proxy_info or not proxy_info.is_active:
                logger.warning(f"⚠️ 账号 {new_account.email} 没有可用代理")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 检查无缝切换条件失败: {e}")
            return False
