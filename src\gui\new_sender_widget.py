#!/usr/bin/env python3
"""
新的发送界面 - 重新架构版本
集成任务队列管理系统，实现任务添加和发送分离
"""

import sys
import os
import time
from typing import List, Dict, Any, Optional
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.task_queue_manager import TaskQueueManager, EmailTask, TaskStatus
from src.core.email_sender_controller import EmailSenderController
from src.core.multi_browser_manager import SendingConfig
from src.gui.task_queue_widget import TaskQueueWidget
from src.gui.send_statistics_widget import SendStatisticsWidget
from src.gui.send_record_widget import SendRecordWidget
from src.models.database import DatabaseManager
from src.utils.logger import setup_logger

logger = setup_logger("INFO")

class NewSenderWidget(QWidget):
    """新的发送界面"""
    
    # 信号
    log_message = pyqtSignal(str, str)  # 日志消息 (message, level)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 核心组件
        self.task_manager = TaskQueueManager(max_concurrent_tasks=3, batch_size=10)
        self.sender_controller = None
        self.accounts = []
        
        # 发送状态
        self.is_sending = False
        self.batch_size = 10  # 每批发送数量
        
        # 当前变量
        self.current_variables = {}
        
        self.init_ui()
        self.load_accounts()
        
    def init_ui(self):
        """初始化界面"""
        layout = QHBoxLayout(self)
        
        # 左侧：邮件编辑区域
        left_panel = self.create_left_panel()
        layout.addWidget(left_panel, 1)
        
        # 右侧：任务队列区域
        right_panel = self.create_right_panel()
        layout.addWidget(right_panel, 2)
        
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #495057;
            }
        """)
    
    def create_left_panel(self) -> QWidget:
        """创建左侧邮件编辑面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 邮件编辑区域
        mail_group = QGroupBox("📧 邮件编辑")
        mail_layout = QVBoxLayout(mail_group)
        
        # 收件人
        recipient_layout = QHBoxLayout()
        recipient_layout.addWidget(QLabel("收件人:"))
        self.to_email_edit = QLineEdit()
        self.to_email_edit.setPlaceholderText("输入邮箱地址，多个邮箱用分号分隔")
        recipient_layout.addWidget(self.to_email_edit)
        
        # 批量导入按钮
        self.import_btn = QPushButton("📁 批量导入")
        self.import_btn.clicked.connect(self.import_recipients)
        recipient_layout.addWidget(self.import_btn)
        
        mail_layout.addLayout(recipient_layout)
        
        # 主题
        subject_layout = QHBoxLayout()
        subject_layout.addWidget(QLabel("主题:"))
        self.subject_edit = QLineEdit()
        self.subject_edit.setPlaceholderText("邮件主题")
        subject_layout.addWidget(self.subject_edit)
        mail_layout.addLayout(subject_layout)
        
        # 内容类型
        content_type_layout = QHBoxLayout()
        content_type_layout.addWidget(QLabel("内容类型:"))
        self.content_type_combo = QComboBox()
        self.content_type_combo.addItems(["HTML", "纯文本"])
        content_type_layout.addWidget(self.content_type_combo)
        content_type_layout.addStretch()
        mail_layout.addLayout(content_type_layout)
        
        # 邮件内容
        mail_layout.addWidget(QLabel("邮件内容:"))
        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("输入邮件内容...")
        self.content_edit.setMinimumHeight(200)
        mail_layout.addWidget(self.content_edit)
        
        layout.addWidget(mail_group)
        
        # 变量设置区域
        var_group = QGroupBox("🔧 变量设置")
        var_layout = QVBoxLayout(var_group)
        
        var_btn_layout = QHBoxLayout()
        self.variables_btn = QPushButton("📝 设置变量")
        self.variables_btn.clicked.connect(self.show_variables_dialog)
        var_btn_layout.addWidget(self.variables_btn)
        var_btn_layout.addStretch()
        var_layout.addLayout(var_btn_layout)
        
        self.variables_label = QLabel("当前变量: 无")
        self.variables_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        var_layout.addWidget(self.variables_label)
        
        layout.addWidget(var_group)
        
        # 操作按钮区域
        action_group = QGroupBox("⚡ 操作控制")
        action_layout = QVBoxLayout(action_group)
        
        # 任务操作按钮
        task_btn_layout = QHBoxLayout()
        
        self.add_task_btn = QPushButton("➕ 添加任务")
        self.add_task_btn.clicked.connect(self.add_email_task)
        self.add_task_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        task_btn_layout.addWidget(self.add_task_btn)
        
        self.clear_form_btn = QPushButton("🧹 清空表单")
        self.clear_form_btn.clicked.connect(self.clear_form)
        task_btn_layout.addWidget(self.clear_form_btn)
        
        action_layout.addLayout(task_btn_layout)
        
        # 发送控制按钮
        send_btn_layout = QHBoxLayout()
        
        self.start_send_btn = QPushButton("🚀 开始发送")
        self.start_send_btn.clicked.connect(self.start_sending)
        self.start_send_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        send_btn_layout.addWidget(self.start_send_btn)
        
        self.pause_send_btn = QPushButton("⏸️ 暂停发送")
        self.pause_send_btn.clicked.connect(self.pause_sending)
        self.pause_send_btn.setEnabled(False)
        send_btn_layout.addWidget(self.pause_send_btn)
        
        self.stop_send_btn = QPushButton("⏹️ 停止发送")
        self.stop_send_btn.clicked.connect(self.stop_sending)
        self.stop_send_btn.setEnabled(False)
        send_btn_layout.addWidget(self.stop_send_btn)
        
        action_layout.addLayout(send_btn_layout)
        
        # 批量设置
        batch_layout = QHBoxLayout()
        batch_layout.addWidget(QLabel("每批发送数量:"))
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 100)
        self.batch_size_spin.setValue(self.batch_size)
        self.batch_size_spin.valueChanged.connect(self.on_batch_size_changed)
        batch_layout.addWidget(self.batch_size_spin)
        batch_layout.addStretch()
        action_layout.addLayout(batch_layout)
        
        layout.addWidget(action_group)
        
        layout.addStretch()
        return panel
    
    def create_right_panel(self) -> QWidget:
        """创建右侧任务队列和统计面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # 创建标签页
        self.right_tab_widget = QTabWidget()

        # 任务队列标签页
        queue_panel = QWidget()
        queue_layout = QVBoxLayout(queue_panel)

        # 任务队列标题
        title_layout = QHBoxLayout()
        title_label = QLabel("📋 任务队列管理")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin: 10px 0;
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        # 发送统计
        self.send_stats_label = QLabel("发送统计: 等待启动")
        self.send_stats_label.setStyleSheet("""
            color: #6c757d;
            font-size: 12px;
            background-color: white;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        """)
        title_layout.addWidget(self.send_stats_label)

        queue_layout.addLayout(title_layout)

        # 任务队列组件
        self.task_queue_widget = TaskQueueWidget(self.task_manager)
        queue_layout.addWidget(self.task_queue_widget)

        self.right_tab_widget.addTab(queue_panel, "📋 任务队列")

        # 发送统计标签页
        try:
            # 获取数据库路径
            from pathlib import Path
            db_path = Path(__file__).parent.parent.parent / "data" / "sina_email_automation.db"

            self.statistics_widget = SendStatisticsWidget(str(db_path))
            self.right_tab_widget.addTab(self.statistics_widget, "📊 发送统计")
        except Exception as e:
            logger.error(f"创建统计界面失败: {e}")
            # 创建简单的统计显示
            stats_panel = QWidget()
            stats_layout = QVBoxLayout(stats_panel)
            stats_layout.addWidget(QLabel("统计功能暂时不可用"))
            self.right_tab_widget.addTab(stats_panel, "📊 发送统计")

        # 发送记录标签页
        try:
            # 获取数据库路径
            from pathlib import Path
            db_path = Path(__file__).parent.parent.parent / "data" / "sina_email_automation.db"

            self.record_widget = SendRecordWidget(str(db_path))
            self.right_tab_widget.addTab(self.record_widget, "📋 发送记录")
        except Exception as e:
            logger.error(f"创建记录界面失败: {e}")
            # 创建简单的记录显示
            record_panel = QWidget()
            record_layout = QVBoxLayout(record_panel)
            record_layout.addWidget(QLabel("记录功能暂时不可用"))
            self.right_tab_widget.addTab(record_panel, "📋 发送记录")

        layout.addWidget(self.right_tab_widget)

        return panel
    
    def load_accounts(self):
        """加载账号"""
        try:
            from pathlib import Path
            db_path = Path(__file__).parent.parent.parent / "data" / "sina_email_automation.db"
            
            if not db_path.exists():
                self.log_message.emit("数据库文件不存在", "error")
                return
            
            db_manager = DatabaseManager(str(db_path))
            
            # 直接查询数据库
            cursor = db_manager.conn.cursor()
            cursor.execute("SELECT * FROM accounts")
            rows = cursor.fetchall()
            
            # 转换为账号对象
            from src.models.account import Account
            self.accounts = []
            for row in rows:
                account = Account(
                    email=row[1],  # email
                    password=row[2],  # password
                    cookies=row[3] if row[3] else "",  # cookies
                    proxy=row[4] if row[4] else "",  # proxy
                    status=row[5] if row[5] else "active"  # status
                )
                self.accounts.append(account)
            
            self.log_message.emit(f"加载了 {len(self.accounts)} 个账号", "info")
            
        except Exception as e:
            logger.error(f"加载账号失败: {e}")
            self.log_message.emit(f"加载账号失败: {e}", "error")
    
    def add_email_task(self):
        """添加邮件任务"""
        to_emails = self.to_email_edit.text().strip()
        subject = self.subject_edit.text().strip()
        content = self.content_edit.toPlainText().strip()
        content_type = self.content_type_combo.currentText().lower()
        
        if not all([to_emails, subject, content]):
            QMessageBox.warning(self, "警告", "请填写完整的邮件信息")
            return
        
        # 处理多个收件人
        email_list = [email.strip() for email in to_emails.split(';') if email.strip()]
        
        added_count = 0
        for to_email in email_list:
            # 应用变量替换
            final_subject = self.apply_variables(subject, to_email)
            final_content = self.apply_variables(content, to_email)
            
            # 添加任务
            task_id = self.task_manager.add_task(to_email, final_subject, final_content, content_type)
            added_count += 1
        
        self.log_message.emit(f"添加了 {added_count} 个任务到队列", "info")
        
        # 清空表单（可选）
        if added_count > 0:
            self.clear_form()
    
    def apply_variables(self, text: str, email: str) -> str:
        """应用变量替换"""
        # 基本变量
        basic_vars = {
            'email': email,
            'name': email.split('@')[0],  # 默认用邮箱前缀作为姓名
        }
        
        # 合并用户设置的变量
        all_vars = {**basic_vars, **self.current_variables}
        
        # 执行变量替换
        for var_name, var_value in all_vars.items():
            placeholder = f"{{{var_name}}}"
            text = text.replace(placeholder, str(var_value))
        
        return text
    
    def clear_form(self):
        """清空表单"""
        self.to_email_edit.clear()
        self.subject_edit.clear()
        self.content_edit.clear()
    
    def start_sending(self):
        """开始发送"""
        if self.is_sending:
            QMessageBox.information(self, "提示", "发送器已在运行中")
            return
        
        if not self.accounts:
            QMessageBox.warning(self, "警告", "没有可用的账号，请先导入账号")
            return
        
        # 检查是否有待发送任务
        pending_tasks = self.task_manager.get_tasks_by_status(TaskStatus.PENDING)
        if not pending_tasks:
            QMessageBox.information(self, "提示", "没有待发送的任务")
            return
        
        try:
            # 创建发送配置
            config = SendingConfig(
                max_browsers=3,
                send_interval=2.0,
                emails_per_account=20,
                browser_timeout=60,
                page_load_timeout=30,
                element_timeout=15
            )

            # 获取数据库路径
            from pathlib import Path
            db_path = Path(__file__).parent.parent.parent / "data" / "sina_email_automation.db"

            # 创建发送控制器
            self.sender_controller = EmailSenderController(self.task_manager, config, str(db_path))
            
            # 设置回调
            self.sender_controller.on_send_progress = self.on_send_progress
            self.sender_controller.on_send_stats_updated = self.on_send_stats_updated
            self.sender_controller.on_record_created = self.on_record_created
            
            # 初始化
            if not self.sender_controller.initialize(self.accounts):
                QMessageBox.critical(self, "错误", "发送器初始化失败")
                return
            
            # 启动发送
            if self.sender_controller.start_sending():
                self.is_sending = True
                self.update_send_buttons()
                self.log_message.emit("邮件发送已启动", "info")
            else:
                QMessageBox.critical(self, "错误", "启动发送失败")
                
        except Exception as e:
            logger.error(f"启动发送失败: {e}")
            QMessageBox.critical(self, "错误", f"启动发送失败: {e}")
    
    def pause_sending(self):
        """暂停发送"""
        if self.sender_controller:
            self.sender_controller.pause_sending()
            self.log_message.emit("邮件发送已暂停", "info")
    
    def resume_sending(self):
        """恢复发送"""
        if self.sender_controller:
            self.sender_controller.resume_sending()
            self.log_message.emit("邮件发送已恢复", "info")
    
    def stop_sending(self):
        """停止发送"""
        if self.sender_controller:
            self.sender_controller.stop_sending()
            self.sender_controller = None
            self.is_sending = False
            self.update_send_buttons()
            self.log_message.emit("邮件发送已停止", "info")
    
    def update_send_buttons(self):
        """更新发送按钮状态"""
        self.start_send_btn.setEnabled(not self.is_sending)
        self.pause_send_btn.setEnabled(self.is_sending)
        self.stop_send_btn.setEnabled(self.is_sending)
    
    def on_batch_size_changed(self, value: int):
        """批量大小改变"""
        self.batch_size = value
        self.task_manager.batch_size = value
    
    def on_send_progress(self, task: EmailTask, success: bool):
        """发送进度回调"""
        status = "成功" if success else "失败"
        self.log_message.emit(f"邮件发送{status}: {task.to_email}", "info" if success else "error")
    
    def on_send_stats_updated(self, stats: Dict[str, Any]):
        """发送统计更新回调"""
        stats_text = (
            f"成功: {stats['sent_success']} | "
            f"失败: {stats['sent_failed']} | "
            f"速率: {stats['send_rate']:.1f}/分钟"
        )
        self.send_stats_label.setText(f"发送统计: {stats_text}")

        # 更新统计界面
        if hasattr(self, 'statistics_widget'):
            try:
                self.statistics_widget.refresh_statistics()
            except Exception as e:
                logger.error(f"更新统计界面失败: {e}")

    def on_record_created(self, record):
        """发送记录创建回调"""
        try:
            # 更新统计界面
            if hasattr(self, 'statistics_widget'):
                self.statistics_widget.refresh_statistics()

            # 更新记录界面
            if hasattr(self, 'record_widget'):
                self.record_widget.load_records()

            # 记录日志
            status = "成功" if record.status.value == "success" else "失败"
            self.log_message.emit(f"记录已保存: {record.to_email} - {status}", "info")

        except Exception as e:
            logger.error(f"处理记录创建回调失败: {e}")
    
    def import_recipients(self):
        """批量导入收件人"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择收件人文件", "",
            "CSV Files (*.csv);;Excel Files (*.xlsx);;Text Files (*.txt)"
        )
        
        if not file_path:
            return
        
        try:
            # 这里可以实现文件导入逻辑
            # 暂时简化处理
            QMessageBox.information(self, "提示", "批量导入功能开发中...")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入失败: {e}")
    
    def show_variables_dialog(self):
        """显示变量设置对话框"""
        # 这里可以实现变量设置对话框
        QMessageBox.information(self, "提示", "变量设置功能开发中...")
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.is_sending:
            reply = QMessageBox.question(
                self, "确认", "发送正在进行中，确定要关闭吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.No:
                event.ignore()
                return
            
            self.stop_sending()
        
        # 清理资源
        if self.sender_controller:
            self.sender_controller.cleanup()
        
        self.task_manager.stop()
        event.accept()
