#!/usr/bin/env python3
"""
强大的任务分配系统
实现真正的多浏览器、多账号并发发送，支持智能任务分发、实时监控、账号切换
"""

import time
import threading
import queue
import uuid
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, field
from collections import defaultdict
from enum import Enum

from src.utils.logger import setup_logger
from src.models.account import Account
from src.models.browser_instance import BrowserInstance

logger = setup_logger("INFO")

class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    ASSIGNED = "assigned"
    SENDING = "sending"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"

class BrowserStatus(Enum):
    """浏览器状态"""
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    SWITCHING = "switching"

@dataclass
class EmailTask:
    """邮件任务"""
    task_id: str
    to_email: str
    subject: str
    content: str
    priority: int = 1
    status: TaskStatus = TaskStatus.PENDING
    assigned_browser: Optional[str] = None
    assigned_account: Optional[str] = None
    created_time: float = field(default_factory=time.time)
    assigned_time: Optional[float] = None
    start_time: Optional[float] = None
    complete_time: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 2
    error_message: Optional[str] = None

@dataclass
class BrowserWorker:
    """浏览器工作单元"""
    browser_id: str
    browser_instance: BrowserInstance
    current_account: Optional[Account] = None
    assigned_accounts: List[Account] = field(default_factory=list)
    current_account_index: int = 0
    
    # 发送统计
    total_sent: int = 0
    total_failed: int = 0
    account_sent_counts: Dict[str, int] = field(default_factory=dict)
    
    # 状态管理
    status: BrowserStatus = BrowserStatus.IDLE
    last_send_time: float = 0
    last_switch_time: float = 0
    
    # 任务队列
    task_queue: queue.Queue = field(default_factory=queue.Queue)
    worker_thread: Optional[threading.Thread] = None
    is_working: bool = False
    
    def get_current_account_sent_count(self) -> int:
        """获取当前账号的发送数量"""
        if self.current_account:
            return self.account_sent_counts.get(self.current_account.email, 0)
        return 0
    
    def increment_sent_count(self):
        """增加当前账号的发送计数"""
        if self.current_account:
            current_count = self.account_sent_counts.get(self.current_account.email, 0)
            self.account_sent_counts[self.current_account.email] = current_count + 1
            self.total_sent += 1
    
    def should_switch_account(self, emails_per_account: int) -> bool:
        """判断是否需要切换账号"""
        if not self.current_account:
            return True
        return self.get_current_account_sent_count() >= emails_per_account

@dataclass
class DistributorConfig:
    """分配器配置"""
    max_browsers: int = 3
    emails_per_account: int = 5
    send_interval: float = 2.0
    switch_cooldown: float = 3.0
    task_timeout: float = 30.0
    max_queue_size_per_browser: int = 10
    enable_load_balancing: bool = True
    enable_auto_retry: bool = True

@dataclass
class DistributorStats:
    """分配器统计"""
    total_tasks: int = 0
    pending_tasks: int = 0
    assigned_tasks: int = 0
    sending_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    
    total_browsers: int = 0
    active_browsers: int = 0
    idle_browsers: int = 0
    
    total_accounts: int = 0
    active_accounts: int = 0
    
    total_switches: int = 0
    successful_switches: int = 0
    failed_switches: int = 0
    
    start_time: float = 0
    emails_per_minute: float = 0

class PowerfulTaskDistributor:
    """强大的任务分配系统"""
    
    def __init__(self, config: DistributorConfig):
        self.config = config
        
        # 核心组件
        self.browser_workers: Dict[str, BrowserWorker] = {}
        self.all_accounts: List[Account] = []
        
        # 任务管理
        self.global_task_queue = queue.PriorityQueue()
        self.tasks: Dict[str, EmailTask] = {}
        
        # 分发器线程
        self.distributor_thread: Optional[threading.Thread] = None
        self.is_running = False
        self.is_stopping = False
        
        # 统计信息
        self.stats = DistributorStats()
        
        # 回调函数
        self.task_callbacks: List[Callable] = []
        self.switch_callbacks: List[Callable] = []
        self.stats_callbacks: List[Callable] = []
        
        # 线程锁
        self.stats_lock = threading.Lock()
        self.task_lock = threading.Lock()
        
        logger.info("🚀 强大的任务分配系统初始化完成")
    
    def initialize(self, accounts: List[Account], browser_instances: Dict[str, BrowserInstance]) -> bool:
        """初始化分配系统"""
        try:
            logger.info(f"🔧 初始化任务分配系统，账号数量: {len(accounts)}, 浏览器数量: {len(browser_instances)}")
            
            # 保存账号列表
            self.all_accounts = accounts.copy()
            
            # 创建浏览器工作单元
            self._create_browser_workers(browser_instances)
            
            # 分配账号到浏览器
            self._assign_accounts_to_browsers()
            
            # 初始化每个浏览器的第一个账号
            self._initialize_browser_accounts()
            
            # 更新统计信息
            self.stats.total_browsers = len(self.browser_workers)
            self.stats.total_accounts = len(accounts)
            
            logger.info("✅ 任务分配系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 任务分配系统初始化失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def _create_browser_workers(self, browser_instances: Dict[str, BrowserInstance]):
        """创建浏览器工作单元"""
        logger.info("🔧 创建浏览器工作单元...")
        
        for browser_id, browser_instance in browser_instances.items():
            worker = BrowserWorker(
                browser_id=browser_id,
                browser_instance=browser_instance
            )
            self.browser_workers[browser_id] = worker
            logger.info(f"✅ 创建工作单元: {browser_id}")
    
    def _assign_accounts_to_browsers(self):
        """智能分配账号到浏览器"""
        logger.info("🔧 智能分配账号到浏览器...")
        
        browser_count = len(self.browser_workers)
        if browser_count == 0:
            logger.error("❌ 没有可用的浏览器工作单元")
            return
        
        # 平均分配账号到每个浏览器
        accounts_per_browser = len(self.all_accounts) // browser_count
        remaining_accounts = len(self.all_accounts) % browser_count
        
        account_index = 0
        for i, (browser_id, worker) in enumerate(self.browser_workers.items()):
            # 计算这个浏览器应该分配的账号数量
            accounts_for_this_browser = accounts_per_browser
            if i < remaining_accounts:
                accounts_for_this_browser += 1
            
            # 分配账号
            for j in range(accounts_for_this_browser):
                if account_index < len(self.all_accounts):
                    worker.assigned_accounts.append(self.all_accounts[account_index])
                    account_index += 1
            
            logger.info(f"📋 浏览器 {browser_id} 分配账号数量: {len(worker.assigned_accounts)}")
            for account in worker.assigned_accounts:
                logger.info(f"  - {account.email}")
    
    def _initialize_browser_accounts(self):
        """初始化每个浏览器的第一个账号"""
        logger.info("🔧 初始化每个浏览器的第一个账号...")
        
        for browser_id, worker in self.browser_workers.items():
            if worker.assigned_accounts:
                # 设置第一个账号为当前账号
                worker.current_account = worker.assigned_accounts[0]
                worker.current_account_index = 0
                
                # 初始化发送计数
                for account in worker.assigned_accounts:
                    worker.account_sent_counts[account.email] = 0
                
                logger.info(f"✅ 浏览器 {browser_id} 初始账号: {worker.current_account.email}")
            else:
                logger.warning(f"⚠️ 浏览器 {browser_id} 没有分配到账号")
    
    def start_distribution(self) -> bool:
        """启动任务分发"""
        try:
            if self.is_running:
                logger.warning("⚠️ 任务分发已在运行")
                return False
            
            logger.info("🚀 启动任务分发系统...")
            
            self.is_running = True
            self.is_stopping = False
            self.stats.start_time = time.time()
            
            # 启动分发器线程
            self._start_distributor()
            
            # 启动所有浏览器工作线程
            self._start_browser_workers()
            
            logger.info("✅ 任务分发系统启动完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动任务分发失败: {e}")
            return False
    
    def _start_distributor(self):
        """启动分发器线程"""
        logger.info("🔧 启动任务分发器...")
        
        self.distributor_thread = threading.Thread(
            target=self._distributor_worker,
            name="TaskDistributor",
            daemon=True
        )
        self.distributor_thread.start()
        logger.info("✅ 任务分发器启动完成")
    
    def _start_browser_workers(self):
        """启动所有浏览器工作线程"""
        logger.info("🔧 启动浏览器工作线程...")
        
        for browser_id, worker in self.browser_workers.items():
            if worker.assigned_accounts:  # 只启动有账号的浏览器
                worker.worker_thread = threading.Thread(
                    target=self._browser_worker,
                    args=(worker,),
                    name=f"BrowserWorker-{browser_id}",
                    daemon=True
                )
                worker.worker_thread.start()
                worker.is_working = True
                self.stats.active_browsers += 1
                logger.info(f"✅ 启动浏览器工作线程: {browser_id}")
        
        logger.info(f"✅ 所有浏览器工作线程启动完成，活跃浏览器: {self.stats.active_browsers}")
    
    def _distributor_worker(self):
        """分发器工作线程 - 智能分发任务到最合适的浏览器"""
        logger.info("🔧 任务分发器开始工作")
        
        while self.is_running and not self.is_stopping:
            try:
                # 从全局队列获取任务
                try:
                    priority, task_id = self.global_task_queue.get(timeout=1.0)
                    task = self.tasks.get(task_id)
                    if not task:
                        continue
                except queue.Empty:
                    continue
                
                # 找到最合适的浏览器
                best_worker = self._find_best_browser_for_task(task)
                
                if best_worker:
                    # 分配任务到选定的浏览器
                    task.assigned_browser = best_worker.browser_id
                    task.assigned_account = best_worker.current_account.email if best_worker.current_account else None
                    task.assigned_time = time.time()
                    task.status = TaskStatus.ASSIGNED
                    
                    best_worker.task_queue.put(task)
                    
                    with self.stats_lock:
                        self.stats.assigned_tasks += 1
                        self.stats.pending_tasks -= 1
                    
                    logger.info(f"📤 任务分发: {task.task_id} -> {best_worker.browser_id} (账号: {task.assigned_account})")
                    
                    # 触发回调
                    self._trigger_task_callback("assigned", task)
                else:
                    # 没有可用浏览器，将任务放回全局队列
                    self.global_task_queue.put((priority, task_id))
                    time.sleep(0.5)  # 等待浏览器可用
                
                self.global_task_queue.task_done()
                
            except Exception as e:
                logger.error(f"❌ 任务分发器异常: {e}")
                time.sleep(1)
        
        logger.info("🛑 任务分发器停止工作")

    def _find_best_browser_for_task(self, task: EmailTask) -> Optional[BrowserWorker]:
        """找到最适合处理任务的浏览器"""
        best_worker = None
        min_queue_size = float('inf')

        for worker in self.browser_workers.values():
            # 检查浏览器是否可用
            if not worker.is_working or worker.status == BrowserStatus.ERROR:
                continue

            if not worker.current_account:
                continue

            # 检查是否需要切换账号
            if worker.should_switch_account(self.config.emails_per_account):
                # 尝试切换到下一个账号
                if not self._can_switch_to_next_account(worker):
                    continue  # 没有更多账号可用

            # 检查队列大小限制
            queue_size = worker.task_queue.qsize()
            if queue_size >= self.config.max_queue_size_per_browser:
                continue

            # 选择队列最短的浏览器（负载均衡）
            if self.config.enable_load_balancing:
                if queue_size < min_queue_size:
                    min_queue_size = queue_size
                    best_worker = worker
            else:
                # 简单轮询
                return worker

        return best_worker

    def _can_switch_to_next_account(self, worker: BrowserWorker) -> bool:
        """检查是否可以切换到下一个账号"""
        if not worker.assigned_accounts:
            return False

        # 检查是否还有未达到发送限制的账号
        for account in worker.assigned_accounts:
            sent_count = worker.account_sent_counts.get(account.email, 0)
            if sent_count < self.config.emails_per_account:
                return True

        return False

    def _browser_worker(self, worker: BrowserWorker):
        """浏览器工作线程 - 处理分配给该浏览器的任务"""
        worker_name = f"Worker-{worker.browser_id}"
        logger.info(f"🔧 {worker_name} 开始工作")

        consecutive_failures = 0
        max_consecutive_failures = 3

        while self.is_running and not self.is_stopping and worker.is_working:
            try:
                # 从浏览器专用队列获取任务
                try:
                    task = worker.task_queue.get(timeout=1.0)
                except queue.Empty:
                    worker.status = BrowserStatus.IDLE
                    continue

                worker.status = BrowserStatus.BUSY

                # 检查是否需要切换账号
                if worker.should_switch_account(self.config.emails_per_account):
                    logger.info(f"🔄 {worker_name} 需要切换账号 (已发送 {worker.get_current_account_sent_count()} 封)")
                    if not self._switch_to_next_account(worker):
                        logger.warning(f"⚠️ {worker_name} 没有更多账号可用，将任务放回全局队列")
                        self._return_task_to_global_queue(task)
                        worker.task_queue.task_done()
                        time.sleep(5)  # 等待其他浏览器处理
                        continue

                # 检查发送间隔
                current_time = time.time()
                if current_time - worker.last_send_time < self.config.send_interval:
                    # 还没到发送时间，将任务放回队列
                    worker.task_queue.put(task)
                    worker.task_queue.task_done()
                    time.sleep(0.1)
                    continue

                # 执行发送任务
                logger.info(f"🚀 {worker_name} 处理任务: {task.task_id} -> {task.to_email} (账号: {worker.current_account.email})")

                task.status = TaskStatus.SENDING
                task.start_time = current_time

                with self.stats_lock:
                    self.stats.sending_tasks += 1
                    self.stats.assigned_tasks -= 1

                # 触发回调
                self._trigger_task_callback("sending", task)

                start_time = time.time()
                success = self._send_email_with_browser(worker, task)
                elapsed = time.time() - start_time

                if success:
                    # 发送成功
                    worker.increment_sent_count()
                    worker.last_send_time = current_time
                    consecutive_failures = 0

                    task.status = TaskStatus.COMPLETED
                    task.complete_time = time.time()

                    with self.stats_lock:
                        self.stats.completed_tasks += 1
                        self.stats.sending_tasks -= 1

                    logger.info(f"✅ {worker_name} 任务完成: {task.task_id} ({elapsed:.2f}s) "
                              f"[账号 {worker.current_account.email} 已发送 {worker.get_current_account_sent_count()} 封]")

                    # 触发回调
                    self._trigger_task_callback("completed", task)
                else:
                    # 发送失败
                    worker.total_failed += 1
                    consecutive_failures += 1

                    task.status = TaskStatus.FAILED
                    task.complete_time = time.time()

                    with self.stats_lock:
                        self.stats.failed_tasks += 1
                        self.stats.sending_tasks -= 1

                    logger.error(f"❌ {worker_name} 任务失败: {task.task_id}")

                    # 重试逻辑
                    if self.config.enable_auto_retry and task.retry_count < task.max_retries:
                        task.retry_count += 1
                        task.status = TaskStatus.RETRYING
                        self._return_task_to_global_queue(task)
                        logger.info(f"🔄 任务重试: {task.task_id} (第 {task.retry_count} 次)")

                    # 触发回调
                    self._trigger_task_callback("failed", task)

                worker.task_queue.task_done()

                # 检查连续失败
                if consecutive_failures >= max_consecutive_failures:
                    logger.error(f"❌ {worker_name} 连续失败 {consecutive_failures} 次，暂停工作")
                    worker.status = BrowserStatus.ERROR
                    time.sleep(10)
                    consecutive_failures = 0
                    worker.status = BrowserStatus.IDLE

            except Exception as e:
                logger.error(f"❌ {worker_name} 处理任务异常: {e}")
                consecutive_failures += 1
                worker.status = BrowserStatus.ERROR
                try:
                    worker.task_queue.task_done()
                except:
                    pass

        logger.info(f"🛑 {worker_name} 停止工作")

    def _switch_to_next_account(self, worker: BrowserWorker) -> bool:
        """切换到下一个可用账号"""
        try:
            if not worker.assigned_accounts:
                return False

            # 找到下一个可用账号
            next_account = self._find_next_available_account(worker)
            if not next_account:
                logger.warning(f"⚠️ 浏览器 {worker.browser_id} 没有更多可用账号")
                return False

            old_account = worker.current_account.email if worker.current_account else "None"

            # 执行账号切换
            worker.status = BrowserStatus.SWITCHING
            success = self._perform_account_switch(worker, next_account)

            if success:
                worker.current_account = next_account
                worker.current_account_index = worker.assigned_accounts.index(next_account)
                worker.last_switch_time = time.time()
                worker.status = BrowserStatus.IDLE

                with self.stats_lock:
                    self.stats.total_switches += 1
                    self.stats.successful_switches += 1

                logger.info(f"🔄 浏览器 {worker.browser_id} 账号切换成功: {old_account} -> {next_account.email}")

                # 触发回调
                self._trigger_switch_callback(worker.browser_id, old_account, next_account.email, True)
                return True
            else:
                with self.stats_lock:
                    self.stats.total_switches += 1
                    self.stats.failed_switches += 1

                worker.status = BrowserStatus.ERROR
                logger.error(f"❌ 浏览器 {worker.browser_id} 账号切换失败: {old_account} -> {next_account.email}")

                # 触发回调
                self._trigger_switch_callback(worker.browser_id, old_account, next_account.email, False)
                return False

        except Exception as e:
            logger.error(f"❌ 账号切换异常: {e}")
            worker.status = BrowserStatus.ERROR
            return False

    def _find_next_available_account(self, worker: BrowserWorker) -> Optional[Account]:
        """找到下一个可用账号"""
        if not worker.assigned_accounts:
            return None

        # 从当前账号的下一个开始查找
        start_index = (worker.current_account_index + 1) % len(worker.assigned_accounts)

        for i in range(len(worker.assigned_accounts)):
            index = (start_index + i) % len(worker.assigned_accounts)
            account = worker.assigned_accounts[index]

            sent_count = worker.account_sent_counts.get(account.email, 0)
            if sent_count < self.config.emails_per_account:
                return account

        return None

    def _perform_account_switch(self, worker: BrowserWorker, new_account: Account) -> bool:
        """执行实际的账号切换操作"""
        try:
            # 这里应该调用实际的账号切换逻辑
            # 例如：清除Cookie、加载新账号Cookie、验证登录状态等

            # 模拟切换过程
            time.sleep(1)  # 模拟切换时间

            # 实际实现中应该调用浏览器管理器的切换方法
            # return browser_manager.switch_account(worker.browser_instance, new_account)

            return True  # 模拟切换成功

        except Exception as e:
            logger.error(f"❌ 执行账号切换失败: {e}")
            return False

    def _send_email_with_browser(self, worker: BrowserWorker, task: EmailTask) -> bool:
        """使用指定浏览器发送邮件"""
        try:
            # 获取或创建邮件发送器
            sender = self._get_or_create_sender(worker)
            if not sender:
                logger.error(f"❌ 无法获取浏览器 {worker.browser_id} 的邮件发送器")
                return False

            # 执行邮件发送
            success = sender.send_email_ultra_fast(
                to_email=task.to_email,
                subject=task.subject,
                content=task.content
            )

            if success:
                logger.info(f"✅ 邮件发送成功: {task.task_id}")
            else:
                logger.error(f"❌ 邮件发送失败: {task.task_id}")

            return success

        except Exception as e:
            logger.error(f"❌ 发送邮件异常: {e}")
            task.error_message = str(e)
            return False

    def _get_or_create_sender(self, worker: BrowserWorker):
        """获取或创建邮件发送器"""
        try:
            # 如果有外部发送器回调，使用外部发送器
            if hasattr(self, 'sender_callback') and self.sender_callback:
                return self.sender_callback(worker.browser_id)

            # 否则创建内部发送器
            from src.core.sina_ultra_fast_sender_final import SinaUltraFastSenderFinal
            return SinaUltraFastSenderFinal(worker.browser_instance.driver)

        except Exception as e:
            logger.error(f"❌ 创建邮件发送器失败: {e}")
            return None

    def set_sender_callback(self, callback: Callable):
        """设置外部发送器回调"""
        self.sender_callback = callback

    def _return_task_to_global_queue(self, task: EmailTask):
        """将任务放回全局队列"""
        task.status = TaskStatus.PENDING
        task.assigned_browser = None
        task.assigned_account = None
        task.assigned_time = None

        self.global_task_queue.put((task.priority, task.task_id))

        with self.stats_lock:
            self.stats.pending_tasks += 1
            if task.status == TaskStatus.ASSIGNED:
                self.stats.assigned_tasks -= 1
            elif task.status == TaskStatus.SENDING:
                self.stats.sending_tasks -= 1

    def _trigger_task_callback(self, event_type: str, task: EmailTask):
        """触发任务回调"""
        for callback in self.task_callbacks:
            try:
                callback(event_type, task)
            except Exception as e:
                logger.error(f"❌ 任务回调异常: {e}")

    def _trigger_switch_callback(self, browser_id: str, old_account: str, new_account: str, success: bool):
        """触发切换回调"""
        for callback in self.switch_callbacks:
            try:
                callback(browser_id, old_account, new_account, success)
            except Exception as e:
                logger.error(f"❌ 切换回调异常: {e}")

    def _trigger_stats_callback(self):
        """触发统计回调"""
        stats = self.get_comprehensive_stats()
        for callback in self.stats_callbacks:
            try:
                callback(stats)
            except Exception as e:
                logger.error(f"❌ 统计回调异常: {e}")

    def add_email_task(self, to_email: str, subject: str, content: str, priority: int = 1) -> str:
        """添加邮件任务"""
        task_id = f"task_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"

        task = EmailTask(
            task_id=task_id,
            to_email=to_email,
            subject=subject,
            content=content,
            priority=priority
        )

        with self.task_lock:
            self.tasks[task_id] = task
            self.global_task_queue.put((priority, task_id))

            with self.stats_lock:
                self.stats.total_tasks += 1
                self.stats.pending_tasks += 1

        logger.info(f"📝 添加邮件任务: {task_id} -> {to_email}")
        return task_id

    def add_batch_tasks(self, email_list: List[Tuple[str, str, str]], priority: int = 1) -> List[str]:
        """批量添加邮件任务"""
        task_ids = []

        with self.task_lock:
            for to_email, subject, content in email_list:
                task_id = f"task_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"

                task = EmailTask(
                    task_id=task_id,
                    to_email=to_email,
                    subject=subject,
                    content=content,
                    priority=priority
                )

                self.tasks[task_id] = task
                self.global_task_queue.put((priority, task_id))
                task_ids.append(task_id)

            with self.stats_lock:
                self.stats.total_tasks += len(email_list)
                self.stats.pending_tasks += len(email_list)

        logger.info(f"📝 批量添加 {len(email_list)} 个邮件任务")
        return task_ids

    def stop_distribution(self):
        """停止任务分发"""
        logger.info("🛑 停止任务分发系统...")

        self.is_stopping = True
        self.is_running = False

        # 等待分发器线程停止
        if self.distributor_thread and self.distributor_thread.is_alive():
            self.distributor_thread.join(timeout=5)

        # 等待所有浏览器工作线程停止
        for worker in self.browser_workers.values():
            if worker.worker_thread and worker.worker_thread.is_alive():
                worker.is_working = False
                worker.worker_thread.join(timeout=5)

        logger.info("✅ 任务分发系统已停止")

    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        with self.stats_lock:
            # 计算运行时间
            runtime = time.time() - self.stats.start_time if self.stats.start_time > 0 else 0

            # 计算每分钟发送数量
            if runtime > 0:
                self.stats.emails_per_minute = (self.stats.completed_tasks / runtime) * 60

            # 更新浏览器状态统计
            active_browsers = 0
            idle_browsers = 0
            for worker in self.browser_workers.values():
                if worker.is_working:
                    if worker.status == BrowserStatus.IDLE:
                        idle_browsers += 1
                    else:
                        active_browsers += 1

            self.stats.active_browsers = active_browsers
            self.stats.idle_browsers = idle_browsers

            # 收集每个浏览器的详细统计
            browser_stats = {}
            for browser_id, worker in self.browser_workers.items():
                browser_stats[browser_id] = {
                    'browser_id': browser_id,
                    'status': worker.status.value,
                    'is_working': worker.is_working,
                    'current_account': worker.current_account.email if worker.current_account else None,
                    'assigned_accounts': [acc.email for acc in worker.assigned_accounts],
                    'current_account_sent': worker.get_current_account_sent_count(),
                    'total_sent': worker.total_sent,
                    'total_failed': worker.total_failed,
                    'queue_size': worker.task_queue.qsize(),
                    'last_send_time': worker.last_send_time,
                    'last_switch_time': worker.last_switch_time,
                    'account_sent_counts': worker.account_sent_counts.copy()
                }

            return {
                'system_status': {
                    'is_running': self.is_running,
                    'is_stopping': self.is_stopping,
                    'runtime_seconds': runtime,
                    'start_time': self.stats.start_time
                },
                'task_stats': {
                    'total_tasks': self.stats.total_tasks,
                    'pending_tasks': self.stats.pending_tasks,
                    'assigned_tasks': self.stats.assigned_tasks,
                    'sending_tasks': self.stats.sending_tasks,
                    'completed_tasks': self.stats.completed_tasks,
                    'failed_tasks': self.stats.failed_tasks,
                    'emails_per_minute': self.stats.emails_per_minute,
                    'global_queue_size': self.global_task_queue.qsize()
                },
                'browser_stats': {
                    'total_browsers': self.stats.total_browsers,
                    'active_browsers': self.stats.active_browsers,
                    'idle_browsers': self.stats.idle_browsers
                },
                'account_stats': {
                    'total_accounts': self.stats.total_accounts,
                    'total_switches': self.stats.total_switches,
                    'successful_switches': self.stats.successful_switches,
                    'failed_switches': self.stats.failed_switches,
                    'switch_success_rate': (self.stats.successful_switches / max(self.stats.total_switches, 1)) * 100
                },
                'browser_details': browser_stats
            }

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        task = self.tasks.get(task_id)
        if not task:
            return None

        return {
            'task_id': task.task_id,
            'to_email': task.to_email,
            'subject': task.subject,
            'status': task.status.value,
            'assigned_browser': task.assigned_browser,
            'assigned_account': task.assigned_account,
            'created_time': task.created_time,
            'assigned_time': task.assigned_time,
            'start_time': task.start_time,
            'complete_time': task.complete_time,
            'retry_count': task.retry_count,
            'error_message': task.error_message
        }

    def force_account_switch(self, browser_id: str) -> bool:
        """强制指定浏览器切换账号"""
        if browser_id not in self.browser_workers:
            logger.error(f"❌ 浏览器 {browser_id} 不存在")
            return False

        worker = self.browser_workers[browser_id]
        return self._switch_to_next_account(worker)

    def get_browser_status(self, browser_id: str) -> Optional[Dict[str, Any]]:
        """获取指定浏览器的状态"""
        if browser_id not in self.browser_workers:
            return None

        worker = self.browser_workers[browser_id]
        return {
            'browser_id': browser_id,
            'status': worker.status.value,
            'is_working': worker.is_working,
            'current_account': worker.current_account.email if worker.current_account else None,
            'assigned_accounts': [acc.email for acc in worker.assigned_accounts],
            'current_account_sent': worker.get_current_account_sent_count(),
            'total_sent': worker.total_sent,
            'total_failed': worker.total_failed,
            'queue_size': worker.task_queue.qsize(),
            'needs_account_switch': worker.should_switch_account(self.config.emails_per_account)
        }

    def add_task_callback(self, callback: Callable):
        """添加任务回调函数"""
        self.task_callbacks.append(callback)

    def add_switch_callback(self, callback: Callable):
        """添加切换回调函数"""
        self.switch_callbacks.append(callback)

    def add_stats_callback(self, callback: Callable):
        """添加统计回调函数"""
        self.stats_callbacks.append(callback)

    def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理任务分配系统资源...")

        # 停止分发
        if self.is_running:
            self.stop_distribution()

        # 清理任务
        self.tasks.clear()

        # 清理队列
        while not self.global_task_queue.empty():
            try:
                self.global_task_queue.get_nowait()
            except queue.Empty:
                break

        logger.info("✅ 资源清理完成")
