#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发送策略修复验证测试
验证成功流程经验是否正确应用到第一步策略中
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_sending_strategies_fix():
    """测试发送策略修复"""
    print("🧪 开始测试发送策略修复...")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 验证发送策略枚举
    print("\n📋 测试1: 验证发送策略枚举")
    try:
        from src.core.unified_email_sender import SendingStrategy
        
        strategies = [
            (SendingStrategy.ULTRA_FAST, "ultra_fast", "第一步策略：超高速发送"),
            (SendingStrategy.STANDARD, "standard", "第二步策略：标准发送"),
            (SendingStrategy.SAFE, "safe", "第三步策略：安全发送")
        ]
        
        for strategy, value, description in strategies:
            if strategy.value == value:
                print(f"  ✅ {description} - {value}")
                test_results.append(True)
            else:
                print(f"  ❌ {description} - 值不匹配")
                test_results.append(False)
                
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        test_results.append(False)
    
    # 测试2: 验证第一步策略修复
    print("\n📋 测试2: 验证第一步策略（ULTRA_FAST）修复")
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        source = inspect.getsource(UnifiedEmailSender._send_ultra_fast)
        
        checks = [
            ("应用成功流程经验" in source, "包含成功流程经验描述"),
            ("点击写信 → 填写收件人 → 填写主题 → 填写内容 → 点击发送" in source, "包含完整的5步流程"),
            ("第一步策略：超高速发送" in source, "明确标识为第一步策略"),
            ("第一步策略成功" in source, "包含成功日志"),
            ("第一步策略失败" in source, "包含失败处理")
        ]
        
        for check, description in checks:
            if check:
                print(f"  ✅ {description}")
                test_results.append(True)
            else:
                print(f"  ❌ {description}")
                test_results.append(False)
                
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        test_results.append(False)
    
    # 测试3: 验证第二步策略修复
    print("\n📋 测试3: 验证第二步策略（STANDARD）修复")
    try:
        source = inspect.getsource(UnifiedEmailSender._send_standard)
        
        checks = [
            ("第二步策略：标准发送" in source, "明确标识为第二步策略"),
            ("元素操作模式" in source, "包含元素操作描述"),
            ("第二步策略成功" in source, "包含成功日志"),
            ("第二步策略失败" in source, "包含失败处理")
        ]
        
        for check, description in checks:
            if check:
                print(f"  ✅ {description}")
                test_results.append(True)
            else:
                print(f"  ❌ {description}")
                test_results.append(False)
                
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        test_results.append(False)
    
    # 测试4: 验证第三步策略修复
    print("\n📋 测试4: 验证第三步策略（SAFE）修复")
    try:
        source = inspect.getsource(UnifiedEmailSender._send_safe)
        
        checks = [
            ("第三步策略：安全发送" in source, "明确标识为第三步策略"),
            ("慢但稳定" in source, "包含安全模式描述"),
            ("第三步策略成功" in source, "包含成功日志"),
            ("所有策略都失败了" in source, "包含最终失败处理")
        ]
        
        for check, description in checks:
            if check:
                print(f"  ✅ {description}")
                test_results.append(True)
            else:
                print(f"  ❌ {description}")
                test_results.append(False)
                
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        test_results.append(False)
    
    # 测试5: 验证智能策略选择逻辑
    print("\n📋 测试5: 验证智能策略选择逻辑")
    try:
        source = inspect.getsource(UnifiedEmailSender.send_email)
        
        checks = [
            ("智能策略选择" in source, "包含智能策略选择描述"),
            ("第一步策略失败，自动尝试第二步策略" in source, "包含自动降级逻辑"),
            ("第二步策略失败，自动尝试第三步策略" in source, "包含完整降级链"),
            ("开始发送，主策略" in source, "包含策略开始日志")
        ]
        
        for check, description in checks:
            if check:
                print(f"  ✅ {description}")
                test_results.append(True)
            else:
                print(f"  ❌ {description}")
                test_results.append(False)
                
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        test_results.append(False)
    
    # 测试结果汇总
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    success_rate = (passed / total) * 100 if total > 0 else 0
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    print(f"📈 成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 发送策略修复验证成功！")
        print("✅ 第一步策略（ULTRA_FAST）已应用成功流程经验")
        print("✅ 第二步策略（STANDARD）已优化为元素操作模式")
        print("✅ 第三步策略（SAFE）已优化为安全稳定模式")
        print("✅ 智能策略选择逻辑已实现自动降级")
    elif success_rate >= 70:
        print("\n⚠️ 发送策略修复基本成功，但有部分问题需要注意")
    else:
        print("\n❌ 发送策略修复存在问题，需要进一步检查")
    
    return success_rate >= 90

def main():
    """主函数"""
    print("🎯 发送策略修复验证")
    print("验证目标：将成功流程经验应用到第一步策略中")
    print("成功流程：点击写信 → 填写收件人 → 填写主题 → 填写内容 → 点击发送")
    print("策略数量：3个（ULTRA_FAST, STANDARD, SAFE）")
    
    success = test_sending_strategies_fix()
    
    if success:
        print("\n🎯 验证结论：发送策略修复成功！")
        print("⚡ 第一步策略已应用最快最可靠的流程经验")
        print("🔄 智能策略选择支持自动降级")
        print("📊 三步策略各有特色，适应不同场景")
    else:
        print("\n⚠️ 验证结论：修复可能存在问题，建议进一步检查")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
