#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源管理器
管理收件人数据的不同来源，包括导入数据、文件监控数据等
"""

import json
import sqlite3
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from src.models.database import DatabaseManager
from src.utils.logger import get_logger

logger = get_logger("DataSourceManager")


@dataclass
class RecipientData:
    """收件人数据"""
    email: str
    name: Optional[str] = None
    company: Optional[str] = None
    phone: Optional[str] = None
    title: Optional[str] = None
    source: str = "manual"  # manual, import, monitor, database
    variables: Dict[str, str] = None
    created_time: Optional[datetime] = None
    
    def __post_init__(self):
        if self.variables is None:
            self.variables = {}
        if self.created_time is None:
            self.created_time = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'email': self.email,
            'name': self.name,
            'company': self.company,
            'phone': self.phone,
            'title': self.title,
            'source': self.source,
            'variables': json.dumps(self.variables, ensure_ascii=False),
            'created_time': self.created_time.isoformat() if self.created_time else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RecipientData':
        """从字典创建对象"""
        variables = {}
        if data.get('variables'):
            try:
                variables = json.loads(data['variables'])
            except:
                pass
        
        created_time = None
        if data.get('created_time'):
            try:
                created_time = datetime.fromisoformat(data['created_time'])
            except:
                pass
        
        return cls(
            email=data['email'],
            name=data.get('name'),
            company=data.get('company'),
            phone=data.get('phone'),
            title=data.get('title'),
            source=data.get('source', 'manual'),
            variables=variables,
            created_time=created_time
        )


class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化数据源管理器
        
        Args:
            db_manager: 数据库管理器
        """
        self.db = db_manager
        self.ensure_table_exists()
    
    def ensure_table_exists(self):
        """确保数据表存在"""
        try:
            create_table_sql = """
                CREATE TABLE IF NOT EXISTS recipient_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL UNIQUE,
                    name TEXT,
                    company TEXT,
                    phone TEXT,
                    title TEXT,
                    source TEXT DEFAULT 'manual',
                    variables TEXT,
                    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """
            self.db.execute_update(create_table_sql)
            
            # 创建索引
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_recipient_email ON recipient_data(email)",
                "CREATE INDEX IF NOT EXISTS idx_recipient_source ON recipient_data(source)",
                "CREATE INDEX IF NOT EXISTS idx_recipient_created_time ON recipient_data(created_time)"
            ]
            
            for index_sql in indexes:
                self.db.execute_update(index_sql)
            
            logger.info("收件人数据表和索引创建完成")
            
        except Exception as e:
            logger.error(f"创建收件人数据表失败: {e}")
            raise
    
    def add_recipient(self, recipient: RecipientData) -> bool:
        """
        添加收件人
        
        Args:
            recipient: 收件人数据
        
        Returns:
            是否添加成功
        """
        try:
            query = """
                INSERT OR REPLACE INTO recipient_data (
                    email, name, company, phone, title, source, variables
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                recipient.email,
                recipient.name,
                recipient.company,
                recipient.phone,
                recipient.title,
                recipient.source,
                json.dumps(recipient.variables, ensure_ascii=False)
            )
            
            self.db.execute_update(query, params)
            logger.debug(f"收件人添加成功: {recipient.email}")
            return True
            
        except Exception as e:
            logger.error(f"添加收件人失败: {recipient.email}, 错误: {e}")
            return False
    
    def add_recipients_batch(self, recipients: List[RecipientData]) -> int:
        """
        批量添加收件人
        
        Args:
            recipients: 收件人列表
        
        Returns:
            成功添加的数量
        """
        success_count = 0
        
        try:
            query = """
                INSERT OR REPLACE INTO recipient_data (
                    email, name, company, phone, title, source, variables
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """

            params_list = []
            for recipient in recipients:
                params = (
                    recipient.email,
                    recipient.name,
                    recipient.company,
                    recipient.phone,
                    recipient.title,
                    recipient.source,
                    json.dumps(recipient.variables, ensure_ascii=False)
                )
                params_list.append(params)

            # 使用数据库管理器的正确方法
            with self.db.get_cursor() as cursor:
                cursor.executemany(query, params_list)

            success_count = len(recipients)
            logger.info(f"批量添加收件人成功: {success_count} 个")

        except Exception as e:
            logger.error(f"批量添加收件人失败: {e}")
        
        return success_count
    
    def get_recipients_by_source(self, source: str, limit: int = 1000) -> List[RecipientData]:
        """
        根据数据源获取收件人
        
        Args:
            source: 数据源类型
            limit: 限制数量
        
        Returns:
            收件人列表
        """
        try:
            query = """
                SELECT * FROM recipient_data 
                WHERE source = ? 
                ORDER BY created_time DESC 
                LIMIT ?
            """
            results = self.db.execute_query(query, (source, limit))
            
            return [RecipientData.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"获取收件人失败: 数据源 {source}, 错误: {e}")
            return []
    
    def get_all_recipients(self, limit: int = 1000) -> List[RecipientData]:
        """
        获取所有收件人
        
        Args:
            limit: 限制数量
        
        Returns:
            收件人列表
        """
        try:
            query = """
                SELECT * FROM recipient_data 
                ORDER BY created_time DESC 
                LIMIT ?
            """
            results = self.db.execute_query(query, (limit,))
            
            return [RecipientData.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"获取所有收件人失败: {e}")
            return []
    
    def search_recipients(self, keyword: str, limit: int = 100) -> List[RecipientData]:
        """
        搜索收件人
        
        Args:
            keyword: 搜索关键词
            limit: 限制数量
        
        Returns:
            收件人列表
        """
        try:
            query = """
                SELECT * FROM recipient_data 
                WHERE email LIKE ? OR name LIKE ? OR company LIKE ?
                ORDER BY created_time DESC 
                LIMIT ?
            """
            search_term = f"%{keyword}%"
            results = self.db.execute_query(query, (search_term, search_term, search_term, limit))
            
            return [RecipientData.from_dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"搜索收件人失败: 关键词 {keyword}, 错误: {e}")
            return []
    
    def delete_recipient(self, email: str) -> bool:
        """
        删除收件人
        
        Args:
            email: 邮箱地址
        
        Returns:
            是否删除成功
        """
        try:
            query = "DELETE FROM recipient_data WHERE email = ?"
            rows_affected = self.db.execute_update(query, (email,))
            success = rows_affected > 0
            
            if success:
                logger.info(f"收件人删除成功: {email}")
            else:
                logger.warning(f"收件人删除失败，未找到记录: {email}")
            
            return success
            
        except Exception as e:
            logger.error(f"删除收件人失败: {email}, 错误: {e}")
            return False
    
    def delete_recipients_by_source(self, source: str) -> int:
        """
        根据数据源删除收件人
        
        Args:
            source: 数据源类型
        
        Returns:
            删除的数量
        """
        try:
            query = "DELETE FROM recipient_data WHERE source = ?"
            rows_affected = self.db.execute_update(query, (source,))
            
            logger.info(f"删除了 {rows_affected} 个收件人，数据源: {source}")
            return rows_affected
            
        except Exception as e:
            logger.error(f"删除收件人失败: 数据源 {source}, 错误: {e}")
            return 0
    
    def get_source_statistics(self) -> Dict[str, int]:
        """
        获取数据源统计
        
        Returns:
            统计信息字典
        """
        try:
            query = """
                SELECT source, COUNT(*) as count 
                FROM recipient_data 
                GROUP BY source
            """
            results = self.db.execute_query(query)
            
            return {row['source']: row['count'] for row in results}
            
        except Exception as e:
            logger.error(f"获取数据源统计失败: {e}")
            return {}
    
    def update_recipient(self, recipient: RecipientData) -> bool:
        """
        更新收件人信息
        
        Args:
            recipient: 收件人数据
        
        Returns:
            是否更新成功
        """
        try:
            query = """
                UPDATE recipient_data SET 
                    name = ?, company = ?, phone = ?, title = ?, 
                    variables = ?, updated_time = CURRENT_TIMESTAMP
                WHERE email = ?
            """
            params = (
                recipient.name,
                recipient.company,
                recipient.phone,
                recipient.title,
                json.dumps(recipient.variables, ensure_ascii=False),
                recipient.email
            )
            
            rows_affected = self.db.execute_update(query, params)
            success = rows_affected > 0
            
            if success:
                logger.debug(f"收件人更新成功: {recipient.email}")
            else:
                logger.warning(f"收件人更新失败，未找到记录: {recipient.email}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新收件人失败: {recipient.email}, 错误: {e}")
            return False
    
    def clear_all_recipients(self) -> int:
        """
        清空所有收件人
        
        Returns:
            删除的数量
        """
        try:
            query = "DELETE FROM recipient_data"
            rows_affected = self.db.execute_update(query)
            
            logger.info(f"清空了所有收件人，共 {rows_affected} 个")
            return rows_affected
            
        except Exception as e:
            logger.error(f"清空收件人失败: {e}")
            return 0
