#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证完整5步逻辑复制
检查第一步策略是否完整复制了multi_browser_sender_flow_test.py中的完美5步流程
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_complete_5_steps_copy():
    """检查完整的5步逻辑复制"""
    print("🔍 检查完整的5步逻辑复制")
    print("=" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        # 检查主要方法
        execute_method = inspect.getsource(UnifiedEmailSender._execute_5_steps_logic)
        find_element_method = inspect.getsource(UnifiedEmailSender._find_element_by_selectors)
        fill_content_method = inspect.getsource(UnifiedEmailSender._fill_content_optimized_copy)
        check_success_method = inspect.getsource(UnifiedEmailSender._check_send_success_fast_copy)
        
        # 检查完整复制特性
        copy_features = [
            # 核心方法存在性
            ("_find_element_by_selectors" in find_element_method, "元素查找方法已复制"),
            ("_fill_content_optimized_copy" in fill_content_method, "内容填写方法已复制"),
            ("_check_send_success_fast_copy" in check_success_method, "成功检查方法已复制"),
            
            # 步骤1: 收件人填写
            ("收件人超极速填写" in execute_method, "步骤1: 收件人超极速填写"),
            ("//input[@type='text'][1]" in execute_method, "收件人选择器: 第一个文本输入框"),
            ("页面状态恢复" in execute_method, "收件人备用逻辑: 页面状态恢复"),
            ("document.body.click()" in execute_method, "收件人备用逻辑: 点击页面空白"),
            
            # 步骤2: 主题填写
            ("精确主题填写" in execute_method, "步骤2: 精确主题填写"),
            ("//input[@name='subj' and @class='input inp_base']" in execute_method, "主题选择器: 精确匹配"),
            ("正确选中主题字段" in execute_method, "主题验证逻辑"),
            ("主题超极速填写成功" in execute_method, "主题填写成功日志"),
            
            # 步骤3: 内容填写
            ("开始超级优化内容填写" in execute_method, "步骤3: 内容填写开始"),
            ("//iframe[@class='iframe']" in fill_content_method, "内容选择器: iframe精确匹配"),
            ("尝试填写到: body" in fill_content_method, "内容填写: iframe body"),
            ("iframe内容已填写" in fill_content_method, "内容填写成功日志"),
            
            # 步骤4: 发送按钮
            ("超极速发送操作" in execute_method, "步骤4: 超极速发送操作"),
            ("//a[.//i[contains(@class, 'icon_send')]]" in execute_method, "发送选择器: 图标匹配"),
            ("发送按钮超极速点击完成" in execute_method, "发送点击成功日志"),
            
            # 成功检查
            ("您的邮件已发送" in check_success_method, "成功检查: 发送确认消息"),
            ("发送成功确认" in check_success_method, "成功检查日志"),
            
            # 性能优化
            ("timeout=0.3" in execute_method, "性能优化: 快速超时"),
            ("elapsed = time.time() - start_time" in execute_method, "性能监控: 耗时统计"),
        ]
        
        print("📋 完整5步逻辑复制特性:")
        passed = 0
        for check, desc in copy_features:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            if check:
                passed += 1
        
        print(f"\n📊 复制完整性: {passed}/{len(copy_features)}")
        
        return passed == len(copy_features)
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_copied_selectors():
    """分析复制的选择器"""
    print("\n🔍 分析复制的选择器")
    print("=" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        execute_method = inspect.getsource(UnifiedEmailSender._execute_5_steps_logic)
        fill_content_method = inspect.getsource(UnifiedEmailSender._fill_content_optimized_copy)
        
        print("📋 收件人字段选择器 (步骤1):")
        recipient_selectors = [
            "//input[@type='text'][1]",
            "//input[@type='text' and not(@name) and not(@id) and not(@placeholder)]",
            "//input[@placeholder='收件人：']",
            "//input[contains(@placeholder, '收件人')]",
            "//input[@name='to']",
        ]
        for selector in recipient_selectors:
            if selector in execute_method:
                print(f"  ✅ {selector}")
            else:
                print(f"  ❌ {selector}")
        
        print("\n📋 主题字段选择器 (步骤2):")
        subject_selectors = [
            "//input[@name='subj' and @class='input inp_base']",
            "//input[@name='subj' and contains(@class, 'inp_base')]",
            "//input[@name='subj']",
            "//input[@class='input inp_base']",
            "//input[contains(@class, 'inp_base')]",
        ]
        for selector in subject_selectors:
            if selector in execute_method:
                print(f"  ✅ {selector}")
            else:
                print(f"  ❌ {selector}")
        
        print("\n📋 内容区域选择器 (步骤3):")
        content_selectors = [
            "//iframe[@class='iframe']",
            "//iframe[contains(@class, 'iframe')]",
            "//iframe[@name='content']",
            "//div[@contenteditable='true']",
            "//textarea[@name='content']",
        ]
        for selector in content_selectors:
            if selector in fill_content_method:
                print(f"  ✅ {selector}")
            else:
                print(f"  ❌ {selector}")
        
        print("\n📋 发送按钮选择器 (步骤4):")
        send_selectors = [
            "//a[.//i[contains(@class, 'icon_send')]]",
            "//a[.//i[text()='发送']]",
            "//a[contains(text(), '发送')]",
            "//button[contains(text(), '发送')]",
            "//input[@value='发送']",
        ]
        for selector in send_selectors:
            if selector in execute_method:
                print(f"  ✅ {selector}")
            else:
                print(f"  ❌ {selector}")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def compare_with_original():
    """与原始SinaUltraFastSenderFinal对比"""
    print("\n🔍 与原始SinaUltraFastSenderFinal对比")
    print("=" * 60)
    
    try:
        from src.core.sina_ultra_fast_sender_final import SinaUltraFastSenderFinal
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        # 获取原始方法
        original_send = inspect.getsource(SinaUltraFastSenderFinal._send_with_elements)
        original_content = inspect.getsource(SinaUltraFastSenderFinal._fill_content_optimized)
        original_success = inspect.getsource(SinaUltraFastSenderFinal._check_send_success_fast)
        
        # 获取复制的方法
        copied_execute = inspect.getsource(UnifiedEmailSender._execute_5_steps_logic)
        copied_content = inspect.getsource(UnifiedEmailSender._fill_content_optimized_copy)
        copied_success = inspect.getsource(UnifiedEmailSender._check_send_success_fast_copy)
        
        # 关键特征对比
        comparison_features = [
            # 日志消息对比
            ("收件人超极速填写" in original_send and "收件人超极速填写" in copied_execute, "日志消息: 收件人超极速填写"),
            ("正确选中主题字段" in original_send and "正确选中主题字段" in copied_execute, "日志消息: 正确选中主题字段"),
            ("主题超极速填写成功" in original_send and "主题超极速填写成功" in copied_execute, "日志消息: 主题超极速填写成功"),
            ("iframe内容已填写" in original_content and "iframe内容已填写" in copied_content, "日志消息: iframe内容已填写"),
            ("发送按钮超极速点击完成" in original_send and "发送按钮超极速点击完成" in copied_execute, "日志消息: 发送按钮超极速点击完成"),
            ("发送成功确认" in original_success and "发送成功确认" in copied_success, "日志消息: 发送成功确认"),
            
            # 技术实现对比
            ("arguments[0].focus()" in original_send and "arguments[0].focus()" in copied_execute, "技术实现: focus()调用"),
            ("dispatchEvent(new Event('input'" in original_send and "dispatchEvent(new Event('input'" in copied_execute, "技术实现: input事件"),
            ("dispatchEvent(new Event('change'" in original_send and "dispatchEvent(new Event('change'" in copied_execute, "技术实现: change事件"),
            ("switch_to.frame(iframe)" in original_content and "switch_to.frame(iframe)" in copied_content, "技术实现: iframe切换"),
            ("您的邮件已发送" in original_success and "您的邮件已发送" in copied_success, "技术实现: 成功检查文本"),
        ]
        
        print("📋 与原始实现对比:")
        passed = 0
        for check, desc in comparison_features:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            if check:
                passed += 1
        
        print(f"\n📊 对比一致性: {passed}/{len(comparison_features)}")
        
        return passed == len(comparison_features)
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 验证完整5步逻辑复制")
    print("目标：确认第一步策略完整复制了multi_browser_sender_flow_test.py中的完美5步流程")
    print("=" * 80)
    
    # 检查完整复制
    copy_ok = check_complete_5_steps_copy()
    
    # 分析选择器
    analyze_copied_selectors()
    
    # 与原始对比
    comparison_ok = compare_with_original()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 完整5步逻辑复制验证结果")
    print("=" * 80)
    
    if copy_ok and comparison_ok:
        print("🎉 完整5步逻辑复制成功！")
        print("✅ 所有关键特性都已复制")
        print("✅ 与原始实现高度一致")
        print("✅ 选择器完整复制")
        print("✅ 日志消息完全一致")
        print("✅ 技术实现完全一致")
        
        print("\n🎯 复制的完美5步流程:")
        print("  1. 🚀 超极速收件人填写 - 完整复制成功逻辑")
        print("  2. 🎯 精确主题填写 - 完整复制成功逻辑")
        print("  3. 🎯 超级优化内容填写 - 完整复制成功逻辑")
        print("  4. 🚀 超极速发送操作 - 完整复制成功逻辑")
        print("  5. 🚀 超极速成功检查 - 完整复制成功逻辑")
        
        print("\n🚀 预期效果:")
        print("第一步策略现在将产生与multi_browser_sender_flow_test.py完全相同的:")
        print("  ✅ 收件人超极速填写: [邮箱地址]")
        print("  ✅ 正确选中主题字段: name=subj, class=input inp_base")
        print("  ✅ 主题超极速填写成功: [主题内容]")
        print("  ✅ iframe内容已填写 (选择器: //iframe[@class='iframe'], 目标: body)")
        print("  ✅ 发送按钮超极速点击完成 (耗时: X.XX秒)")
        print("  ✅ 发送成功确认: 您的邮件已发送")
        
    else:
        print("❌ 完整5步逻辑复制不完整")
        if not copy_ok:
            print("❌ 复制特性检查失败")
        if not comparison_ok:
            print("❌ 与原始实现对比失败")
    
    return 0 if (copy_ok and comparison_ok) else 1

if __name__ == "__main__":
    sys.exit(main())
