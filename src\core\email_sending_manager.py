#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件发送管理器
实现先添加任务再发送的流程，分离任务管理和发送执行
"""

import time
import threading
from typing import List, Dict, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from loguru import logger

from .smart_task_queue import SmartTaskQueue, QueueConfig, TaskPriority
from .batch_processor import BatchProcessor, BatchConfig
from .unified_email_sender import UnifiedEmailSender, SendingStrategy


class SendingMode(Enum):
    """发送模式枚举"""
    MANUAL = "manual"           # 手动发送
    AUTO = "auto"              # 自动发送
    SCHEDULED = "scheduled"     # 定时发送


class SendingStatus(Enum):
    """发送状态枚举"""
    IDLE = "idle"              # 空闲
    PREPARING = "preparing"     # 准备中
    SENDING = "sending"        # 发送中
    PAUSED = "paused"          # 暂停
    COMPLETED = "completed"     # 完成
    ERROR = "error"            # 错误


@dataclass
class SendingConfig:
    """发送配置"""
    mode: SendingMode = SendingMode.MANUAL
    strategy: SendingStrategy = SendingStrategy.ULTRA_FAST
    concurrent_workers: int = 3
    send_interval: float = 2.0
    auto_start_threshold: int = 100  # 自动开始发送的任务阈值
    max_retry_attempts: int = 3
    retry_delay: int = 60


class EmailSendingManager:
    """
    邮件发送管理器
    
    功能特性：
    1. 分离任务管理和发送执行
    2. 支持手动/自动/定时发送
    3. 多工作线程并发发送
    4. 智能任务分配
    5. 实时状态监控
    """
    
    def __init__(self, config: SendingConfig):
        """
        初始化邮件发送管理器
        
        Args:
            config: 发送配置
        """
        self.config = config
        
        # 初始化组件
        queue_config = QueueConfig(
            max_concurrent_tasks=config.concurrent_workers,
            auto_allocation=True,
            batch_allocation_size=1000
        )
        self.task_queue = SmartTaskQueue(queue_config)
        
        batch_config = BatchConfig(
            batch_size=1000,
            auto_adjust=True
        )
        self.batch_processor = BatchProcessor(self.task_queue, batch_config)
        
        # 发送器管理
        self.senders: Dict[str, UnifiedEmailSender] = {}
        self.sender_factory: Optional[Callable] = None
        
        # 发送状态
        self.status = SendingStatus.IDLE
        self.is_sending = False
        self.is_paused = False
        
        # 工作线程
        self.worker_threads: List[threading.Thread] = []
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 统计信息
        self.sending_stats = {
            'start_time': None,
            'end_time': None,
            'total_sent': 0,
            'successful_sent': 0,
            'failed_sent': 0,
            'current_speed': 0.0,  # 每分钟发送数
            'estimated_completion': None
        }
        
        # 回调函数
        self.on_sending_started: Optional[Callable] = None
        self.on_sending_paused: Optional[Callable] = None
        self.on_sending_resumed: Optional[Callable] = None
        self.on_sending_completed: Optional[Callable] = None
        self.on_task_sent: Optional[Callable] = None
        self.on_status_changed: Optional[Callable] = None
        
        # 设置任务队列回调
        self.task_queue.on_task_completed = self._on_task_completed
        self.task_queue.on_task_failed = self._on_task_failed
        
        logger.info("📧 邮件发送管理器初始化完成")
    
    def set_sender_factory(self, factory: Callable[[], UnifiedEmailSender]):
        """设置发送器工厂函数"""
        self.sender_factory = factory
        logger.info("🏭 发送器工厂已设置")
    
    def load_recipients_from_file(self, file_path: str, 
                                 subject_template: str = "邮件主题",
                                 content_template: str = "邮件内容") -> bool:
        """
        从文件加载收件人
        
        Args:
            file_path: 文件路径
            subject_template: 主题模板
            content_template: 内容模板
            
        Returns:
            是否加载成功
        """
        source_id = f"source_{int(time.time())}"
        
        success = self.batch_processor.load_data_source(
            source_id=source_id,
            file_path=file_path,
            email_column='email',  # 默认邮箱列名
            subject_template=subject_template,
            content_template=content_template
        )
        
        if success:
            self.batch_processor.set_current_source(source_id)
            logger.info(f"✅ 收件人文件加载成功: {file_path}")
        else:
            logger.error(f"❌ 收件人文件加载失败: {file_path}")
        
        return success
    
    def add_single_task(self, to_email: str, subject: str, content: str,
                       priority: TaskPriority = TaskPriority.NORMAL) -> str:
        """
        添加单个邮件任务
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            content: 邮件内容
            priority: 优先级
            
        Returns:
            任务ID
        """
        # 创建单任务批次
        batch_id = self.task_queue.create_batch(
            name=f"single_task_{int(time.time())}",
            total_tasks=1,
            priority=priority
        )
        
        # 添加任务
        task_ids = self.task_queue.add_tasks_to_batch(batch_id, [{
            'to_email': to_email,
            'subject': subject,
            'content': content,
            'content_type': 'text/plain',
            'metadata': {'type': 'single_task'}
        }])
        
        logger.info(f"📝 添加单个邮件任务: {to_email}")
        return task_ids[0] if task_ids else ""
    
    def add_batch_tasks(self, tasks_data: List[Dict[str, Any]],
                       batch_name: str = None,
                       priority: TaskPriority = TaskPriority.NORMAL) -> str:
        """
        批量添加邮件任务
        
        Args:
            tasks_data: 任务数据列表
            batch_name: 批次名称
            priority: 优先级
            
        Returns:
            批次ID
        """
        if not batch_name:
            batch_name = f"batch_tasks_{int(time.time())}"
        
        # 创建批次
        batch_id = self.task_queue.create_batch(
            name=batch_name,
            total_tasks=len(tasks_data),
            priority=priority
        )
        
        # 添加任务
        task_ids = self.task_queue.add_tasks_to_batch(batch_id, tasks_data)
        
        logger.info(f"📝 批量添加邮件任务: {len(tasks_data)} 个任务")
        return batch_id
    
    def start_batch_processing(self) -> bool:
        """开始分批处理（从文件加载的大数据量）"""
        if not self.batch_processor.current_source:
            logger.error("❌ 未加载数据源")
            return False
        
        self._change_status(SendingStatus.PREPARING)
        
        # 启动任务队列
        self.task_queue.start_queue()
        
        # 启动分批处理
        success = self.batch_processor.start_processing()
        
        if success:
            logger.info("🚀 开始分批处理")
        else:
            self._change_status(SendingStatus.ERROR)
            logger.error("❌ 分批处理启动失败")
        
        return success
    
    def start_sending(self) -> bool:
        """开始发送邮件"""
        if self.is_sending:
            logger.warning("⚠️ 邮件发送已在进行中")
            return True
        
        if not self.sender_factory:
            logger.error("❌ 未设置发送器工厂")
            return False
        
        # 检查是否有任务
        stats = self.task_queue.get_stats()
        if stats['total_tasks'] == 0:
            logger.warning("⚠️ 没有待发送的任务")
            return False
        
        self.is_sending = True
        self.is_paused = False
        self._change_status(SendingStatus.SENDING)
        
        # 启动任务队列（如果未启动）
        if not self.task_queue.is_running:
            self.task_queue.start_queue()
        
        # 启动发送工作线程
        self._start_sending_workers()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
        self.monitor_thread.start()
        
        # 记录开始时间
        self.sending_stats['start_time'] = datetime.now()
        
        logger.info("🚀 开始发送邮件")
        
        if self.on_sending_started:
            self.on_sending_started()
        
        return True
    
    def pause_sending(self):
        """暂停发送"""
        if not self.is_sending:
            return
        
        self.is_paused = True
        self.task_queue.pause_queue()
        self._change_status(SendingStatus.PAUSED)
        
        logger.info("⏸️ 邮件发送已暂停")
        
        if self.on_sending_paused:
            self.on_sending_paused()
    
    def resume_sending(self):
        """恢复发送"""
        if not self.is_sending or not self.is_paused:
            return
        
        self.is_paused = False
        self.task_queue.resume_queue()
        self._change_status(SendingStatus.SENDING)
        
        logger.info("▶️ 邮件发送已恢复")
        
        if self.on_sending_resumed:
            self.on_sending_resumed()
    
    def stop_sending(self):
        """停止发送"""
        if not self.is_sending:
            return
        
        self.is_sending = False
        self.is_paused = False
        
        # 停止任务队列
        self.task_queue.stop_queue()
        
        # 停止分批处理
        self.batch_processor.stop_processing()
        
        # 等待工作线程结束
        for thread in self.worker_threads:
            if thread.is_alive():
                thread.join(timeout=5)
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        # 清理发送器
        for sender in self.senders.values():
            sender.cleanup()
        self.senders.clear()
        
        self.sending_stats['end_time'] = datetime.now()
        self._change_status(SendingStatus.IDLE)
        
        logger.info("🛑 邮件发送已停止")
    
    def _start_sending_workers(self):
        """启动发送工作线程"""
        self.worker_threads.clear()
        
        for i in range(self.config.concurrent_workers):
            worker_id = f"worker_{i}"
            thread = threading.Thread(target=self._sending_worker, args=(worker_id,), daemon=True)
            thread.start()
            self.worker_threads.append(thread)
        
        logger.info(f"🔄 启动 {self.config.concurrent_workers} 个发送工作线程")
    
    def _sending_worker(self, worker_id: str):
        """发送工作线程"""
        logger.info(f"🔄 发送工作线程 {worker_id} 启动")
        
        # 创建发送器
        try:
            sender = self.sender_factory()
            sender.change_strategy(self.config.strategy)
            self.senders[worker_id] = sender
            
            # 准备发送环境
            if not sender.prepare_for_sending():
                logger.error(f"❌ 工作线程 {worker_id} 发送环境准备失败")
                return
            
        except Exception as e:
            logger.error(f"❌ 工作线程 {worker_id} 发送器创建失败: {e}")
            return
        
        # 发送循环
        while self.is_sending:
            try:
                if self.is_paused:
                    time.sleep(1)
                    continue
                
                # 获取下一个任务
                task = self.task_queue.get_next_task(worker_id)
                if not task:
                    time.sleep(1)
                    continue
                
                # 发送邮件
                result = sender.send_email(
                    task.to_email,
                    task.subject,
                    task.content,
                    task.content_type
                )
                
                # 完成任务
                self.task_queue.complete_task(
                    task.task_id,
                    result.success,
                    result.message if not result.success else None
                )
                
                # 发送间隔
                time.sleep(self.config.send_interval)
                
            except Exception as e:
                logger.error(f"❌ 发送工作线程 {worker_id} 异常: {e}")
                time.sleep(5)
        
        logger.info(f"🔄 发送工作线程 {worker_id} 结束")
    
    def _monitor_worker(self):
        """监控工作线程"""
        logger.info("📊 发送监控线程启动")
        
        while self.is_sending:
            try:
                self._update_sending_stats()
                
                # 检查是否完成
                if self._check_sending_completion():
                    break
                
                time.sleep(10)  # 每10秒更新一次
                
            except Exception as e:
                logger.error(f"❌ 发送监控异常: {e}")
                time.sleep(30)
        
        logger.info("📊 发送监控线程结束")
    
    def _update_sending_stats(self):
        """更新发送统计"""
        queue_stats = self.task_queue.get_stats()
        
        self.sending_stats['total_sent'] = queue_stats['completed_tasks'] + queue_stats['failed_tasks']
        self.sending_stats['successful_sent'] = queue_stats['completed_tasks']
        self.sending_stats['failed_sent'] = queue_stats['failed_tasks']
        
        # 计算发送速度
        if self.sending_stats['start_time']:
            elapsed = (datetime.now() - self.sending_stats['start_time']).total_seconds()
            if elapsed > 0:
                self.sending_stats['current_speed'] = (self.sending_stats['total_sent'] / elapsed) * 60
        
        # 估算完成时间
        remaining_tasks = queue_stats['total_tasks'] - self.sending_stats['total_sent']
        if self.sending_stats['current_speed'] > 0 and remaining_tasks > 0:
            remaining_minutes = remaining_tasks / self.sending_stats['current_speed']
            self.sending_stats['estimated_completion'] = datetime.now().timestamp() + (remaining_minutes * 60)
    
    def _check_sending_completion(self) -> bool:
        """检查发送是否完成"""
        queue_stats = self.task_queue.get_stats()
        
        # 检查是否所有任务都已处理
        total_processed = queue_stats['completed_tasks'] + queue_stats['failed_tasks']
        if total_processed >= queue_stats['total_tasks'] and queue_stats['processing_tasks'] == 0:
            self.is_sending = False
            self.sending_stats['end_time'] = datetime.now()
            self._change_status(SendingStatus.COMPLETED)
            
            logger.info("🎉 邮件发送完成")
            
            if self.on_sending_completed:
                self.on_sending_completed()
            
            return True
        
        return False
    
    def _on_task_completed(self, task):
        """任务完成回调"""
        if self.on_task_sent:
            self.on_task_sent(task, True)
    
    def _on_task_failed(self, task):
        """任务失败回调"""
        if self.on_task_sent:
            self.on_task_sent(task, False)
    
    def _change_status(self, new_status: SendingStatus):
        """更改状态"""
        old_status = self.status
        self.status = new_status
        
        if old_status != new_status:
            logger.info(f"📊 状态变更: {old_status.value} → {new_status.value}")
            
            if self.on_status_changed:
                self.on_status_changed(old_status, new_status)
    
    def get_sending_status(self) -> Dict[str, Any]:
        """获取发送状态"""
        queue_stats = self.task_queue.get_stats()
        progress_info = self.batch_processor.get_progress_info()
        
        return {
            'status': self.status.value,
            'is_sending': self.is_sending,
            'is_paused': self.is_paused,
            'queue_stats': queue_stats,
            'sending_stats': self.sending_stats,
            'progress_info': progress_info,
            'worker_count': len(self.worker_threads),
            'config': {
                'mode': self.config.mode.value,
                'strategy': self.config.strategy.value,
                'concurrent_workers': self.config.concurrent_workers,
                'send_interval': self.config.send_interval
            }
        }
    
    def get_batch_list(self) -> List[Dict[str, Any]]:
        """获取批次列表"""
        return self.task_queue.get_all_batches_info()
    
    def get_data_sources(self) -> List[Dict[str, Any]]:
        """获取数据源列表"""
        return self.batch_processor.get_data_sources_info()
