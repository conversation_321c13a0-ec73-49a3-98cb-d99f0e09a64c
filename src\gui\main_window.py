#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块
应用程序的主界面，包含所有功能模块的入口
"""

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QMenuBar, QStatusBar, QAction,
    QMessageBox, QLabel, QPushButton, QTextEdit
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon, QFont
from typing import Dict, Any
from src.utils.logger import get_logger
from src.utils.config_manager import ConfigManager
from src.models.database import DatabaseManager
from src.gui.account_widget import AccountWidget
from src.gui.file_monitor_widget import FileMonitorWidget
# 移除邮件发送和轻量化发送模块

logger = get_logger("MainWindow")


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化主窗口

        Args:
            config: 应用程序配置
        """
        super().__init__()
        self.config = config

        # 初始化数据库
        config_manager = ConfigManager()
        db_path = config_manager.get_database_path()
        self.db_manager = DatabaseManager(str(db_path))

        self.init_ui()
        self.setup_status_bar()
        self.setup_menu_bar()

        logger.info("主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口基本属性
        self.setWindowTitle(self.config.get("app.name", "新浪邮箱自动化程序"))
        
        # 设置窗口大小和位置 - 增加高度以展示更多功能
        window_size = self.config.get("gui.window_size", [1600, 1000])  # 增加宽度和高度
        self.resize(window_size[0], window_size[1])

        # 设置最小窗口大小
        self.setMinimumSize(1400, 900)
        
        # 居中显示窗口
        self.center_window()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建标题区域
        title_layout = self.create_title_section()
        main_layout.addLayout(title_layout)
        
        # 创建选项卡控件
        self.tab_widget = QTabWidget()
        self.setup_tabs()
        main_layout.addWidget(self.tab_widget)
        
        # 设置样式
        self.setStyleSheet(self.get_stylesheet())
    
    def center_window(self):
        """将窗口居中显示"""
        screen = self.screen().availableGeometry()
        window = self.frameGeometry()
        window.moveCenter(screen.center())
        self.move(window.topLeft())
    
    def create_title_section(self) -> QHBoxLayout:
        """创建标题区域"""
        title_layout = QHBoxLayout()
        
        # 应用程序标题
        title_label = QLabel(self.config.get("app.name", "新浪邮箱自动化程序"))
        title_label.setObjectName("titleLabel")
        title_font = QFont("Microsoft YaHei", 16, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
        # 版本信息
        version_label = QLabel(f"v{self.config.get('app.version', '1.0.0')}")
        version_label.setObjectName("versionLabel")
        version_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(version_label)
        
        return title_layout
    
    def setup_tabs(self):
        """设置选项卡"""
        # 智能任务管理选项卡
        task_management_tab = self.create_task_management_tab()
        self.tab_widget.addTab(task_management_tab, "📋 智能任务管理")

        # 账号管理选项卡
        account_tab = self.create_account_tab()
        self.tab_widget.addTab(account_tab, "👤 账号管理")

        # 多浏览器发送选项卡（原有系统）
        multi_browser_tab = self.create_original_multi_browser_tab()
        self.tab_widget.addTab(multi_browser_tab, "🌐 多浏览器发送")

        # 强大的多浏览器发送选项卡（新系统）
        powerful_multi_browser_tab = self.create_powerful_multi_browser_tab()
        self.tab_widget.addTab(powerful_multi_browser_tab, "🚀 强大多浏览器发送")

        # 并发发送器选项卡
        concurrent_tab = self.create_concurrent_sender_tab()
        self.tab_widget.addTab(concurrent_tab, "⚡ 并发发送器")

        # 代理管理选项卡
        proxy_tab = self.create_proxy_management_tab()
        self.tab_widget.addTab(proxy_tab, "🌐 代理管理")

        # 文件监控选项卡
        monitor_tab = self.create_monitor_tab()
        self.tab_widget.addTab(monitor_tab, "📁 文件监控")

        # 日志查看选项卡
        log_tab = self.create_log_tab()
        self.tab_widget.addTab(log_tab, "📝 日志查看")
    


    def create_task_management_tab(self) -> QWidget:
        """创建智能任务管理选项卡"""
        try:
            # 导入新的任务管理界面
            from src.gui.task_management_window import TaskManagementWindow

            # 创建一个包装器，将tkinter界面嵌入到PyQt中
            tab = QWidget()
            layout = QVBoxLayout(tab)

            # 添加启动按钮
            start_button = QPushButton("🚀 启动智能任务管理系统")
            start_button.setMinimumHeight(60)
            start_button.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: bold;
                    padding: 10px;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
                QPushButton:pressed {
                    background-color: #3d8b40;
                }
            """)

            def launch_task_management():
                """启动任务管理系统"""
                try:
                    import threading

                    def start_in_thread():
                        """在新线程中启动任务管理系统"""
                        try:
                            # 创建并显示任务管理窗口
                            task_window = TaskManagementWindow()
                            task_window.show()

                            # 启动tkinter主循环
                            if task_window.window:
                                task_window.window.mainloop()

                        except Exception as e:
                            logger.error(f"任务管理系统线程异常: {e}")

                    # 在新线程中启动，避免阻塞主界面
                    thread = threading.Thread(target=start_in_thread, daemon=True)
                    thread.start()

                    logger.info("智能任务管理系统启动线程已创建")

                except Exception as e:
                    logger.error(f"启动任务管理系统失败: {e}")
                    QMessageBox.critical(self, "错误", f"启动任务管理系统失败:\n{e}")

            start_button.clicked.connect(launch_task_management)

            # 添加功能介绍
            info_label = QLabel("""
            <h2>📋 智能任务管理系统</h2>
            <p><b>全新的邮件发送流程：先添加任务，再点击发送</b></p>

            <h3>🎯 核心功能：</h3>
            <ul>
                <li><b>智能任务队列</b> - 5级优先级管理，智能负载均衡</li>
                <li><b>大数据量支持</b> - 支持几十万封邮件的分批处理</li>
                <li><b>分离式流程</b> - 任务管理与发送执行完全分离</li>
                <li><b>实时监控</b> - 发送进度、速度、状态实时显示</li>
                <li><b>灵活控制</b> - 暂停、恢复、停止随时控制</li>
            </ul>

            <h3>📝 使用流程：</h3>
            <ol>
                <li><b>添加任务</b> - 单个/批量/文件导入</li>
                <li><b>管理任务</b> - 查看批次状态和进度</li>
                <li><b>配置发送</b> - 设置发送策略和参数</li>
                <li><b>开始发送</b> - 点击发送按钮执行</li>
                <li><b>实时监控</b> - 监控发送进度和状态</li>
            </ol>

            <h3>🚀 性能特色：</h3>
            <ul>
                <li><b>大数据量</b> - 理论支持几十万封邮件</li>
                <li><b>智能分批</b> - 自适应批次大小调整</li>
                <li><b>并发发送</b> - 多工作线程并发处理</li>
                <li><b>内存优化</b> - 分批加载，内存可控</li>
            </ul>

            <p><i>点击上方按钮启动智能任务管理系统</i></p>
            """)
            info_label.setWordWrap(True)
            info_label.setStyleSheet("""
                QLabel {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 20px;
                    font-size: 12px;
                    line-height: 1.5;
                }
            """)

            layout.addWidget(start_button)
            layout.addWidget(info_label)
            layout.addStretch()

            return tab

        except Exception as e:
            logger.error(f"创建任务管理标签页失败: {e}")
            # 返回错误提示标签页
            tab = QWidget()
            layout = QVBoxLayout(tab)
            label = QLabel(f"智能任务管理系统加载失败\n错误: {e}")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            return tab

    def create_account_tab(self) -> QWidget:
        """创建账号管理选项卡"""
        return AccountWidget(self.db_manager, self.config)

    # 移除邮件发送和轻量化发送选项卡创建方法



    def create_original_multi_browser_tab(self) -> QWidget:
        """创建原有的多浏览器发送选项卡"""
        try:
            from src.gui.optimized_multi_browser_widget import OptimizedMultiBrowserWidget
            return OptimizedMultiBrowserWidget(self.db_manager)
        except ImportError as e:
            logger.error(f"导入原有多浏览器发送器失败: {e}")
            # 返回占位符标签页
            tab = QWidget()
            layout = QVBoxLayout(tab)

            # 添加功能介绍
            info_label = QLabel("""
            <h2>🌐 原有多浏览器邮件发送系统</h2>
            <p><b>原有的发送流程：先添加任务，再启动发送器，然后逐渐发送邮件</b></p>

            <h3>🎯 核心特色：</h3>
            <ul>
                <li><b>先添加任务</b> - 在邮件编辑标签页编辑邮件并添加到任务队列</li>
                <li><b>再启动发送器</b> - 点击启动发送器按钮开始发送流程</li>
                <li><b>逐渐发送邮件</b> - 按设定间隔逐个发送邮件任务</li>
                <li><b>实时监控</b> - 发送进度和状态实时显示</li>
                <li><b>灵活控制</b> - 暂停、恢复、停止随时控制</li>
            </ul>

            <h3>📝 使用流程：</h3>
            <ol>
                <li>在账号管理中添加和配置邮箱账号</li>
                <li>在邮件编辑标签页编辑邮件内容</li>
                <li>点击"添加到任务队列"按钮</li>
                <li>在多浏览器发送标签页配置发送参数</li>
                <li>点击"启动发送器"开始发送</li>
                <li>监控发送进度和状态</li>
            </ol>

            <h3>⚠️ 注意事项：</h3>
            <ul>
                <li>请先在账号管理中添加邮箱账号</li>
                <li>确保账号已经登录并保存了Cookie</li>
                <li>建议先用少量邮件测试</li>
                <li>注意发送间隔设置，避免被限制</li>
            </ul>
            """)
            info_label.setWordWrap(True)
            info_label.setAlignment(Qt.AlignTop)
            layout.addWidget(info_label)

            return tab
        except Exception as e:
            logger.error(f"创建原有多浏览器发送标签页失败: {e}")
            # 返回错误信息标签页
            tab = QWidget()
            layout = QVBoxLayout(tab)
            error_label = QLabel(f"创建原有多浏览器发送器失败: {str(e)}")
            error_label.setWordWrap(True)
            layout.addWidget(error_label)
            return tab

    def create_powerful_multi_browser_tab(self) -> QWidget:
        """创建强大的多浏览器发送选项卡"""
        try:
            from src.gui.powerful_multi_browser_widget import PowerfulMultiBrowserWidget

            # 创建强大的多浏览器发送控件
            powerful_widget = PowerfulMultiBrowserWidget()

            # 从数据库加载账号并设置到控件
            try:
                accounts = self.db_manager.get_all_accounts()
                if accounts:
                    powerful_widget.set_accounts(accounts)
                    logger.info(f"为强大的多浏览器发送器加载了 {len(accounts)} 个账号")
                else:
                    logger.warning("数据库中没有找到账号信息")
            except Exception as e:
                logger.error(f"加载账号信息失败: {e}")

            return powerful_widget

        except ImportError as e:
            logger.error(f"导入强大的多浏览器发送器失败: {e}")
            # 返回占位符标签页
            tab = QWidget()
            layout = QVBoxLayout(tab)

            # 添加功能介绍
            info_label = QLabel("""
            <h2>🚀 强大的多浏览器并发发送系统</h2>
            <p><b>真正的多账号并发发送，支持智能任务分发、实时监控、账号切换</b></p>

            <h3>🎯 核心特色：</h3>
            <ul>
                <li><b>真正并发</b> - 多个浏览器同时工作，不再是单线程</li>
                <li><b>智能分发</b> - 根据浏览器状态智能分发任务</li>
                <li><b>账号监控</b> - 精确监控每个账号的发送数量</li>
                <li><b>自动切换</b> - 达到限制后自动无感切换账号</li>
                <li><b>实时监控</b> - 详细的实时状态监控和统计</li>
                <li><b>负载均衡</b> - 智能负载均衡，最大化发送效率</li>
            </ul>

            <h3>📝 使用流程：</h3>
            <ol>
                <li><b>配置系统</b> - 设置浏览器数量和每账号发送限制</li>
                <li><b>初始化系统</b> - 点击"初始化系统"创建浏览器实例</li>
                <li><b>启动发送</b> - 点击"启动发送"开始并发发送</li>
                <li><b>添加任务</b> - 在快速任务区域添加邮件任务</li>
                <li><b>监控状态</b> - 实时监控每个浏览器和账号状态</li>
                <li><b>查看统计</b> - 在各个标签页查看详细统计信息</li>
            </ol>

            <h3>⚠️ 注意事项：</h3>
            <ul>
                <li>请先在账号管理中添加邮箱账号</li>
                <li>确保账号已经登录并保存了Cookie</li>
                <li>建议先用少量账号和任务测试</li>
                <li>监控系统资源使用情况</li>
                <li>注意发送间隔设置，避免被限制</li>
            </ul>

            <p><i>强大的多浏览器并发发送系统正在加载中...</i></p>
            """)
            info_label.setWordWrap(True)
            info_label.setStyleSheet("""
                QLabel {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 20px;
                    font-size: 12px;
                    line-height: 1.5;
                }
            """)

            layout.addWidget(info_label)
            return tab
        except Exception as e:
            logger.error(f"创建强大的多浏览器发送标签页失败: {e}")
            # 返回错误信息标签页
            tab = QWidget()
            layout = QVBoxLayout(tab)
            error_label = QLabel(f"创建强大的多浏览器发送器失败: {str(e)}")
            error_label.setWordWrap(True)
            layout.addWidget(error_label)
            return tab



    def create_proxy_management_tab(self) -> QWidget:
        """创建代理管理选项卡"""
        from src.models.account import AccountManager
        from src.gui.proxy_management_widget import ProxyManagementWidget

        account_manager = AccountManager(self.db_manager)

        return ProxyManagementWidget(account_manager)
    
    def create_monitor_tab(self) -> QWidget:
        """创建文件监控选项卡"""
        return FileMonitorWidget(self.config, self.db_manager)
    
    def create_log_tab(self) -> QWidget:
        """创建日志查看选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        return tab
    
    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 导入账号
        import_action = QAction("导入账号(&I)", self)
        import_action.setShortcut("Ctrl+I")
        import_action.triggered.connect(self.import_accounts)
        file_menu.addAction(import_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪")
        
        # 添加状态指示器
        self.status_label = QLabel("状态: 就绪")
        self.status_bar.addPermanentWidget(self.status_label)
    
    def get_stylesheet(self) -> str:
        """获取样式表"""
        return """
            QMainWindow {
                background-color: #f5f5f5;
            }
            
            #titleLabel {
                color: #2c3e50;
                padding: 10px;
            }
            
            #versionLabel {
                color: #7f8c8d;
                padding: 10px;
            }
            
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
            }
            
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 8px 16px;
                margin-right: 2px;
                border: 1px solid #bdc3c7;
                border-bottom: none;
            }
            
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
            
            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }
        """
    
    def import_accounts(self):
        """导入账号"""
        QMessageBox.information(self, "提示", "导入账号功能正在开发中...")
    
    def show_about(self):
        """显示关于对话框"""
        about_text = f"""
        <h3>{self.config.get('app.name', '新浪邮箱自动化程序')}</h3>
        <p>版本: {self.config.get('app.version', '1.0.0')}</p>
        <p>作者: {self.config.get('app.author', 'AI Assistant')}</p>
        <p>功能强大的Windows桌面应用程序，实现新浪邮箱的批量自动化操作</p>
        <p>主要功能:</p>
        <ul>
        <li>批量导入新浪邮箱账号和密码</li>
        <li>为每个账号配置独立的代理IP</li>
        <li>自动在浏览器中登录新浪邮箱</li>
        <li>按模板自动发送邮件</li>
        <li>实时监控文件夹中的txt文件</li>
        <li>智能轮换发送账号</li>
        </ul>
        """
        QMessageBox.about(self, "关于", about_text)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(
            self, 
            "确认退出", 
            "确定要退出程序吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            logger.info("用户确认退出程序")

    def create_concurrent_sender_tab(self) -> QWidget:
        """创建并发发送器选项卡"""
        try:
            from src.gui.concurrent_sender_widget import ConcurrentSenderWidget
            from src.models.account import AccountManager

            # 创建并发发送器控件
            concurrent_widget = ConcurrentSenderWidget()

            # 从数据库加载账号并设置到控件
            try:
                # 创建账号管理器实例
                account_manager = AccountManager(self.db_manager)
                accounts = account_manager.get_all_accounts()
                if accounts:
                    concurrent_widget.set_accounts(accounts)
                    logger.info(f"为并发发送器加载了 {len(accounts)} 个账号")
                else:
                    logger.warning("数据库中没有找到账号信息")
            except Exception as e:
                logger.error(f"加载账号信息失败: {e}")

            return concurrent_widget

        except ImportError as e:
            logger.error(f"导入并发发送器失败: {e}")
            # 返回占位符标签页
            tab = QWidget()
            layout = QVBoxLayout(tab)

            # 添加功能介绍
            info_label = QLabel("""
            <h2>⚡ 多账号并发发送器</h2>
            <p><b>全新的多账号并发发送系统，支持真正的多浏览器同时工作</b></p>

            <h3>🎯 核心特色：</h3>
            <ul>
                <li><b>真正并发</b> - 多个浏览器同时工作，不再是单线程</li>
                <li><b>智能账号切换</b> - 自动监控发送数量，达到限制后无感切换</li>
                <li><b>独立工作单元</b> - 每个浏览器都是独立的工作单元</li>
                <li><b>实时监控</b> - 每个浏览器和账号的状态实时显示</li>
                <li><b>智能任务分发</b> - 根据浏览器状态智能分发任务</li>
            </ul>

            <h3>📝 使用流程：</h3>
            <ol>
                <li>配置浏览器数量和每账号发送限制</li>
                <li>点击"初始化系统"创建浏览器实例</li>
                <li>点击"启动发送"开始并发发送</li>
                <li>添加邮件任务到队列</li>
                <li>系统自动分发任务并并发发送</li>
                <li>监控每个浏览器和账号的状态</li>
            </ol>

            <h3>⚠️ 注意事项：</h3>
            <ul>
                <li>请先在账号管理中添加邮箱账号</li>
                <li>确保账号已经登录并保存了Cookie</li>
                <li>建议先用少量账号测试</li>
                <li>监控系统资源使用情况</li>
            </ul>
            """)
            info_label.setWordWrap(True)
            info_label.setAlignment(Qt.AlignTop)
            layout.addWidget(info_label)

            return tab
        except Exception as e:
            logger.error(f"创建并发发送器标签页失败: {e}")
            # 返回错误信息标签页
            tab = QWidget()
            layout = QVBoxLayout(tab)
            error_label = QLabel(f"创建并发发送器失败: {str(e)}")
            error_label.setWordWrap(True)
            layout.addWidget(error_label)
            return tab

    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(
            self,
            "确认退出",
            "确定要退出程序吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            logger.info("用户确认退出程序")
            event.accept()
        else:
            event.ignore()
