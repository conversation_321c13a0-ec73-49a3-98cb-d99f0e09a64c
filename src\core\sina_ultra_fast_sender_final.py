#!/usr/bin/env python3
"""
新浪邮箱超高速发送器 - 最终完善版
基于真实测试结果的完整实现，支持cookies登录后的超高速发送
"""

import time
from typing import Optional, Dict, Any, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from src.utils.logger import setup_logger

logger = setup_logger("INFO")

class SinaUltraFastSenderFinal:
    """新浪邮箱超高速发送器 - 最终完善版
    
    完整功能:
    1. 智能登录状态检测
    2. 多策略写信按钮查找
    3. 多种发送方式支持
    4. 完整的错误处理和重试机制
    """
    
    def __init__(self, driver: webdriver.Chrome):
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        self.short_wait = WebDriverWait(driver, 3)
        self.is_ready = False
        self.send_count = 0
        self.success_count = 0
        
        # 完整的选择器配置 - 基于成功测试经验优化
        self.selectors = {
            # 写信按钮选择器 - 优先使用成功验证的选择器
            'write_buttons': [
                # 最高优先级 - 基于调试工具发现的真实选择器
                "//a[@title='写信']",  # 调试发现：真实的写信按钮有title='写信'
                "//a[contains(text(), '写信') and @title='写信']",  # 双重验证
                "//a[normalize-space(text())='写信']",  # 处理换行符情况

                # 高优先级 - 基于成功测试验证
                "//a[contains(text(), '写信')]",  # 测试中成功找到的选择器
                "//a[text()='写信']",  # 精确匹配

                # 中优先级 - 常见变体
                "//a[contains(text(), '写邮件')]",
                "//button[contains(text(), '写信')]",
                "//input[@value='写信']",

                # 低优先级 - 属性匹配
                "//a[@title='写邮件']",
                "//span[contains(text(), '写信')]//parent::a",
                "//div[contains(text(), '写信')]//a",

                # 更低优先级 - 链接和类匹配
                "//a[contains(@href, 'compose')]",
                "//a[contains(@href, 'write')]",
                "//a[contains(@href, 'writer')]",  # 基于URL中的action=writer
                "//a[contains(@class, 'write')]",
                "//a[contains(@class, 'compose')]",
                "//a[contains(@onclick, 'compose')]",
                "//a[contains(@onclick, 'writer')]",

                # 兜底选择器
                "//*[contains(text(), '写信')]",
                "//*[text()='写信']",
            ],
            
            # 收件人输入框 - 基于调试工具发现的真实选择器
            'to_inputs': [
                # 基于调试发现：第24个input，无name/id但可见可用
                "//input[@type='text'][1]",  # 第一个文本输入框
                "//input[@type='text' and not(@name) and not(@id) and not(@placeholder)]",

                # 通用备选
                "//input[@placeholder='收件人：']",
                "//input[contains(@placeholder, '收件人')]",
                "//input[@name='to']",
                "//input[@id='to']",
                "//input[contains(@name, 'to')]",
                "//input[contains(@name, 'mail')]",
                "//textarea[contains(@name, 'to')]",
                "//input[contains(@id, 'to')]",
                "//input[contains(@id, 'mail')]",
                "//input[contains(@class, 'to')]",
                "//input[contains(@class, 'recipient')]",

                # 更宽泛的搜索
                "//div[contains(@class, 'compose')]//input[@type='text']",
                "//form//input[@type='text']",
            ],

            # 主题输入框 - 基于调试结果的精确选择器
            'subject_inputs': [
                # 🎯 最精确的选择器：同时匹配name和class，确保选择正确元素
                "//input[@name='subj' and @class='input inp_base']",  # 最精确匹配！
                "//input[@name='subj' and contains(@class, 'inp_base')]",  # 稍微宽松的class匹配
                "//input[@name='subj']",  # name匹配（但可能有多个）

                # 🔍 基于调试发现的可见性过滤
                "//input[@name='subj' and @class='input inp_base'][@style[not(contains(., 'display:none'))] or not(@style)]",

                # 通用备选（保持原有逻辑）
                "//input[@class='input inp_base']",
                "//input[contains(@class, 'inp_base')]",
                "//input[@placeholder='主　题：']",
                "//input[contains(@placeholder, '主题')]",
                "//input[@name='subject']",
                "//input[@name='title']",
                "//input[contains(@name, 'subject')]",
                "//input[contains(@name, 'title')]",
                "//input[contains(@id, 'subject')]",
                "//input[contains(@id, 'title')]",
                "//input[contains(@class, 'subject')]",
                "//input[contains(@class, 'title')]",

                # 更精确的位置匹配 - 避免选择收件人字段附近的输入框
                "//tr[contains(.,'主') and contains(.,'题')]//input[@type='text']",  # 包含"主题"文字的行中的输入框
                "//td[contains(.,'主') and contains(.,'题')]//input[@type='text']",  # 包含"主题"文字的单元格中的输入框
                "//label[contains(.,'主题')]/..//input[@type='text']",  # 主题标签旁边的输入框
                "//label[contains(.,'主题')]/following-sibling::input[@type='text']",  # 主题标签后面的输入框

                # 🎯 增强选择器：基于页面结构
                "//td[contains(text(), '主题') or contains(text(), 'Subject')]//following-sibling::td//input",
                "//label[contains(text(), '主题')]//following-sibling::input",
                "//span[contains(text(), '主题')]//following-sibling::input",

                # 🎯 通过索引查找（收件人后面的输入框）
                "(//input[@type='text'])[2]",  # 第二个文本输入框
                "(//input[@type='text'])[3]",  # 第三个文本输入框（以防万一）

                # 🎯 兜底选择器
                "//input[@type='text'][not(@name='to') and not(@name='mailto')]",  # 排除收件人字段
            ],

            # 邮件内容区域 - 基于调试工具发现的真实选择器
            'content_areas': [
                # 基于调试发现：Iframe 5, class: iframe
                "//iframe[@class='iframe']",  # 精确匹配！
                "//iframe[contains(@class, 'iframe')]",

                # 通用iframe选择器
                "//iframe[@name='content']",
                "//iframe[@id='content']",
                "//iframe[contains(@name, 'editor')]",
                "//iframe[contains(@id, 'editor')]",
                "//iframe[contains(@src, 'editor')]",
                "//iframe[contains(@class, 'editor')]",

                # 富文本编辑器
                "//div[@contenteditable='true']",
                "//div[@contenteditable='']",

                # 通过位置查找
                "//div[contains(@class, 'compose')]//div[@contenteditable='true']",
                "//div[contains(@class, 'mail')]//div[@contenteditable='true']",
                "//form//div[@contenteditable='true']",

                # 🎯 增强iframe选择器
                "//iframe[contains(@src, 'compose')]",
                "//iframe[contains(@src, 'mail')]",
                "//iframe[contains(@name, 'compose')]",
                "//iframe[contains(@id, 'compose')]",

                # 🎯 通过索引查找iframe
                "(//iframe)[1]",  # 第一个iframe
                "(//iframe)[2]",  # 第二个iframe
                "//iframe",  # 任何iframe

                # 🎯 textarea备用选择器
                "//textarea[@name='content']",
                "//textarea[contains(@name, 'content')]",
                "//textarea[contains(@class, 'content')]",
                "//textarea",  # 任何textarea

                # textarea备选
                "//textarea[@name='content']",
                "//textarea[contains(@name, 'content')]",
                "//textarea[contains(@name, 'body')]",
                "//textarea[contains(@name, 'message')]",
                "//textarea[contains(@class, 'content')]",

                # 兜底选择器
                "//iframe",  # 任何iframe
                "//*[@contenteditable='true']",
                "//*[@contenteditable='']",
            ],

            # 发送按钮 - 基于真实HTML结构修正
            'send_buttons': [
                # 🎯 最高优先级：基于真实HTML结构的精确选择器
                "//a[.//i[contains(@class, 'icon_send')] and .//i[contains(@class, 'mailPubText') and text()='发送']]",
                "//a[.//i[contains(@class, 'icon_send')]]",  # 包含发送图标的链接
                "//a[.//i[text()='发送']]",  # 包含发送文字的链接
                "//a[contains(@href, 'javascript:void(0)') and .//i[text()='发送']]",

                # 🎯 高优先级：基于class和结构的选择器
                "//i[contains(@class, 'mailPubText') and text()='发送']/parent::a",
                "//i[contains(@class, 'icon_send')]/parent::a",
                "//a[contains(@class, 'mailPub') and .//text()[contains(., '发送')]]",

                # 🎯 中优先级：通用发送按钮选择器
                "//a[contains(text(), '发送')]",
                "//a[.//text()[contains(., '发送')]]",
                "//button[contains(text(), '发送')]",
                "//input[@value='发送']",
                "//input[@type='submit'][@value='发送']",

                # 🎯 低优先级：位置相关选择器
                "//div[contains(@class, 'toolbar')]//a[contains(text(), '发送')]",
                "//form//a[contains(text(), '发送')]",
                "//form//button[contains(text(), '发送')]",
                "//form//input[@type='submit']",

                # 🎯 兜底选择器
                "//*[contains(text(), '发送') and (self::a or self::button or self::input)]",
                "//input[@type='submit']",  # 任何提交按钮
            ]
        }
    
    def check_login_status(self) -> tuple[bool, str]:
        """智能检查登录状态"""
        try:
            current_url = self.driver.current_url.lower()
            page_title = self.driver.title
            
            logger.info(f"检查登录状态 - URL: {current_url}")
            logger.info(f"检查登录状态 - 标题: {page_title}")
            
            # 优先检查URL和标题
            if ('mail.sina.com.cn' in current_url and 
                ('index.php' in current_url or 'classic' in current_url) and
                ('新浪邮箱' in page_title or 'sina' in page_title.lower())):
                return True, "已进入新浪邮箱主界面"
            
            # 检查是否在登录页面
            if 'login' in current_url or 'passport' in current_url:
                return False, "仍在登录页面"
            
            # 检查页面内容
            page_source = self.driver.page_source
            mailbox_indicators = ['收件箱', 'inbox', '写信', 'compose', '发件箱', 'sent']
            
            if any(indicator in page_source for indicator in mailbox_indicators):
                return True, "检测到邮箱功能界面"
            
            # 如果在新浪邮箱域名下
            if 'mail.sina.com.cn' in current_url:
                return True, "在邮箱域名下，可能已登录"
            
            return False, "登录状态不明确"
            
        except Exception as e:
            logger.error(f"检查登录状态失败: {e}")
            return False, f"检查失败: {e}"
    
    def find_element_by_selectors(self, selectors: List[str], timeout: int = 2) -> Optional[Any]:
        """通过多个选择器查找元素"""
        for selector in selectors:
            try:
                element = WebDriverWait(self.driver, timeout).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                if element and element.is_displayed():
                    logger.debug(f"找到元素: {selector}")
                    return element
            except TimeoutException:
                continue
            except Exception as e:
                logger.debug(f"选择器失败 {selector}: {e}")
                continue
        return None
    
    def find_write_button(self) -> Optional[Any]:
        """智能查找写信按钮"""
        try:
            logger.info("查找写信按钮...")
            
            # 极速等待页面加载
            time.sleep(1)  # 减少等待时间

            # 使用预定义选择器 - 极速查找
            element = self.find_element_by_selectors(self.selectors['write_buttons'], timeout=2)
            if element:
                logger.info(f"找到写信按钮: {element.text}")
                return element
            
            # 如果预定义选择器失败，尝试智能搜索
            logger.info("预定义选择器失败，尝试智能搜索...")
            
            all_elements = (
                self.driver.find_elements(By.TAG_NAME, "a") +
                self.driver.find_elements(By.TAG_NAME, "button") +
                self.driver.find_elements(By.XPATH, "//input[@type='button' or @type='submit']")
            )
            
            for element in all_elements:
                try:
                    text = element.text.strip()
                    title = element.get_attribute('title') or ''
                    onclick = element.get_attribute('onclick') or ''
                    href = element.get_attribute('href') or ''
                    
                    combined_text = (text + title + onclick + href).lower()
                    
                    if any(keyword in combined_text for keyword in ['写信', '写邮件', 'compose', 'write']):
                        if element.is_displayed() and element.is_enabled():
                            logger.info(f"智能搜索找到写信按钮: {text}")
                            return element
                except:
                    continue
            
            logger.warning("未找到写信按钮")
            return None
            
        except Exception as e:
            logger.error(f"查找写信按钮失败: {e}")
            return None
    
    def prepare_compose_page(self) -> bool:
        """准备写邮件页面 - 修正版：每封邮件都必须重新点击写信按钮"""
        try:
            logger.info("🔧 准备写邮件页面 - 每封邮件都重新点击写信按钮...")

            # 检查登录状态
            login_success, login_message = self.check_login_status()
            if not login_success:
                logger.error(f"登录状态检查失败: {login_message}")
                return False

            logger.info(f"登录状态确认: {login_message}")

            # 关键修正：每封邮件都必须重新点击写信按钮！
            # 这样确保每次都是全新的写邮件界面，避免界面状态问题
            logger.info("🖱️ 重新点击'写信'按钮开始新的邮件编写...")

            # 超极速查找并点击写信按钮
            if not hasattr(self, 'ultra_compose_manager'):
                from src.core.ultra_speed_cookie_manager import UltraSpeedComposeManager
                self.ultra_compose_manager = UltraSpeedComposeManager(self.driver)

            # 使用超极速写信按钮管理器
            success = self.ultra_compose_manager.ultra_speed_find_and_click_compose()

            if success:
                self.is_ready = True
                logger.info("⚡ 超极速写信按钮点击成功")
                return True
            else:
                logger.error("❌ 超极速写信按钮点击失败")

                # 备用方案：使用原始方法
                logger.info("🔄 尝试备用写信按钮点击方案...")
                write_button = self.find_write_button()
                if write_button:
                    # 记录点击前的URL
                    before_url = self.driver.current_url
                    logger.info(f"✅ 找到写信按钮，立即点击...")
                    logger.info(f"点击前URL: {before_url}")

                    write_button.click()

                    # 关键修正：必须验证URL是否真的变化了
                    logger.info("⚡ 等待URL变化，验证写信按钮是否真的被点击...")

                    # 极速等待URL变化 - 基于成功经验优化
                    for i in range(6):  # 减少到最多等待6秒
                        time.sleep(0.5)  # 减少每次等待时间到0.5秒
                        after_url = self.driver.current_url

                        if 'action=writer' in after_url:
                            logger.info(f"✅ 写信按钮点击成功！URL已变化")
                            logger.info(f"点击后URL: {after_url}")
                            logger.info("✅ 已进入写邮件界面")
                            self.is_ready = True
                            return True
                        elif after_url != before_url:
                            logger.info(f"⚠️ URL发生变化但不是预期的写邮件界面")
                            logger.info(f"点击后URL: {after_url}")

                    # 如果URL没有变化，说明点击失败
                    final_url = self.driver.current_url
                    logger.error("❌ 写信按钮点击失败！URL没有变化")
                    logger.error(f"点击前URL: {before_url}")
                    logger.error(f"点击后URL: {final_url}")
                    logger.error("❌ 未能进入写邮件界面")
                    return False
                else:
                    logger.error("❌ 未找到写信按钮")
                    logger.error("❌ 无法显示右侧发件界面")
                    return False

        except Exception as e:
            logger.error(f"❌ 准备写邮件页面失败: {e}")
            return False
    
    def is_compose_interface_visible(self) -> bool:
        """检查写邮件界面是否可见 - 修正版：必须基于URL变化判断"""
        try:
            # 关键修正：必须基于URL变化判断，不能仅凭输入框存在
            current_url = self.driver.current_url
            logger.info(f"🔍 检查写邮件界面 - 当前URL: {current_url}")

            # 正确的判断逻辑：URL必须包含action=writer
            url_indicates_compose = 'action=writer' in current_url

            if url_indicates_compose:
                logger.info("✅ URL确认：已进入写邮件界面 (action=writer)")
                return True
            else:
                logger.warning("❌ URL确认：未进入写邮件界面")
                logger.info(f"   期望包含: action=writer")
                logger.info(f"   实际URL: {current_url}")
                logger.info("   必须点击'写信'按钮来进入写邮件界面")
                return False

        except Exception as e:
            logger.error(f"❌ 检查写邮件界面失败: {e}")
            return False
    
    def send_email_ultra_fast(self, to_email: str, subject: str, content: str) -> bool:
        """超高速发送邮件 - 修正版：每封邮件都必须重新点击写信按钮"""
        try:
            # 关键修正：每封邮件都必须重新点击写信按钮！
            logger.info("⚡ 每封邮件都需要重新点击写信按钮...")
            if not self.prepare_compose_page():
                logger.error("❌ 点击写信按钮失败")
                return False

            logger.info(f"🚀 开始超高速发送邮件到: {to_email}")
            logger.info("📝 正确流程：点击写信 → 填写收件人 → 填写主题 → 填写内容 → 点击发送")
            start_time = time.time()

            # 策略1: JavaScript超高速填写
            logger.info("🚀 尝试JavaScript超高速发送...")
            if self._send_with_javascript(to_email, subject, content):
                elapsed = time.time() - start_time
                logger.info(f"✅ JavaScript发送成功 ({elapsed:.2f}秒)")
                self.success_count += 1
                return True

            # 策略2: 直接元素操作
            logger.info("🔧 JavaScript失败，尝试元素操作发送...")
            if self._send_with_elements(to_email, subject, content):
                elapsed = time.time() - start_time
                logger.info(f"✅ 元素操作发送成功 ({elapsed:.2f}秒)")
                self.success_count += 1
                return True

            elapsed = time.time() - start_time
            logger.error(f"❌ 所有发送策略都失败了 ({elapsed:.2f}秒)")
            return False

        except Exception as e:
            logger.error(f"❌ 超高速发送异常: {e}")
            return False
        finally:
            self.send_count += 1
    
    def _send_with_javascript(self, to_email: str, subject: str, content: str) -> bool:
        """使用JavaScript发送"""
        try:
            logger.info("尝试JavaScript发送...")
            
            # 转义特殊字符
            safe_email = to_email.replace("'", "\\'").replace('"', '\\"')
            safe_subject = subject.replace("'", "\\'").replace('"', '\\"')
            safe_content = content.replace("'", "\\'").replace('"', '\\"').replace('\n', '\\n')
            
            js_code = f"""
            try {{
                console.log('🚀 开始JavaScript超高速填写和发送...');
                console.log('📝 正确流程: 点击写信 → 1.填写收件人 → 2.填写主题 → 3.填写内容 → 4.点击发送');
                console.log('✅ 写信按钮已点击，现在开始填写邮件内容...');
                var startTime = Date.now();

                // 步骤1: 填写收件人 - 基于成功经验的精确选择器
                var toField = document.querySelector('input[type="text"]') ||  // 测试成功的选择器！
                             document.querySelector('input[name="to"]') ||
                             document.querySelector('input[name="mailto"]') ||
                             document.querySelector('input[name*="to"]') ||
                             document.querySelector('input[placeholder*="收件人"]') ||
                             document.querySelector('textarea[name*="to"]') ||
                             document.querySelectorAll('input[type="text"]')[0];  // 第一个文本输入框

                if (toField && toField.offsetParent !== null) {{
                    toField.focus();
                    toField.value = '{safe_email}';
                    toField.dispatchEvent(new Event('input', {{bubbles: true}}));
                    toField.dispatchEvent(new Event('change', {{bubbles: true}}));
                    toField.dispatchEvent(new Event('blur', {{bubbles: true}}));
                    console.log('✅ 收件人已填写:', '{safe_email}');
                }} else {{
                    console.log('❌ 未找到收件人字段');
                    return false;
                }}

                // 步骤2: 填写主题 - 基于调试结果的精确选择器
                var subjectField =
                    // 🎯 最精确的选择器：同时匹配name和class
                    document.querySelector('input[name="subj"][class="input inp_base"]') ||
                    document.querySelector('input[name="subj"][class*="inp_base"]') ||
                    // 🔍 基于可见性过滤的选择器
                    (function() {{
                        var subjElements = document.querySelectorAll('input[name="subj"]');
                        for (var i = 0; i < subjElements.length; i++) {{
                            if (subjElements[i].offsetParent !== null &&
                                subjElements[i].className.includes('inp_base')) {{
                                return subjElements[i];
                            }}
                        }}
                        return null;
                    }})() ||
                    // 备用选择器
                    document.querySelector('input[name="subj"]') ||
                    document.querySelector('input[class*="inp_base"]') ||
                    document.querySelector('input[name="subject"]') ||
                    document.querySelector('input[name="title"]') ||
                    document.querySelector('input[name*="subject"]') ||
                    document.querySelector('input[placeholder*="主题"]');

                if (subjectField && subjectField.offsetParent !== null) {{
                    subjectField.focus();
                    subjectField.value = '{safe_subject}';
                    subjectField.dispatchEvent(new Event('input', {{bubbles: true}}));
                    subjectField.dispatchEvent(new Event('change', {{bubbles: true}}));
                    console.log('✅ 主题已填写:', '{safe_subject}');
                }}

                // 步骤3: 填写邮件内容 - 并行尝试多种方式
                var contentFilled = false;

                // 方式1: iframe编辑器 - 基于真实选择器优化
                var iframe = document.querySelector('iframe[class="iframe"]') ||  // 基于调试发现的真实选择器！
                           document.querySelector('iframe.iframe') ||  // CSS类选择器
                           document.querySelector('iframe[name="content"]') ||
                           document.querySelector('iframe[id="content"]') ||
                           document.querySelector('iframe[name*="editor"]') ||
                           document.querySelector('iframe[id*="editor"]') ||
                           document.querySelector('iframe');  // 任何iframe

                if (iframe && iframe.offsetParent !== null && !contentFilled) {{
                    try {{
                        var iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        var body = iframeDoc.body || iframeDoc.querySelector('body');
                        if (body) {{
                            body.innerHTML = '{safe_content}';
                            body.dispatchEvent(new Event('input', {{bubbles: true}}));
                            contentFilled = true;
                            console.log('✅ iframe内容已填写');
                        }}
                    }} catch(e) {{
                        console.log('iframe访问失败:', e);
                    }}
                }}

                // 方式2: 富文本编辑器
                if (!contentFilled) {{
                    var contentDiv = document.querySelector('div[contenteditable="true"]') ||
                                   document.querySelector('div[contenteditable=""]');
                    if (contentDiv && contentDiv.offsetParent !== null) {{
                        contentDiv.focus();
                        contentDiv.innerHTML = '{safe_content}';
                        contentDiv.dispatchEvent(new Event('input', {{bubbles: true}}));
                        contentFilled = true;
                        console.log('✅ 富文本编辑器内容已填写');
                    }}
                }}

                // 方式3: textarea
                if (!contentFilled) {{
                    var textarea = document.querySelector('textarea[name="content"]') ||
                                 document.querySelector('textarea[name*="content"]') ||
                                 document.querySelector('textarea[name*="body"]');
                    if (textarea && textarea.offsetParent !== null) {{
                        textarea.focus();
                        textarea.value = '{safe_content}';
                        textarea.dispatchEvent(new Event('input', {{bubbles: true}}));
                        contentFilled = true;
                        console.log('✅ textarea内容已填写');
                    }}
                }}

                if (!contentFilled) {{
                    console.log('❌ 内容填写失败，停止发送操作');
                    return false;
                }}

                // 步骤4: 点击发送按钮 - 基于成功经验的精确选择器
                var sendButton = document.querySelector('input[type="submit"][value="发送"]') ||
                               document.querySelector('input[value="发送"]') ||
                               document.querySelector('input[type="submit"][value*="发送"]') ||
                               document.querySelector('button[text()="发送"]') ||
                               document.querySelector('button:contains("发送")') ||
                               document.querySelector('button[contains(text(), "发送")]') ||
                               document.querySelector('input[type="submit"]') ||  // 任何提交按钮
                               document.querySelector('button[type="submit"]');

                if (sendButton && sendButton.offsetParent !== null) {{
                    console.log('✅ 找到发送按钮，立即发送...');
                    sendButton.focus();
                    sendButton.click();

                    var elapsed = Date.now() - startTime;
                    console.log('🚀 JavaScript发送完成，耗时:', elapsed + 'ms');
                    return true;
                }} else {{
                    console.log('❌ 未找到发送按钮');
                    return false;
                }}

            }} catch(e) {{
                console.error('❌ JavaScript发送失败:', e);
                return false;
            }}
            """
            
            result = self.driver.execute_script(js_code)
            
            if result:
                logger.info("✅ JavaScript超极速发送完成")
                # 🚀 使用超快速成功检查
                return self._ultra_fast_success_check()
            
            return False
            
        except Exception as e:
            logger.error(f"JavaScript发送失败: {e}")
            return False
    
    def _ultra_fast_fill_mode(self, elements: dict, to_email: str, subject: str, content: str, start_time: float) -> bool:
        """🚀 超极速填写模式 - 基于预加载元素的最快填写策略"""
        logger.info("⚡ 启动超极速填写模式...")

        try:
            # 🚀 并行填写策略：同时填写收件人和主题
            self.driver.execute_script("""
                // 超极速并行填写
                var toField = arguments[0];
                var subjectField = arguments[1];
                var toEmail = arguments[2];
                var subject = arguments[3];

                // 同时填写收件人和主题
                if (toField) {
                    toField.focus();
                    toField.value = toEmail;
                    toField.dispatchEvent(new Event('input', {bubbles: true}));
                }

                if (subjectField) {
                    subjectField.focus();
                    subjectField.value = subject;
                    subjectField.dispatchEvent(new Event('input', {bubbles: true}));
                }

                return {
                    toFilled: toField && toField.value.includes(toEmail),
                    subjectFilled: subjectField && subjectField.value.includes(subject)
                };
            """, elements['toField'], elements['subjectField'], to_email, subject)

            logger.info(f"✅ 收件人和主题并行填写完成: {to_email}, {subject}")

            # 🚀 超极速内容填写
            content_filled = False
            if elements['contentIframe']:
                try:
                    self.driver.switch_to.frame(elements['contentIframe'])
                    body = self.driver.find_element(By.TAG_NAME, "body")
                    self.driver.execute_script("arguments[0].innerHTML = arguments[1];", body, content)
                    self.driver.switch_to.default_content()
                    content_filled = True
                    logger.info("✅ iframe内容超极速填写完成")
                except:
                    self.driver.switch_to.default_content()

            if not content_filled and elements['textarea']:
                self.driver.execute_script("""
                    arguments[0].focus();
                    arguments[0].value = arguments[1];
                    arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                """, elements['textarea'], content)
                content_filled = True
                logger.info("✅ textarea内容超极速填写完成")

            if not content_filled:
                logger.error("❌ 内容填写失败")
                return False

            # 🚀 超极速发送
            self.driver.execute_script("arguments[0].click();", elements['sendButton'])
            elapsed = time.time() - start_time
            logger.info(f"✅ 超极速发送完成 (总耗时: {elapsed:.2f}秒)")

            # 🚀 超快速成功检查
            return self._ultra_fast_success_check()

        except Exception as e:
            logger.error(f"❌ 超极速填写模式失败: {e}")
            return False

    def _send_with_elements(self, to_email: str, subject: str, content: str) -> bool:
        """使用元素操作发送 - 修正版：在点击写信按钮后进行"""
        try:
            logger.info("🔧 尝试元素操作发送（写信按钮已点击）...")
            start_time = time.time()

            # 🚀 策略1: 预加载所有必需元素，减少查找时间
            logger.info("⚡ 预加载所有邮件元素...")
            elements = self.driver.execute_script("""
                // 基于截图界面的精确元素定位
                var elements = {
                    // 收件人输入框 - 基于截图红框位置
                    toField: document.querySelector('input[type="text"]') ||
                             document.querySelector('input[name="to"]') ||
                             document.querySelector('input[placeholder*="收件人"]'),

                    // 主题输入框 - 基于截图红框位置
                    subjectField: document.querySelector('input[name="subj"][class="input inp_base"]') ||
                                  document.querySelector('input[name="subj"]') ||
                                  document.querySelector('input[placeholder*="主题"]'),

                    // 发送按钮 - 基于截图左上角位置
                    sendButton: document.querySelector('input[type="submit"][value="发送"]') ||
                                document.querySelector('button[type="submit"]') ||
                                document.querySelector('input[value="发送"]'),

                    // 内容编辑器iframe - 基于截图大红框区域
                    contentIframe: document.querySelector('iframe[class="iframe"]') ||
                                   document.querySelector('iframe'),

                    // 备用textarea
                    textarea: document.querySelector('textarea')
                };

                // 验证元素可用性
                var validation = {
                    toFieldReady: elements.toField && elements.toField.offsetParent !== null,
                    subjectFieldReady: elements.subjectField && elements.subjectField.offsetParent !== null,
                    sendButtonReady: elements.sendButton && elements.sendButton.offsetParent !== null,
                    contentReady: elements.contentIframe || elements.textarea
                };

                return {elements: elements, validation: validation};
            """)

            if (elements['validation']['toFieldReady'] and
                elements['validation']['subjectFieldReady'] and
                elements['validation']['sendButtonReady'] and
                elements['validation']['contentReady']):
                logger.info("✅ 所有关键元素预加载成功，启动超极速填写模式")
                return self._ultra_fast_fill_mode(elements['elements'], to_email, subject, content, start_time)
            else:
                logger.warning("⚠️ 预加载部分失败，使用传统查找模式")
                # 继续使用原有逻辑作为备用方案

            # 🚀 超极速收件人填写 - 增强容错性
            to_field = self.find_element_by_selectors(self.selectors['to_inputs'], timeout=0.3)

            # 如果第一次查找失败，尝试刷新页面状态并重新查找
            if not to_field:
                logger.warning("⚠️ 第一次未找到收件人字段，尝试页面状态恢复...")
                try:
                    # 尝试点击页面空白区域，确保页面状态正常
                    self.driver.execute_script("document.body.click();")
                    time.sleep(0.2)

                    # 重新查找，使用更长的超时时间
                    to_field = self.find_element_by_selectors(self.selectors['to_inputs'], timeout=1.0)

                    if not to_field:
                        # 尝试更宽泛的选择器
                        logger.warning("⚠️ 使用备用选择器查找收件人字段...")
                        backup_selectors = [
                            "//input[@type='text']",  # 最宽泛的文本输入框
                            "//div[contains(@class, 'compose')]//input",  # 写邮件区域的输入框
                            "//form//input[@type='text']",  # 表单中的文本输入框
                            "//input[not(@type) or @type='text']",  # 无类型或文本类型的输入框
                        ]
                        to_field = self.find_element_by_selectors(backup_selectors, timeout=1.0)

                except Exception as e:
                    logger.warning(f"⚠️ 页面状态恢复失败: {e}")

            if to_field:
                # 🚀 极速填写策略：直接JavaScript填写，无需验证（基于成功经验）
                self.driver.execute_script("""
                    arguments[0].focus();
                    arguments[0].value = arguments[1];
                    arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                    arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                """, to_field, to_email)
                logger.info(f"✅ 收件人超极速填写: {to_email}")
            else:
                logger.error("❌ 所有尝试都失败，未找到收件人字段")
                return False

            # 🎯 精确主题填写 - 基于调试结果优化
            subject_field = self.find_element_by_selectors(self.selectors['subject_inputs'], timeout=0.3)  # 减少超时时间
            if subject_field:
                # 验证选中的元素是否正确
                element_info = self.driver.execute_script("""
                    return {
                        name: arguments[0].name,
                        className: arguments[0].className,
                        visible: arguments[0].offsetParent !== null
                    };
                """, subject_field)

                # 🚀 基于成功经验：直接填写，减少验证步骤
                if element_info['name'] == 'subj' and 'inp_base' in element_info['className']:
                    logger.info(f"✅ 正确选中主题字段: name={element_info['name']}, class={element_info['className']}")
                    # 🚀 极速填写策略：直接JavaScript填写，无需验证（基于成功经验）
                    self.driver.execute_script("""
                        arguments[0].focus();
                        arguments[0].value = arguments[1];
                        arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                        arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                    """, subject_field, subject)
                    logger.info(f"✅ 主题超极速填写成功: {subject}")
                else:
                    logger.warning(f"⚠️ 选中错误元素，使用备用方案: name={element_info['name']}, class={element_info['className']}")
                    # 备用方案：重新查找正确元素
                    try:
                        correct_subject_field = self.driver.find_element(By.XPATH, "//input[@name='subj' and @class='input inp_base']")
                        self.driver.execute_script("""
                            arguments[0].focus();
                            arguments[0].value = arguments[1];
                            arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                            arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                        """, correct_subject_field, subject)
                        logger.info(f"✅ 主题备用方式填写成功: {subject}")
                    except Exception as e:
                        logger.error(f"❌ 主题填写完全失败: {e}")
                        return False
            else:
                logger.error("❌ 未找到主题字段！尝试所有选择器都失败了")
                # 调试信息：显示页面上所有的input元素
                try:
                    all_inputs = self.driver.find_elements(By.XPATH, "//input[@type='text']")
                    logger.info(f"🔍 页面上共有 {len(all_inputs)} 个文本输入框")
                    for i, inp in enumerate(all_inputs[:5]):  # 只显示前5个
                        name = inp.get_attribute('name') or 'None'
                        class_attr = inp.get_attribute('class') or 'None'
                        placeholder = inp.get_attribute('placeholder') or 'None'
                        logger.info(f"  输入框{i+1}: name='{name}', class='{class_attr}', placeholder='{placeholder}'")
                except:
                    pass

            # 极速内容填写
            content_filled = self._fill_content_optimized(content)
            if content_filled:
                logger.info("✅ 内容填写成功")
            else:
                logger.error("❌ 内容填写失败，停止发送操作")
                return False

            # 🚀 超极速发送操作 - 基于成功经验优化
            send_button = self.find_element_by_selectors(self.selectors['send_buttons'], timeout=0.3)  # 进一步减少超时时间
            if send_button:
                # 🚀 极速点击策略：直接JavaScript点击，基于成功经验
                self.driver.execute_script("arguments[0].click();", send_button)
                elapsed = time.time() - start_time
                logger.info(f"✅ 发送按钮超极速点击完成 (耗时: {elapsed:.2f}秒)")

                # 🚀 超极速成功检查
                return self._check_send_success_fast()
            else:
                logger.error("❌ 未找到发送按钮")
                return False

        except Exception as e:
            logger.error(f"❌ 元素操作发送失败: {e}")
            return False
    
    def _fill_content_optimized(self, content: str) -> bool:
        """优化的邮件内容填写"""
        try:
            logger.info("🎯 开始超级优化内容填写...")

            # 🚀 超级策略0: 先尝试直接可见的textarea和可编辑div
            try:
                # 尝试所有可见的textarea
                textareas = self.driver.find_elements(By.TAG_NAME, "textarea")
                logger.info(f"🔍 找到 {len(textareas)} 个textarea")

                for i, textarea in enumerate(textareas):
                    try:
                        # 强制显示检查
                        is_displayed = self.driver.execute_script("return arguments[0].offsetParent !== null;", textarea)
                        is_enabled = textarea.is_enabled()

                        logger.info(f"🔍 检查textarea{i+1}: displayed={is_displayed}, enabled={is_enabled}")

                        if is_displayed and is_enabled:
                            logger.info(f"🎯 找到可用textarea{i+1}，直接填写")
                            # 使用JavaScript强制填写
                            self.driver.execute_script("arguments[0].value = arguments[1];", textarea, content)
                            self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", textarea)
                            logger.info("✅ 超级策略0成功：直接textarea填写")
                            return True
                    except Exception as e:
                        logger.debug(f"textarea{i+1}填写失败: {e}")

                # 尝试所有可编辑div
                editable_divs = self.driver.find_elements(By.XPATH, "//div[@contenteditable='true'] | //div[@contenteditable=''] | //*[@contenteditable='true']")
                logger.info(f"🔍 找到 {len(editable_divs)} 个可编辑元素")

                for i, div in enumerate(editable_divs):
                    try:
                        is_displayed = self.driver.execute_script("return arguments[0].offsetParent !== null;", div)

                        if is_displayed:
                            logger.info(f"🎯 找到可编辑div{i+1}，直接填写")
                            # 使用JavaScript强制填写
                            self.driver.execute_script("arguments[0].innerHTML = arguments[1];", div, content)
                            self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", div)
                            logger.info("✅ 超级策略0成功：可编辑div填写")
                            return True
                    except Exception as e:
                        logger.debug(f"可编辑div{i+1}填写失败: {e}")

            except Exception as e:
                logger.debug(f"超级策略0失败: {e}")

            # 🚀 超极速策略1: iframe编辑器 - 基于成功经验优化
            iframe_selectors = [
                # 🎯 最高优先级：基于成功经验的精确选择器
                "//iframe[@class='iframe']",  # 100%成功的选择器！
                # 备用选择器（减少数量，提高速度）
                "//iframe[contains(@class, 'iframe')]",
                "//iframe"  # 最后兜底
            ]

            for selector in iframe_selectors:
                try:
                    iframe = self.driver.find_element(By.XPATH, selector)
                    if iframe and iframe.is_displayed():
                        logger.info(f"🎯 尝试iframe选择器: {selector}")
                        self.driver.switch_to.frame(iframe)

                        # 多种方式尝试找到编辑区域
                        edit_targets = [
                            ("body", By.TAG_NAME, "body"),
                            ("div[contenteditable]", By.XPATH, "//div[@contenteditable='true']"),
                            ("div[contenteditable]", By.XPATH, "//div[@contenteditable='']"),
                            ("任何div", By.TAG_NAME, "div"),
                            ("html", By.TAG_NAME, "html")
                        ]

                        for target_name, by_type, target_selector in edit_targets:
                            try:
                                target = self.driver.find_element(by_type, target_selector)
                                if target:
                                    logger.info(f"🎯 尝试填写到: {target_name}")

                                    # 多种填写方式
                                    target.click()  # 确保焦点

                                    # 方式1: 直接send_keys
                                    try:
                                        target.clear()
                                        target.send_keys(content)
                                        logger.info(f"✅ 方式1成功: send_keys")
                                    except:
                                        # 方式2: JavaScript填写
                                        self.driver.execute_script("arguments[0].innerHTML = arguments[1];", target, content)
                                        logger.info(f"✅ 方式2成功: innerHTML")

                                    # 触发事件
                                    self.driver.execute_script("""
                                        arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                                        arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                                    """, target)

                                    self.driver.switch_to.default_content()
                                    logger.info(f"✅ iframe内容已填写 (选择器: {selector}, 目标: {target_name})")
                                    return True
                            except Exception as inner_e:
                                logger.debug(f"目标 {target_name} 失败: {inner_e}")
                                continue

                        self.driver.switch_to.default_content()

                except Exception as e:
                    self.driver.switch_to.default_content()  # 确保退出iframe
                    logger.debug(f"iframe选择器失败 {selector}: {e}")
                    continue

            # 🚀 超级策略1.5: 强制遍历所有iframe
            try:
                all_iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
                logger.info(f"🔍 强制检查所有 {len(all_iframes)} 个iframe")

                for i, iframe in enumerate(all_iframes):
                    try:
                        logger.info(f"🎯 强制检查iframe{i+1}")
                        self.driver.switch_to.frame(iframe)

                        # 强制尝试所有可能的编辑元素
                        edit_elements = []

                        # 收集所有可能的编辑元素
                        try:
                            edit_elements.extend(self.driver.find_elements(By.TAG_NAME, "body"))
                            edit_elements.extend(self.driver.find_elements(By.TAG_NAME, "textarea"))
                            edit_elements.extend(self.driver.find_elements(By.XPATH, "//*[@contenteditable='true']"))
                            edit_elements.extend(self.driver.find_elements(By.XPATH, "//*[@contenteditable='']"))
                            edit_elements.extend(self.driver.find_elements(By.TAG_NAME, "div"))
                        except:
                            pass

                        for j, element in enumerate(edit_elements):
                            try:
                                # 强制JavaScript填写
                                self.driver.execute_script("""
                                    arguments[0].innerHTML = arguments[1];
                                    arguments[0].textContent = arguments[1];
                                    arguments[0].value = arguments[1];
                                    arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                                    arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                                """, element, content)

                                # 验证是否填写成功
                                filled_content = self.driver.execute_script("""
                                    return arguments[0].innerHTML || arguments[0].textContent || arguments[0].value || '';
                                """, element)

                                if content in filled_content:
                                    self.driver.switch_to.default_content()
                                    logger.info(f"✅ 强制策略成功：iframe{i+1}元素{j+1}")
                                    return True

                            except Exception as ee:
                                continue

                        self.driver.switch_to.default_content()

                    except Exception as e:
                        self.driver.switch_to.default_content()
                        continue

            except Exception as e:
                logger.debug(f"强制iframe策略失败: {e}")
                self.driver.switch_to.default_content()

            # 策略2: 富文本编辑器
            contenteditable_selectors = [
                "//div[@contenteditable='true']",
                "//div[@contenteditable='']"
            ]

            for selector in contenteditable_selectors:
                try:
                    content_div = self.driver.find_element(By.XPATH, selector)
                    if content_div and content_div.is_displayed():
                        content_div.click()
                        content_div.clear()
                        content_div.send_keys(content)

                        # 触发事件
                        self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", content_div)

                        logger.info(f"✅ 富文本编辑器内容已填写 (选择器: {selector})")
                        return True
                except Exception as e:
                    logger.debug(f"富文本编辑器选择器失败 {selector}: {e}")
                    continue

            # 🚀 策略3: textarea - 优化版
            textarea_selectors = [
                "//textarea[@name='content']",
                "//textarea[contains(@name, 'content')]",
                "//textarea[contains(@name, 'body')]",
                "//textarea[contains(@name, 'message')]"
            ]

            for selector in textarea_selectors:
                try:
                    textarea = self.driver.find_element(By.XPATH, selector)
                    if textarea and textarea.is_displayed():
                        # 🚀 使用JavaScript填写，更可靠
                        self.driver.execute_script("""
                            arguments[0].focus();
                            arguments[0].value = arguments[1];
                            arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                            arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                        """, textarea, content)

                        logger.info(f"✅ textarea内容已填写 (选择器: {selector})")
                        return True
                except Exception as e:
                    logger.debug(f"textarea选择器失败 {selector}: {e}")
                    continue

            # 🚀 策略4: 通用textarea查找 - 查找所有可见的textarea
            try:
                textareas = self.driver.find_elements(By.TAG_NAME, "textarea")
                logger.info(f"🔍 找到 {len(textareas)} 个textarea")
                for i, textarea in enumerate(textareas):
                    try:
                        logger.info(f"🔍 检查textarea{i+1}: displayed={textarea.is_displayed()}, enabled={textarea.is_enabled()}")
                        if textarea.is_displayed() and textarea.is_enabled():
                            # 🚀 多种填写方式尝试
                            try:
                                # 方式1: JavaScript填写
                                self.driver.execute_script("""
                                    arguments[0].focus();
                                    arguments[0].value = arguments[1];
                                    arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                                    arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
                                """, textarea, content)
                                logger.info(f"✅ textarea{i+1}内容已填写 (JavaScript方式)")
                                return True
                            except Exception as js_error:
                                logger.warning(f"⚠️ textarea{i+1} JavaScript填写失败: {js_error}")
                                # 方式2: 传统填写
                                try:
                                    textarea.clear()
                                    textarea.send_keys(content)
                                    logger.info(f"✅ textarea{i+1}内容已填写 (传统方式)")
                                    return True
                                except Exception as traditional_error:
                                    logger.error(f"❌ textarea{i+1} 传统填写也失败: {traditional_error}")
                        else:
                            logger.warning(f"⚠️ textarea{i+1} 不可见或不可用")
                    except Exception as e:
                        logger.error(f"❌ textarea{i+1}检查失败: {e}")
                        continue
            except Exception as e:
                logger.debug(f"通用textarea查找失败: {e}")

            logger.warning("⚠️ 所有内容填写策略都失败了")

            # 🔍 调试信息：显示页面上的iframe和可编辑元素
            try:
                all_iframes = self.driver.find_elements(By.XPATH, "//iframe")
                logger.info(f"🔍 页面上共有 {len(all_iframes)} 个iframe")
                for i, iframe in enumerate(all_iframes[:3]):  # 只显示前3个
                    src = iframe.get_attribute('src') or 'None'
                    class_attr = iframe.get_attribute('class') or 'None'
                    name = iframe.get_attribute('name') or 'None'
                    logger.info(f"  iframe{i+1}: src='{src}', class='{class_attr}', name='{name}'")

                all_textareas = self.driver.find_elements(By.XPATH, "//textarea")
                logger.info(f"🔍 页面上共有 {len(all_textareas)} 个textarea")

                all_contenteditable = self.driver.find_elements(By.XPATH, "//div[@contenteditable='true']")
                logger.info(f"🔍 页面上共有 {len(all_contenteditable)} 个可编辑div")

            except Exception as debug_error:
                logger.debug(f"调试信息获取失败: {debug_error}")

            return False

        except Exception as e:
            logger.error(f"❌ 填写内容失败: {e}")
            return False

    def _ultra_fast_success_check(self) -> bool:
        """🚀 超快速成功检查 - 基于成功经验的最快验证"""
        try:
            # 🚀 超快速检查 - 只等待1秒
            max_wait = 1.0
            check_interval = 0.2
            start_time = time.time()

            while time.time() - start_time < max_wait:
                # 🚀 JavaScript快速检查多个成功标志
                result = self.driver.execute_script("""
                    // 检查多种成功标志
                    var indicators = {
                        successText: document.body.innerText.includes('您的邮件已发送') ||
                                    document.body.innerText.includes('发送成功') ||
                                    document.body.innerText.includes('邮件已发送'),

                        urlChange: window.location.href.includes('success') ||
                                  window.location.href.includes('sent'),

                        alertPresent: document.querySelector('.alert-success') ||
                                     document.querySelector('.success-message') ||
                                     document.querySelector('[class*="success"]')
                    };

                    return {
                        success: indicators.successText || indicators.urlChange || indicators.alertPresent,
                        details: indicators
                    };
                """)

                if result['success']:
                    elapsed = time.time() - start_time
                    logger.info(f"✅ 发送成功确认: 您的邮件已发送 (等待{elapsed:.1f}秒)")
                    return True

                time.sleep(check_interval)

            # 超时后的最后检查
            logger.warning("⚠️ 快速检查超时，进行最后确认")
            final_check = self.driver.execute_script("""
                return document.body.innerText.includes('您的邮件已发送') ||
                       document.body.innerText.includes('发送成功');
            """)

            if final_check:
                logger.info("✅ 最后确认：邮件发送成功")
                return True
            else:
                logger.error("❌ 邮件发送可能失败")
                return False

        except Exception as e:
            logger.error(f"❌ 成功检查失败: {e}")
            return False

    def _check_send_success_fast(self) -> bool:
        """第6步：检查发送结果 - 包含成功检查和账号频繁限制处理"""
        try:
            logger.info("🔍 第6步：检查发送结果...")

            # 🚀 超快速检查 - 基于成功经验进一步减少等待时间
            max_wait = 2  # 最多等待2秒（基于成功经验，通常0.5秒就能确认）
            check_interval = 0.3  # 减少检查间隔
            waited = 0

            while waited < max_wait:
                time.sleep(check_interval)
                waited += check_interval

                page_source = self.driver.page_source
                current_url = self.driver.current_url.lower()

                # 🚨 优先检查账号频繁限制
                frequency_limit_indicators = [
                    "发信过于频繁",
                    "请1小时后再试",
                    "发送频率过高",
                    "请稍后再试",
                    "频繁发送",
                    "操作过于频繁",
                    "m0.mail.sina.com.cn 显示",
                ]

                for indicator in frequency_limit_indicators:
                    if indicator in page_source:
                        logger.error(f"🚨 账号频繁限制检测: {indicator}")
                        logger.error("❌ 账号发送过于频繁，需要冷却处理")
                        logger.info("🔄 建议：将此账号冷却60分钟，立即切换其他账号")

                        # 标记账号需要冷却
                        self._mark_account_for_cooling()
                        return False  # 返回False表示需要切换账号

                # ✅ 检查发送成功消息
                page_source_lower = page_source.lower()
                success_indicators = [
                    '您的邮件已发送',  # 真实界面显示的文字！
                    '此邮件发送成功，并已保存到',
                    '邮件已发送', '发送成功', '已发送', '发送完成',
                    'sent successfully', 'message sent',
                    '已成功发送', '发送邮件成功',
                    '邮件发送成功', '成功发送'
                ]
                for indicator in success_indicators:
                    if indicator in page_source_lower:
                        logger.info(f"✅ 第6步成功：发送成功确认 - {indicator} (等待{waited:.1f}秒)")
                        return True

                # URL变化检查 - 发送成功后的URL变化
                if 'sent' in current_url or 'success' in current_url or 'mailinfo' in current_url:
                    logger.info(f"✅ 第6步成功：发送成功确认 - URL变化 (等待{waited:.1f}秒)")
                    return True

                # 🎯 界面元素检查 - 基于真实发送成功界面
                try:
                    # 检查绿色对勾和"您的邮件已发送"文字
                    success_elements = self.driver.find_elements(By.XPATH,
                        "//*[contains(text(), '您的邮件已发送') or contains(text(), '邮件已发送') or contains(text(), '发送成功')]")
                    if success_elements:
                        logger.info(f"✅ 发送成功确认: 找到成功提示元素 (等待{waited:.1f}秒)")
                        return True

                    # 检查绿色对勾图标
                    check_icons = self.driver.find_elements(By.XPATH,
                        "//i[contains(@class, 'check') or contains(@class, 'success')] | //div[contains(@class, 'success')]")
                    if check_icons:
                        logger.info(f"✅ 发送成功确认: 找到成功图标 (等待{waited:.1f}秒)")
                        return True
                except:
                    pass

                # 错误指示器
                error_indicators = ['发送失败', 'send failed', 'error', '错误', '失败']
                for indicator in error_indicators:
                    if indicator in page_source:
                        logger.warning(f"❌ 发送失败确认: {indicator}")
                        return False

            # 超时后的最终检查
            logger.info(f"⏰ 快速检查超时({max_wait}秒)，进行最终判断...")

            # 如果没有明确的失败指示器，假设成功
            page_source = self.driver.page_source.lower()
            error_indicators = ['发送失败', 'send failed', 'error', '错误']

            for indicator in error_indicators:
                if indicator in page_source:
                    logger.warning(f"❌ 最终检查发现失败指示器: {indicator}")
                    return False

            logger.info("🤔 无明确指示器，假设发送成功")
            return True

        except Exception as e:
            logger.error(f"❌ 第6步异常：发送结果检查异常 - {e}")
            return False

    def _mark_account_for_cooling(self):
        """标记账号需要冷却 - 账号频繁限制处理"""
        try:
            import time

            # 记录冷却信息
            cooling_info = {
                'account': getattr(self, 'current_account', 'unknown'),
                'cooling_start_time': time.time(),
                'cooling_duration': 60 * 60,  # 60分钟冷却
                'reason': '发送过于频繁'
            }

            logger.info(f"🧊 账号冷却标记: {cooling_info['account']}")
            logger.info(f"⏰ 冷却时间: {cooling_info['cooling_duration'] // 60}分钟")
            logger.info(f"📝 冷却原因: {cooling_info['reason']}")

            # 这里可以将冷却信息保存到文件或数据库
            # 供多浏览器发送系统使用，以便切换账号

        except Exception as e:
            logger.error(f"❌ 账号冷却标记失败: {e}")

    def _check_send_success(self) -> bool:
        """检查发送是否成功"""
        try:
            time.sleep(2)
            page_source = self.driver.page_source.lower()
            
            success_indicators = ['发送成功', '已发送', 'sent successfully', '发送完成']
            error_indicators = ['发送失败', 'send failed', 'error', '错误']
            
            for indicator in success_indicators:
                if indicator in page_source:
                    logger.info(f"发送成功确认: {indicator}")
                    return True
            
            for indicator in error_indicators:
                if indicator in page_source:
                    logger.warning(f"发送失败确认: {indicator}")
                    return False
            
            logger.info("无明确指示器，假设发送成功")
            return True
            
        except Exception as e:
            logger.error(f"检查发送结果失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取发送统计"""
        success_rate = (self.success_count / max(self.send_count, 1)) * 100
        return {
            'send_count': self.send_count,
            'success_count': self.success_count,
            'fail_count': self.send_count - self.success_count,
            'success_rate': round(success_rate, 1)
        }
    
    def reset_for_next_email(self) -> bool:
        """为下一封邮件重置状态"""
        try:
            logger.info("为下一封邮件重置状态...")
            return self.prepare_compose_page()
        except Exception as e:
            logger.error(f"重置状态失败: {e}")
            return False

    def quick_reset_for_continuous_sending(self) -> bool:
        """快速重置用于连续发送 - 直接点击写信按钮开始下一封"""
        try:
            logger.info("⚡ 连续发送重置：直接点击写信按钮开始下一封...")

            # 根据用户指正：发送成功后不需要返回主页
            # 写信按钮就在原来的位置，直接点击即可
            # 正确流程：点击写信 → 填写收件人 → 填写主题 → 填写内容 → 点击发送

            logger.info("⚡ 直接点击写信按钮开始下一封邮件...")
            return self.prepare_compose_page()

        except Exception as e:
            logger.error(f"❌ 快速重置失败: {e}")
            # 如果快速重置失败，尝试完整重置
            try:
                logger.info("⚡ 尝试完整重置...")
                return self.prepare_compose_page()
            except:
                return False

    def _quick_clear_compose_fields(self):
        """快速清空写邮件字段 - 增强版本"""
        try:
            # 使用JavaScript快速清空所有输入字段
            clear_script = """
                // 清空所有文本输入框
                var inputs = document.querySelectorAll('input[type="text"], input:not([type])');
                for (var i = 0; i < inputs.length; i++) {
                    if (inputs[i].offsetParent !== null) { // 只清空可见的输入框
                        inputs[i].value = '';
                        inputs[i].dispatchEvent(new Event('input', {bubbles: true}));
                        inputs[i].dispatchEvent(new Event('change', {bubbles: true}));
                    }
                }

                // 清空所有textarea
                var textareas = document.querySelectorAll('textarea');
                for (var i = 0; i < textareas.length; i++) {
                    if (textareas[i].offsetParent !== null) {
                        textareas[i].value = '';
                        textareas[i].dispatchEvent(new Event('input', {bubbles: true}));
                        textareas[i].dispatchEvent(new Event('change', {bubbles: true}));
                    }
                }

                // 清空iframe中的内容
                var iframes = document.querySelectorAll('iframe');
                for (var i = 0; i < iframes.length; i++) {
                    try {
                        var iframeDoc = iframes[i].contentDocument || iframes[i].contentWindow.document;
                        if (iframeDoc && iframeDoc.body) {
                            iframeDoc.body.innerHTML = '';
                        }
                    } catch (e) {
                        // 跨域iframe无法访问，忽略
                    }
                }

                return true;
            """

            # 执行JavaScript清空
            self.driver.execute_script(clear_script)

            # 额外的安全清空：针对特定字段
            try:
                # 清空收件人字段
                to_field = self.find_element_by_selectors(self.selectors['to_inputs'], timeout=0.3)
                if to_field:
                    self.driver.execute_script("arguments[0].value = ''; arguments[0].dispatchEvent(new Event('change', {bubbles: true}));", to_field)

                # 清空主题字段
                subject_field = self.find_element_by_selectors(self.selectors['subject_inputs'], timeout=0.3)
                if subject_field:
                    self.driver.execute_script("arguments[0].value = ''; arguments[0].dispatchEvent(new Event('change', {bubbles: true}));", subject_field)
            except:
                pass  # 如果特定字段清空失败，JavaScript通用清空应该已经处理了

            logger.info("⚡ 输入字段快速清空完成")

        except Exception as e:
            logger.debug(f"快速清空字段失败: {e}")
            # 如果JavaScript清空失败，尝试传统方法
            try:
                to_field = self.find_element_by_selectors(self.selectors['to_inputs'], timeout=0.5)
                if to_field:
                    to_field.clear()
                subject_field = self.find_element_by_selectors(self.selectors['subject_inputs'], timeout=0.5)
                if subject_field:
                    subject_field.clear()
            except:
                pass
