#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证第一步策略与测试成功代码的完全一致性
确保每个细节都与sina_ultra_fast_sender_final.py中的JavaScript代码完全一致
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_step2_recipient_consistency():
    """检查第2步收件人填写的完全一致性"""
    print("🔍 检查第2步收件人填写的完全一致性")
    print("-" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        step2_source = inspect.getsource(UnifiedEmailSender._step2_fill_recipient)
        
        # 检查与测试成功代码的完全一致性
        consistency_checks = [
            ("// 步骤1: 填写收件人 - 基于成功经验的精确选择器" in step2_source, "步骤注释一致"),
            ("document.querySelector('input[type=\"text\"]') ||  // 测试成功的选择器！" in step2_source, "主选择器注释一致"),
            ("document.querySelectorAll('input[type=\"text\"]')[0];  // 第一个文本输入框" in step2_source, "备用选择器注释一致"),
            ("Event('blur', {bubbles: true})" in step2_source, "blur事件一致"),
            ("console.log('✅ 收件人已填写:', arguments[1]);" in step2_source, "成功日志一致")
        ]
        
        passed = 0
        for check, desc in consistency_checks:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            if check:
                passed += 1
        
        print(f"  📊 第2步一致性: {passed}/{len(consistency_checks)}")
        return passed == len(consistency_checks)
        
    except Exception as e:
        print(f"  ❌ 第2步检查失败: {e}")
        return False

def check_step3_subject_consistency():
    """检查第3步主题填写的完全一致性"""
    print("\n🔍 检查第3步主题填写的完全一致性")
    print("-" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        step3_source = inspect.getsource(UnifiedEmailSender._step3_fill_subject)
        
        # 检查与测试成功代码的完全一致性
        consistency_checks = [
            ("// 步骤2: 填写主题 - 基于调试结果的精确选择器" in step3_source, "步骤注释一致"),
            ("// 🎯 最精确的选择器：同时匹配name和class" in step3_source, "精确选择器注释一致"),
            ("// 🔍 基于可见性过滤的选择器" in step3_source, "可见性过滤注释一致"),
            ("subjElements[i].className.includes('inp_base')" in step3_source, "class检查逻辑一致"),
            ("Event('blur'" not in step3_source, "正确无blur事件（与测试一致）"),
            ("console.log('✅ 主题已填写:', arguments[1]);" in step3_source, "成功日志一致")
        ]
        
        passed = 0
        for check, desc in consistency_checks:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            if check:
                passed += 1
        
        print(f"  📊 第3步一致性: {passed}/{len(consistency_checks)}")
        return passed == len(consistency_checks)
        
    except Exception as e:
        print(f"  ❌ 第3步检查失败: {e}")
        return False

def check_step4_content_consistency():
    """检查第4步内容填写的完全一致性"""
    print("\n🔍 检查第4步内容填写的完全一致性")
    print("-" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        step4_source = inspect.getsource(UnifiedEmailSender._step4_fill_content)
        
        # 检查与测试成功代码的完全一致性
        consistency_checks = [
            ("步骤3: 填写邮件内容 - 并行尝试多种方式" in step4_source, "步骤注释一致"),
            ("// 方式1: iframe编辑器 - 基于真实选择器优化" in step4_source, "iframe方式注释一致"),
            ("document.querySelector('iframe[class=\"iframe\"]') ||  // 基于调试发现的真实选择器！" in step4_source, "iframe选择器注释一致"),
            ("document.querySelector('iframe');  // 任何iframe" in step4_source, "任何iframe注释一致"),
            ("方式2: 富文本编辑器" in step4_source, "富文本方式注释一致"),
            ("方式3: textarea" in step4_source, "textarea方式注释一致"),
            ("console.log('✅ iframe内容已填写');" in step4_source, "iframe成功日志一致"),
            ("console.log('✅ 富文本编辑器内容已填写');" in step4_source, "富文本成功日志一致"),
            ("console.log('✅ textarea内容已填写');" in step4_source, "textarea成功日志一致"),
            ("❌ 内容填写失败，停止发送操作" in step4_source, "失败处理一致")
        ]
        
        passed = 0
        for check, desc in consistency_checks:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            if check:
                passed += 1
        
        print(f"  📊 第4步一致性: {passed}/{len(consistency_checks)}")
        return passed == len(consistency_checks)
        
    except Exception as e:
        print(f"  ❌ 第4步检查失败: {e}")
        return False

def check_step5_send_consistency():
    """检查第5步发送按钮的完全一致性"""
    print("\n🔍 检查第5步发送按钮的完全一致性")
    print("-" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        step5_source = inspect.getsource(UnifiedEmailSender._step5_click_send)
        
        # 检查与测试成功代码的完全一致性
        consistency_checks = [
            ("步骤4: 点击发送按钮 - 基于成功经验的精确选择器" in step5_source, "步骤注释一致"),
            ("document.querySelector('input[type=\"submit\"]') ||  // 任何提交按钮" in step5_source, "任何提交按钮注释一致"),
            ("console.log('✅ 找到发送按钮，立即发送...');" in step5_source, "找到按钮日志一致"),
            ("console.log('❌ 未找到发送按钮');" in step5_source, "未找到按钮日志一致"),
            ("sendButton.focus();" in step5_source, "focus操作一致"),
            ("sendButton.click();" in step5_source, "click操作一致"),
            ("您的邮件已发送" in step5_source, "成功检查文本一致"),
            ("time.sleep(0.3)" in step5_source, "等待时间一致")
        ]
        
        passed = 0
        for check, desc in consistency_checks:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            if check:
                passed += 1
        
        print(f"  📊 第5步一致性: {passed}/{len(consistency_checks)}")
        return passed == len(consistency_checks)
        
    except Exception as e:
        print(f"  ❌ 第5步检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 验证第一步策略与测试成功代码的完全一致性")
    print("目标：确保每个细节都与sina_ultra_fast_sender_final.py中的JavaScript代码完全一致")
    print("=" * 80)
    
    # 检查所有步骤的一致性
    step_results = []
    step_results.append(check_step2_recipient_consistency())
    step_results.append(check_step3_subject_consistency())
    step_results.append(check_step4_content_consistency())
    step_results.append(check_step5_send_consistency())
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 完全一致性验证结果")
    print("=" * 80)
    
    passed_steps = sum(step_results)
    total_steps = len(step_results)
    consistency_rate = (passed_steps / total_steps) * 100 if total_steps > 0 else 0
    
    print(f"✅ 一致的步骤: {passed_steps}/{total_steps}")
    print(f"❌ 不一致的步骤: {total_steps - passed_steps}/{total_steps}")
    print(f"📈 一致性: {consistency_rate:.1f}%")
    
    # 详细结果
    step_names = ["第2步：填写收件人", "第3步：填写主题", "第4步：填写内容", "第5步：点击发送"]
    print(f"\n📋 详细结果:")
    for i, (result, name) in enumerate(zip(step_results, step_names)):
        status = "✅" if result else "❌"
        print(f"  {status} {name}")
    
    if consistency_rate == 100:
        print("\n🎉 完全一致性验证通过！第一步策略与测试成功代码100%一致！")
        print("✅ 所有JavaScript代码细节都已完全复刻")
        print("✅ 所有注释和日志都与测试成功代码一致")
        print("✅ 所有选择器逻辑都与测试成功代码一致")
        print("✅ 所有事件触发都与测试成功代码一致")
        print("✅ 所有错误处理都与测试成功代码一致")
        
        print("\n🎯 一致性确认:")
        print("  📧 第2步：收件人填写 - 包含blur事件，与测试代码一致")
        print("  📝 第3步：主题填写 - 无blur事件，与测试代码一致")
        print("  📄 第4步：内容填写 - 3种方式并行，与测试代码一致")
        print("  🚀 第5步：发送按钮 - 8种选择器，与测试代码一致")
        
        print("\n🚀 现在第一步策略将产生与测试成功完全相同的执行效果！")
        
    elif consistency_rate >= 80:
        print("\n✅ 一致性验证基本通过，但有部分细节需要调整")
    else:
        print("\n❌ 一致性验证未通过，需要进一步修复")
    
    return 0 if consistency_rate == 100 else 1

if __name__ == "__main__":
    sys.exit(main())
