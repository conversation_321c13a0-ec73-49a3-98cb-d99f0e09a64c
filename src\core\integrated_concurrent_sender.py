#!/usr/bin/env python3
"""
集成并发发送器 - 整合多浏览器管理器和智能账号监控器
实现真正的多账号并发发送，支持自动账号切换和发送监控
"""

import time
import threading
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from src.utils.logger import setup_logger
from src.models.account import Account
from src.core.concurrent_multi_browser_manager import ConcurrentMultiBrowserManager, SendingConfig
from src.core.smart_account_monitor import SmartAccountMonitor, MonitorConfig

logger = setup_logger("INFO")

@dataclass
class IntegratedSendingConfig:
    """集成发送配置"""
    # 浏览器配置
    max_browsers: int = 3
    emails_per_account: int = 5
    send_interval: float = 2.0
    browser_window_width: int = 800
    browser_window_height: int = 600
    minimize_browsers: bool = True
    
    # 监控配置
    monitor_interval: float = 1.0
    auto_switch_enabled: bool = True
    switch_cooldown: float = 2.0
    max_retries: int = 3
    
    # 发送配置
    max_concurrent_tasks: int = 10
    task_timeout: float = 30.0
    retry_failed_tasks: bool = True

class IntegratedConcurrentSender:
    """集成并发发送器"""
    
    def __init__(self, config: IntegratedSendingConfig):
        self.config = config
        
        # 创建发送配置
        sending_config = SendingConfig(
            max_browsers=config.max_browsers,
            emails_per_account=config.emails_per_account,
            send_interval=config.send_interval,
            browser_window_width=config.browser_window_width,
            browser_window_height=config.browser_window_height,
            minimize_browsers=config.minimize_browsers
        )
        
        # 创建监控配置
        monitor_config = MonitorConfig(
            emails_per_account=config.emails_per_account,
            monitor_interval=config.monitor_interval,
            auto_switch_enabled=config.auto_switch_enabled,
            switch_cooldown=config.switch_cooldown,
            max_retries=config.max_retries
        )
        
        # 初始化核心组件
        self.browser_manager = ConcurrentMultiBrowserManager(sending_config)
        self.account_monitor = SmartAccountMonitor(monitor_config)
        
        # 状态管理
        self.is_initialized = False
        self.is_running = False
        self.accounts: List[Account] = []
        
        # 统计信息
        self.start_time = 0
        self.total_sent = 0
        self.total_failed = 0
        self.total_account_switches = 0
        
        # 设置回调函数
        self._setup_callbacks()
        
        logger.info("🚀 集成并发发送器初始化完成")
    
    def _setup_callbacks(self):
        """设置回调函数"""
        # 账号切换回调
        self.account_monitor.add_switch_callback(self._on_account_switch_requested)
        
        # 警报回调
        self.account_monitor.add_alert_callback(self._on_monitor_alert)
    
    def initialize(self, accounts: List[Account]) -> bool:
        """初始化集成发送系统"""
        try:
            logger.info(f"🔧 初始化集成发送系统，账号数量: {len(accounts)}")
            
            if not accounts:
                logger.error("❌ 没有提供账号信息")
                return False
            
            self.accounts = accounts.copy()
            
            # 1. 初始化浏览器管理器
            logger.info("🔧 初始化浏览器管理器...")
            if not self.browser_manager.initialize(accounts):
                logger.error("❌ 浏览器管理器初始化失败")
                return False
            
            # 2. 初始化账号监控器
            logger.info("🔧 初始化账号监控器...")
            browser_ids = list(self.browser_manager.browser_work_units.keys())
            self.account_monitor.initialize_accounts(accounts, browser_ids)
            
            self.is_initialized = True
            logger.info("✅ 集成发送系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 集成发送系统初始化失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def start_sending(self) -> bool:
        """启动并发发送"""
        try:
            if not self.is_initialized:
                logger.error("❌ 系统未初始化，无法启动发送")
                return False
            
            if self.is_running:
                logger.warning("⚠️ 发送系统已在运行")
                return False
            
            logger.info("🚀 启动集成并发发送系统...")
            
            # 1. 启动浏览器管理器
            if not self.browser_manager.start_concurrent_sending():
                logger.error("❌ 浏览器管理器启动失败")
                return False
            
            # 2. 启动账号监控器
            self.account_monitor.start_monitoring()
            
            self.is_running = True
            self.start_time = time.time()
            
            logger.info("✅ 集成并发发送系统启动完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动集成并发发送失败: {e}")
            return False
    
    def stop_sending(self):
        """停止并发发送"""
        logger.info("🛑 停止集成并发发送系统...")
        
        self.is_running = False
        
        # 停止账号监控器
        self.account_monitor.stop_monitoring()
        
        # 停止浏览器管理器
        self.browser_manager.stop_concurrent_sending()
        
        logger.info("✅ 集成并发发送系统已停止")
    
    def add_email_task(self, to_email: str, subject: str, content: str, priority: int = 1) -> str:
        """添加邮件任务"""
        if not self.is_running:
            logger.error("❌ 发送系统未运行，无法添加任务")
            return ""
        
        task_id = self.browser_manager.add_email_task(to_email, subject, content, priority)
        logger.info(f"📝 添加邮件任务: {task_id} -> {to_email}")
        return task_id
    
    def add_batch_tasks(self, email_list: List[Tuple[str, str, str]]) -> List[str]:
        """批量添加邮件任务"""
        if not self.is_running:
            logger.error("❌ 发送系统未运行，无法添加任务")
            return []
        
        task_ids = self.browser_manager.add_batch_tasks(email_list)
        logger.info(f"📝 批量添加 {len(email_list)} 个邮件任务")
        return task_ids
    
    def _on_account_switch_requested(self, browser_id: str, new_account: str) -> bool:
        """处理账号切换请求"""
        try:
            logger.info(f"🔄 处理账号切换请求: 浏览器 {browser_id} -> 账号 {new_account}")
            
            # 通过浏览器管理器执行切换
            success = self.browser_manager.force_account_switch(browser_id)
            
            if success:
                self.total_account_switches += 1
                logger.info(f"✅ 账号切换成功: 浏览器 {browser_id}")
            else:
                logger.error(f"❌ 账号切换失败: 浏览器 {browser_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 处理账号切换请求异常: {e}")
            return False
    
    def _on_monitor_alert(self, alert_type: str, browser_id: str, **kwargs):
        """处理监控警报"""
        logger.warning(f"⚠️ 监控警报: {alert_type} - 浏览器 {browser_id}")
        
        if alert_type == "no_available_accounts":
            logger.warning(f"⚠️ 浏览器 {browser_id} 没有更多可用账号")
            # 可以在这里实现更多的处理逻辑，比如通知用户
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        runtime = time.time() - self.start_time if self.start_time > 0 else 0
        
        # 获取浏览器管理器统计
        browser_stats = self.browser_manager.get_concurrent_stats()
        
        # 获取账号监控统计
        monitor_stats = self.account_monitor.get_monitor_stats()
        
        # 获取浏览器状态
        browser_status = self.browser_manager.get_browser_status()
        
        return {
            'system_status': {
                'is_initialized': self.is_initialized,
                'is_running': self.is_running,
                'runtime_seconds': runtime,
                'start_time': self.start_time
            },
            'sending_stats': {
                'total_browsers': browser_stats.get('total_browsers', 0),
                'active_browsers': browser_stats.get('active_browsers', 0),
                'total_accounts': len(self.accounts),
                'total_tasks': browser_stats.get('total_tasks', 0),
                'completed_tasks': browser_stats.get('completed_tasks', 0),
                'failed_tasks': browser_stats.get('failed_tasks', 0),
                'emails_per_minute': browser_stats.get('emails_per_minute', 0),
                'global_queue_size': browser_stats.get('global_queue_size', 0)
            },
            'account_monitoring': {
                'total_switches': monitor_stats.get('total_switches', 0),
                'successful_switches': monitor_stats.get('successful_switches', 0),
                'failed_switches': monitor_stats.get('failed_switches', 0),
                'switch_success_rate': monitor_stats.get('switch_success_rate', 0)
            },
            'browser_details': browser_status,
            'account_details': monitor_stats.get('account_status', {}),
            'browser_bindings': monitor_stats.get('browser_bindings', {})
        }
    
    def get_browser_status(self) -> Dict[str, Dict]:
        """获取浏览器状态"""
        return self.browser_manager.get_browser_status()
    
    def force_account_switch(self, browser_id: str) -> bool:
        """强制切换指定浏览器的账号"""
        logger.info(f"🔄 强制切换浏览器 {browser_id} 的账号")
        
        # 通过账号监控器触发切换
        return self.account_monitor.force_switch_account(browser_id)
    
    def get_current_account(self, browser_id: str) -> Optional[str]:
        """获取指定浏览器的当前账号"""
        return self.account_monitor.get_current_account(browser_id)
    
    def get_account_sent_count(self, account_email: str) -> int:
        """获取指定账号的发送数量"""
        return self.account_monitor.get_account_sent_count(account_email)
    
    def record_email_sent(self, browser_id: str, account_email: str, send_time: float = 0):
        """记录邮件发送（用于外部调用）"""
        self.account_monitor.record_email_sent(browser_id, account_email, send_time)
        self.total_sent += 1
    
    def record_email_failed(self, browser_id: str, account_email: str):
        """记录邮件发送失败"""
        self.total_failed += 1
        logger.warning(f"⚠️ 邮件发送失败: 浏览器 {browser_id}, 账号 {account_email}")
    
    def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理集成并发发送器资源...")
        
        # 停止发送
        if self.is_running:
            self.stop_sending()
        
        # 清理浏览器管理器
        self.browser_manager.cleanup()
        
        logger.info("✅ 资源清理完成")
    
    def is_system_healthy(self) -> bool:
        """检查系统健康状态"""
        if not self.is_initialized or not self.is_running:
            return False
        
        # 检查是否有活跃的浏览器
        browser_status = self.get_browser_status()
        active_browsers = sum(1 for status in browser_status.values() if status.get('is_working', False))
        
        return active_browsers > 0
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        stats = self.get_comprehensive_stats()
        runtime = stats['system_status']['runtime_seconds']
        
        if runtime <= 0:
            return {
                'emails_per_minute': 0,
                'emails_per_hour': 0,
                'average_switch_time': 0,
                'system_efficiency': 0
            }
        
        completed_tasks = stats['sending_stats']['completed_tasks']
        total_switches = stats['account_monitoring']['total_switches']
        
        return {
            'emails_per_minute': (completed_tasks / runtime) * 60 if runtime > 0 else 0,
            'emails_per_hour': (completed_tasks / runtime) * 3600 if runtime > 0 else 0,
            'average_switch_time': runtime / max(total_switches, 1),
            'system_efficiency': (completed_tasks / max(stats['sending_stats']['total_tasks'], 1)) * 100
        }
