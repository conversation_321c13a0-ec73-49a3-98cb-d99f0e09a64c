#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试第2步收件人填写问题
分析为什么第2步会失败
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def analyze_step2_issue():
    """分析第2步收件人填写问题"""
    print("🔍 分析第2步收件人填写问题")
    print("=" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        # 获取第2步的源码
        step2_source = inspect.getsource(UnifiedEmailSender._step2_fill_recipient)
        
        print("📋 第2步当前实现:")
        print("-" * 40)
        
        # 检查选择器
        selectors_check = [
            ("input[type=\"text\"]" in step2_source, "主要选择器: input[type='text']"),
            ("input[name=\"to\"]" in step2_source, "备用选择器: input[name='to']"),
            ("input[name=\"mailto\"]" in step2_source, "备用选择器: input[name='mailto']"),
            ("input[name*=\"to\"]" in step2_source, "备用选择器: input[name*='to']"),
            ("input[placeholder*=\"收件人\"]" in step2_source, "备用选择器: placeholder收件人"),
            ("textarea[name*=\"to\"]" in step2_source, "备用选择器: textarea[name*='to']"),
            ("querySelectorAll('input[type=\"text\"]')[0]" in step2_source, "备用选择器: 第一个文本框")
        ]
        
        for check, desc in selectors_check:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
        
        # 检查操作逻辑
        operations_check = [
            ("offsetParent !== null" in step2_source, "可见性检查"),
            ("focus()" in step2_source, "focus操作"),
            ("value = arguments[1]" in step2_source, "值设置"),
            ("Event('input'" in step2_source, "input事件"),
            ("Event('change'" in step2_source, "change事件"),
            ("Event('blur'" in step2_source, "blur事件"),
            ("console.log" in step2_source, "调试日志"),
            ("logger.info" in step2_source, "Python日志"),
            ("logger.warning" in step2_source, "警告日志")
        ]
        
        print("\n📋 操作逻辑检查:")
        print("-" * 40)
        for check, desc in operations_check:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
        
        # 对比原始成功逻辑
        print("\n📋 与原始成功逻辑对比:")
        print("-" * 40)
        
        try:
            with open("src/core/sina_ultra_fast_sender_final.py", "r", encoding="utf-8") as f:
                original_content = f.read()
            
            # 查找原始收件人填写逻辑
            start_marker = "// 步骤1: 填写收件人"
            end_marker = "// 步骤2: 填写主题"
            
            start_idx = original_content.find(start_marker)
            end_idx = original_content.find(end_marker)
            
            if start_idx != -1 and end_idx != -1:
                original_logic = original_content[start_idx:end_idx]
                
                print("原始成功逻辑片段:")
                print("```javascript")
                # 只显示关键部分
                lines = original_logic.split('\n')
                for line in lines[:20]:  # 显示前20行
                    if line.strip():
                        print(line)
                print("```")
                
                # 检查关键差异
                print("\n🔍 关键差异分析:")
                differences = [
                    ("toField.focus()" in original_logic and "focus()" in step2_source, "focus操作一致"),
                    ("toField.value =" in original_logic and "value = arguments[1]" in step2_source, "值设置一致"),
                    ("dispatchEvent(new Event('input'" in original_logic and "Event('input'" in step2_source, "input事件一致"),
                    ("dispatchEvent(new Event('change'" in original_logic and "Event('change'" in step2_source, "change事件一致"),
                    ("dispatchEvent(new Event('blur'" in original_logic and "Event('blur'" in step2_source, "blur事件一致"),
                    ("offsetParent !== null" in original_logic and "offsetParent !== null" in step2_source, "可见性检查一致")
                ]
                
                for check, desc in differences:
                    status = "✅" if check else "❌"
                    print(f"  {status} {desc}")
            
        except Exception as e:
            print(f"❌ 无法读取原始逻辑: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def suggest_fixes():
    """建议修复方案"""
    print("\n🔧 建议修复方案:")
    print("=" * 60)
    
    print("1. 🎯 增强选择器策略:")
    print("   - 确保所有备用选择器都已包含")
    print("   - 添加更多调试信息来定位具体问题")
    print("   - 检查页面加载状态")
    
    print("\n2. 🕐 时序问题:")
    print("   - 可能需要等待页面完全加载")
    print("   - 添加重试机制")
    print("   - 检查元素是否已渲染")
    
    print("\n3. 🔍 调试增强:")
    print("   - 添加页面元素检查")
    print("   - 输出找到的元素信息")
    print("   - 记录失败的具体原因")
    
    print("\n4. 📝 建议的调试代码:")
    print("```python")
    print("# 在第2步开始前添加:")
    print("all_inputs = driver.execute_script('''")
    print("    var inputs = document.querySelectorAll('input');")
    print("    var result = [];")
    print("    for(var i = 0; i < inputs.length; i++) {")
    print("        result.push({")
    print("            type: inputs[i].type,")
    print("            name: inputs[i].name,")
    print("            placeholder: inputs[i].placeholder,")
    print("            visible: inputs[i].offsetParent !== null")
    print("        });")
    print("    }")
    print("    return result;")
    print("''')")
    print("logger.info(f'页面所有输入框: {all_inputs}')")
    print("```")

def main():
    """主函数"""
    print("🎯 调试第2步收件人填写问题")
    print("目标：找出为什么第2步会失败")
    
    # 分析问题
    success = analyze_step2_issue()
    
    if success:
        # 建议修复方案
        suggest_fixes()
        
        print("\n📊 分析总结:")
        print("=" * 60)
        print("✅ 第2步实现已包含完整的选择器逻辑")
        print("✅ 操作逻辑与原始成功逻辑一致")
        print("✅ 已添加调试日志和错误处理")
        print("⚠️ 可能的问题：时序问题或页面状态问题")
        
        print("\n🎯 下一步行动:")
        print("1. 运行实际测试，查看详细的调试日志")
        print("2. 检查页面元素的实际状态")
        print("3. 如果需要，添加页面元素检查代码")
        print("4. 考虑添加重试机制")
        
    else:
        print("\n❌ 分析失败，需要进一步检查代码")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
