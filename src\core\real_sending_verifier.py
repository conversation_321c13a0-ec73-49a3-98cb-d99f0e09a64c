#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实邮件发送验证器
基于多维度验证机制确保邮件真正发送成功
"""

import time
import json
from typing import Dict, List, Tuple, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from src.utils.logger import get_logger

logger = get_logger("RealSendingVerifier")

class RealSendingVerifier:
    """真实邮件发送验证器"""
    
    def __init__(self, driver: webdriver.Chrome):
        """
        初始化验证器
        
        Args:
            driver: Chrome浏览器驱动
        """
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        
        # 验证方法权重配置
        self.verification_weights = {
            'success_message': 0.40,    # 成功消息验证 - 权重40%
            'url_change': 0.20,         # URL变化验证 - 权重20%
            'sent_folder': 0.20,        # 发件箱验证 - 权重20%
            'network_request': 0.15,    # 网络请求验证 - 权重15%
            'timestamp': 0.05           # 时间戳验证 - 权重5%
        }
        
        # 成功指示器配置
        self.success_indicators = {
            'text': [
                '您的邮件已发送',      # 新浪邮箱真实成功提示
                '邮件已发送',
                '发送成功',
                '已发送',
                '发送完成',
                'sent successfully',
                'message sent',
                '已成功发送',
                '发送邮件成功',
                '邮件发送成功',
                '成功发送'
            ],
            'url_keywords': [
                'sent',
                'success', 
                'complete',
                'mailinfo',
                'finish'
            ],
            'css_selectors': [
                '.success-message',
                '.alert-success',
                '[class*="success"]',
                '.send-success',
                '#success-tip'
            ]
        }
        
        # 失败指示器配置
        self.failure_indicators = {
            'text': [
                '发送失败',
                'send failed',
                'error',
                '错误',
                '发送错误',
                'sending error',
                '网络错误',
                'network error'
            ],
            'css_selectors': [
                '.error-message',
                '.alert-error',
                '[class*="error"]',
                '.send-error',
                '#error-tip'
            ]
        }
    
    def verify_real_sending(self, timeout: int = 30) -> Tuple[bool, float, Dict]:
        """
        多维度验证邮件是否真实发送成功
        
        Args:
            timeout: 验证超时时间（秒）
            
        Returns:
            Tuple[bool, float, Dict]: (是否成功, 置信度, 详细结果)
        """
        try:
            logger.info("🔍 开始多维度真实发送验证...")
            start_time = time.time()
            
            verification_results = {}
            total_score = 0.0
            
            # 1. 成功消息验证 (权重40%)
            success_msg_score = self._verify_success_message(timeout)
            verification_results['success_message'] = success_msg_score
            total_score += success_msg_score * self.verification_weights['success_message']
            logger.info(f"✅ 成功消息验证得分: {success_msg_score:.2f}")
            
            # 2. URL变化验证 (权重20%)
            url_change_score = self._verify_url_change(timeout)
            verification_results['url_change'] = url_change_score
            total_score += url_change_score * self.verification_weights['url_change']
            logger.info(f"🔗 URL变化验证得分: {url_change_score:.2f}")
            
            # 3. 发件箱验证 (权重20%)
            sent_folder_score = self._verify_sent_folder(timeout)
            verification_results['sent_folder'] = sent_folder_score
            total_score += sent_folder_score * self.verification_weights['sent_folder']
            logger.info(f"📤 发件箱验证得分: {sent_folder_score:.2f}")
            
            # 4. 网络请求验证 (权重15%)
            network_score = self._verify_network_request(timeout)
            verification_results['network_request'] = network_score
            total_score += network_score * self.verification_weights['network_request']
            logger.info(f"🌐 网络请求验证得分: {network_score:.2f}")
            
            # 5. 时间戳验证 (权重5%)
            timestamp_score = self._verify_timestamp(timeout)
            verification_results['timestamp'] = timestamp_score
            total_score += timestamp_score * self.verification_weights['timestamp']
            logger.info(f"⏰ 时间戳验证得分: {timestamp_score:.2f}")
            
            # 计算最终置信度
            confidence = total_score
            is_success = confidence > 0.7  # 置信度阈值70%
            
            elapsed_time = time.time() - start_time
            
            # 详细结果
            detailed_result = {
                'verification_results': verification_results,
                'total_score': total_score,
                'confidence': confidence,
                'is_success': is_success,
                'verification_time': elapsed_time,
                'threshold': 0.7
            }
            
            if is_success:
                logger.info(f"✅ 真实发送验证成功！置信度: {confidence:.2f} (耗时: {elapsed_time:.2f}秒)")
            else:
                logger.warning(f"❌ 真实发送验证失败！置信度: {confidence:.2f} (耗时: {elapsed_time:.2f}秒)")
            
            return is_success, confidence, detailed_result
            
        except Exception as e:
            logger.error(f"❌ 真实发送验证异常: {e}")
            return False, 0.0, {'error': str(e)}
    
    def _verify_success_message(self, timeout: int) -> float:
        """验证成功消息 - 权重40%"""
        try:
            max_wait = min(timeout, 10)  # 最多等待10秒
            check_interval = 0.5
            waited = 0
            
            while waited < max_wait:
                # 检查页面文本
                page_source = self.driver.page_source.lower()
                
                # 检查成功指示器
                for indicator in self.success_indicators['text']:
                    if indicator.lower() in page_source:
                        logger.info(f"✅ 找到成功消息: {indicator}")
                        return 1.0
                
                # 检查CSS选择器
                for selector in self.success_indicators['css_selectors']:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements and any(elem.is_displayed() for elem in elements):
                            logger.info(f"✅ 找到成功元素: {selector}")
                            return 1.0
                    except:
                        continue
                
                # 检查失败指示器
                for indicator in self.failure_indicators['text']:
                    if indicator.lower() in page_source:
                        logger.warning(f"❌ 找到失败消息: {indicator}")
                        return 0.0
                
                time.sleep(check_interval)
                waited += check_interval
            
            logger.info("⚠️ 未找到明确的成功或失败消息")
            return 0.3  # 部分分数
            
        except Exception as e:
            logger.error(f"❌ 成功消息验证失败: {e}")
            return 0.0
    
    def _verify_url_change(self, timeout: int) -> float:
        """验证URL变化 - 权重20%"""
        try:
            current_url = self.driver.current_url.lower()
            
            # 检查URL中的成功关键词
            for keyword in self.success_indicators['url_keywords']:
                if keyword in current_url:
                    logger.info(f"✅ URL包含成功关键词: {keyword}")
                    return 1.0
            
            # 检查是否离开了写信页面
            if 'compose' not in current_url and 'write' not in current_url:
                logger.info("✅ 已离开写信页面，可能发送成功")
                return 0.8
            
            logger.info("⚠️ URL未发生预期变化")
            return 0.2
            
        except Exception as e:
            logger.error(f"❌ URL变化验证失败: {e}")
            return 0.0
    
    def _verify_sent_folder(self, timeout: int) -> float:
        """验证发件箱 - 权重20%"""
        try:
            # 这里可以实现检查发件箱的逻辑
            # 由于需要导航到发件箱页面，可能影响性能
            # 暂时返回中性分数
            logger.info("📤 发件箱验证暂未实现")
            return 0.5
            
        except Exception as e:
            logger.error(f"❌ 发件箱验证失败: {e}")
            return 0.0
    
    def _verify_network_request(self, timeout: int) -> float:
        """验证网络请求 - 权重15%"""
        try:
            # 检查浏览器网络日志
            # 这需要启用网络日志记录
            logs = self.driver.get_log('performance')
            
            # 查找发送相关的网络请求
            send_requests = []
            for log in logs:
                message = json.loads(log['message'])
                if message.get('message', {}).get('method') == 'Network.responseReceived':
                    url = message.get('message', {}).get('params', {}).get('response', {}).get('url', '')
                    if 'send' in url.lower() or 'mail' in url.lower():
                        send_requests.append(url)
            
            if send_requests:
                logger.info(f"✅ 检测到发送相关网络请求: {len(send_requests)}个")
                return 1.0
            else:
                logger.info("⚠️ 未检测到发送相关网络请求")
                return 0.3
                
        except Exception as e:
            logger.debug(f"网络请求验证失败: {e}")
            return 0.5  # 网络日志可能不可用，给中性分数
    
    def _verify_timestamp(self, timeout: int) -> float:
        """验证时间戳 - 权重5%"""
        try:
            # 检查页面中的时间戳信息
            current_time = time.time()
            
            # 查找页面中的时间相关元素
            time_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '2025') or contains(text(), ':')]")
            
            if time_elements:
                logger.info("✅ 找到时间戳相关信息")
                return 1.0
            else:
                logger.info("⚠️ 未找到时间戳信息")
                return 0.5
                
        except Exception as e:
            logger.error(f"❌ 时间戳验证失败: {e}")
            return 0.0
    
    def get_verification_summary(self, detailed_result: Dict) -> str:
        """获取验证结果摘要"""
        if not detailed_result or 'verification_results' not in detailed_result:
            return "验证结果无效"
        
        results = detailed_result['verification_results']
        confidence = detailed_result.get('confidence', 0)
        
        summary_parts = []
        summary_parts.append(f"置信度: {confidence:.1%}")
        summary_parts.append(f"成功消息: {results.get('success_message', 0):.1f}")
        summary_parts.append(f"URL变化: {results.get('url_change', 0):.1f}")
        summary_parts.append(f"发件箱: {results.get('sent_folder', 0):.1f}")
        summary_parts.append(f"网络请求: {results.get('network_request', 0):.1f}")
        summary_parts.append(f"时间戳: {results.get('timestamp', 0):.1f}")
        
        return " | ".join(summary_parts)
