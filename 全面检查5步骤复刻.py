#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面检查5步骤复刻
检查这5步骤是否完全复刻至第一步策略
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_step1_implementation():
    """检查第1步：点击写信按钮"""
    print("🔍 检查第1步：点击写信按钮")
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        source = inspect.getsource(UnifiedEmailSender._step1_click_compose)
        
        checks = [
            ("第1步：点击写信按钮 - 复刻成功逻辑" in source, "方法描述正确"),
            ("self.ultra_fast_sender.prepare_compose_page()" in source, "调用正确的方法"),
            ("return" in source, "有返回值"),
            ("except Exception" in source, "有异常处理")
        ]
        
        results = []
        for check, desc in checks:
            if check:
                print(f"  ✅ {desc}")
                results.append(True)
            else:
                print(f"  ❌ {desc}")
                results.append(False)
        
        return results
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return [False]

def check_step2_implementation():
    """检查第2步：填写收件人"""
    print("\n🔍 检查第2步：填写收件人")
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        source = inspect.getsource(UnifiedEmailSender._step2_fill_recipient)
        
        checks = [
            ("第2步：填写收件人 - 复刻成功逻辑" in source, "方法描述正确"),
            ("input[type=\"text\"]" in source, "使用正确的选择器"),
            ("input[name=\"to\"]" in source, "包含备用选择器"),
            ("input[placeholder*=\"收件人\"]" in source, "包含placeholder选择器"),
            ("querySelectorAll('input[type=\"text\"]')[0]" in source, "包含第一个文本框选择器"),
            ("focus()" in source, "包含focus操作"),
            ("value = arguments[1]" in source, "包含值设置"),
            ("dispatchEvent(new Event('input'" in source, "包含input事件"),
            ("dispatchEvent(new Event('change'" in source, "包含change事件")
        ]
        
        results = []
        for check, desc in checks:
            if check:
                print(f"  ✅ {desc}")
                results.append(True)
            else:
                print(f"  ❌ {desc}")
                results.append(False)
        
        return results
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return [False]

def check_step3_implementation():
    """检查第3步：填写主题"""
    print("\n🔍 检查第3步：填写主题")
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        source = inspect.getsource(UnifiedEmailSender._step3_fill_subject)
        
        checks = [
            ("第3步：填写主题 - 复刻成功逻辑" in source, "方法描述正确"),
            ("input[name=\"subj\"][class=\"input inp_base\"]" in source, "使用精确的主题选择器"),
            ("input[name=\"subj\"]" in source, "包含备用选择器"),
            ("input[placeholder*=\"主题\"]" in source, "包含placeholder选择器"),
            ("focus()" in source, "包含focus操作"),
            ("value = arguments[1]" in source, "包含值设置"),
            ("dispatchEvent(new Event('input'" in source, "包含input事件"),
            ("dispatchEvent(new Event('change'" in source, "包含change事件")
        ]
        
        results = []
        for check, desc in checks:
            if check:
                print(f"  ✅ {desc}")
                results.append(True)
            else:
                print(f"  ❌ {desc}")
                results.append(False)
        
        return results
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return [False]

def check_step4_implementation():
    """检查第4步：填写内容"""
    print("\n🔍 检查第4步：填写内容")
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        source = inspect.getsource(UnifiedEmailSender._step4_fill_content)
        
        checks = [
            ("第4步：填写内容 - 复刻成功逻辑" in source, "方法描述正确"),
            ("iframe[class=\"iframe\"]" in source, "使用iframe选择器"),
            ("document.querySelector('iframe')" in source, "包含通用iframe选择器"),
            ("switch_to.frame(iframe)" in source, "包含iframe切换"),
            ("document.body" in source, "包含body操作"),
            ("innerHTML = arguments[1]" in source, "包含innerHTML设置"),
            ("switch_to.default_content()" in source, "包含iframe退出"),
            ("document.querySelector('textarea')" in source, "包含textarea备用方案"),
            ("dispatchEvent(new Event('input'" in source, "包含input事件")
        ]
        
        results = []
        for check, desc in checks:
            if check:
                print(f"  ✅ {desc}")
                results.append(True)
            else:
                print(f"  ❌ {desc}")
                results.append(False)
        
        return results
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return [False]

def check_step5_implementation():
    """检查第5步：点击发送"""
    print("\n🔍 检查第5步：点击发送")
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        source = inspect.getsource(UnifiedEmailSender._step5_click_send)
        
        checks = [
            ("第5步：点击发送 - 复刻成功逻辑" in source, "方法描述正确"),
            ("input[type=\"submit\"][value=\"发送\"]" in source, "使用正确的发送按钮选择器"),
            ("button[type=\"submit\"]" in source, "包含submit按钮选择器"),
            ("input[value=\"发送\"]" in source, "包含发送值选择器"),
            ("arguments[0].click()" in source, "包含点击操作"),
            ("time.sleep(0.3)" in source, "包含等待检查"),
            ("您的邮件已发送" in source, "包含成功检查文本"),
            ("发送成功" in source, "包含成功检查文本"),
            ("邮件已发送" in source, "包含成功检查文本"),
            ("document.body.innerText.includes" in source, "包含文本检查逻辑")
        ]
        
        results = []
        for check, desc in checks:
            if check:
                print(f"  ✅ {desc}")
                results.append(True)
            else:
                print(f"  ❌ {desc}")
                results.append(False)
        
        return results
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return [False]

def check_integration():
    """检查集成情况"""
    print("\n🔍 检查集成情况")
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        # 检查主方法
        main_source = inspect.getsource(UnifiedEmailSender._send_ultra_fast)
        exec_source = inspect.getsource(UnifiedEmailSender._execute_5_steps_logic)
        
        checks = [
            ("_execute_5_steps_logic" in main_source, "主方法调用5步执行逻辑"),
            ("_step1_click_compose" in exec_source, "执行逻辑调用第1步"),
            ("_step2_fill_recipient" in exec_source, "执行逻辑调用第2步"),
            ("_step3_fill_subject" in exec_source, "执行逻辑调用第3步"),
            ("_step4_fill_content" in exec_source, "执行逻辑调用第4步"),
            ("_step5_click_send" in exec_source, "执行逻辑调用第5步"),
            ("第1步：点击写信按钮" in exec_source, "包含第1步日志"),
            ("第2步：填写收件人" in exec_source, "包含第2步日志"),
            ("第3步：填写主题" in exec_source, "包含第3步日志"),
            ("第4步：填写内容" in exec_source, "包含第4步日志"),
            ("第5步：点击发送" in exec_source, "包含第5步日志")
        ]
        
        results = []
        for check, desc in checks:
            if check:
                print(f"  ✅ {desc}")
                results.append(True)
            else:
                print(f"  ❌ {desc}")
                results.append(False)
        
        return results
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return [False]

def main():
    """主函数"""
    print("🎯 全面检查5步骤复刻")
    print("检查目标：第一步策略（ULTRA_FAST）")
    print("检查内容：5步骤是否完全复刻")
    print("=" * 60)
    
    all_results = []
    
    # 检查各步骤实现
    all_results.extend(check_step1_implementation())
    all_results.extend(check_step2_implementation())
    all_results.extend(check_step3_implementation())
    all_results.extend(check_step4_implementation())
    all_results.extend(check_step5_implementation())
    all_results.extend(check_integration())
    
    # 结果汇总
    print("\n" + "=" * 60)
    print("📊 全面检查结果汇总:")
    print("=" * 60)
    
    passed = sum(all_results)
    total = len(all_results)
    success_rate = (passed / total) * 100 if total > 0 else 0
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    print(f"📈 成功率: {success_rate:.1f}%")
    
    if success_rate >= 95:
        print("\n🎉 全面检查通过！5步骤已完全复刻至第一步策略！")
        print("✅ 所有5个步骤都有独立的实现方法")
        print("✅ 每个步骤都复刻了成功案例的具体细节")
        print("✅ 集成逻辑完整，调用关系正确")
        print("✅ 日志和错误处理完善")
    elif success_rate >= 80:
        print("\n⚠️ 基本通过，但有部分细节需要完善")
    else:
        print("\n❌ 检查未通过，5步骤复刻不完整")
    
    print("\n🎯 复刻完整性评估:")
    print("1. 🖱️ 第1步：点击写信按钮 - 复刻prepare_compose_page()逻辑")
    print("2. 📧 第2步：填写收件人 - 复刻input[type='text']选择器和JavaScript填写")
    print("3. 📝 第3步：填写主题 - 复刻input[name='subj']选择器和JavaScript填写")
    print("4. 📄 第4步：填写内容 - 复刻iframe/textarea填写逻辑")
    print("5. 🚀 第5步：点击发送 - 复刻发送按钮点击和成功检查")
    
    return 0 if success_rate >= 95 else 1

if __name__ == "__main__":
    sys.exit(main())
