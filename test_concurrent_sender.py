#!/usr/bin/env python3
"""
并发发送器测试脚本
测试多账号并发发送功能
"""

import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger import setup_logger
from src.models.account import Account
from src.core.integrated_concurrent_sender import IntegratedConcurrentSender, IntegratedSendingConfig

logger = setup_logger("INFO")

def create_test_accounts():
    """创建测试账号"""
    test_accounts = [
        Account(email="<EMAIL>", password="password1"),
        Account(email="<EMAIL>", password="password2"),
        Account(email="<EMAIL>", password="password3"),
        Account(email="<EMAIL>", password="password4"),
        Account(email="<EMAIL>", password="password5"),
    ]
    return test_accounts

def test_concurrent_sender():
    """测试并发发送器"""
    logger.info("🚀 开始测试并发发送器...")
    
    try:
        # 1. 创建测试账号
        accounts = create_test_accounts()
        logger.info(f"📋 创建了 {len(accounts)} 个测试账号")
        
        # 2. 创建配置
        config = IntegratedSendingConfig(
            max_browsers=3,  # 3个浏览器
            emails_per_account=2,  # 每个账号发送2封邮件
            send_interval=1.0,  # 1秒间隔
            auto_switch_enabled=True,  # 启用自动切换
            monitor_interval=0.5  # 0.5秒监控间隔
        )
        logger.info(f"⚙️ 创建配置: {config.max_browsers}个浏览器, 每账号{config.emails_per_account}封邮件")
        
        # 3. 创建并发发送器
        sender = IntegratedConcurrentSender(config)
        
        # 4. 初始化系统
        logger.info("🔧 初始化并发发送系统...")
        if not sender.initialize(accounts):
            logger.error("❌ 系统初始化失败")
            return False
        
        logger.info("✅ 系统初始化成功")
        
        # 5. 启动发送
        logger.info("🚀 启动并发发送...")
        if not sender.start_sending():
            logger.error("❌ 启动发送失败")
            return False
        
        logger.info("✅ 并发发送启动成功")
        
        # 6. 添加测试任务
        test_emails = [
            ("<EMAIL>", "测试邮件1", "这是第一封测试邮件"),
            ("<EMAIL>", "测试邮件2", "这是第二封测试邮件"),
            ("<EMAIL>", "测试邮件3", "这是第三封测试邮件"),
            ("<EMAIL>", "测试邮件4", "这是第四封测试邮件"),
            ("<EMAIL>", "测试邮件5", "这是第五封测试邮件"),
            ("<EMAIL>", "测试邮件6", "这是第六封测试邮件"),
            ("<EMAIL>", "测试邮件7", "这是第七封测试邮件"),
            ("<EMAIL>", "测试邮件8", "这是第八封测试邮件"),
        ]
        
        logger.info(f"📝 添加 {len(test_emails)} 个测试任务...")
        task_ids = sender.add_batch_tasks(test_emails)
        logger.info(f"✅ 成功添加 {len(task_ids)} 个任务")
        
        # 7. 监控发送过程
        logger.info("👀 开始监控发送过程...")
        monitor_duration = 60  # 监控60秒
        start_time = time.time()
        
        while time.time() - start_time < monitor_duration:
            try:
                # 获取统计信息
                stats = sender.get_comprehensive_stats()
                
                # 显示关键信息
                system_status = stats.get('system_status', {})
                sending_stats = stats.get('sending_stats', {})
                account_monitoring = stats.get('account_monitoring', {})
                
                logger.info(f"📊 状态报告:")
                logger.info(f"  - 系统运行: {system_status.get('is_running', False)}")
                logger.info(f"  - 活跃浏览器: {sending_stats.get('active_browsers', 0)}")
                logger.info(f"  - 总任务: {sending_stats.get('total_tasks', 0)}")
                logger.info(f"  - 已完成: {sending_stats.get('completed_tasks', 0)}")
                logger.info(f"  - 失败: {sending_stats.get('failed_tasks', 0)}")
                logger.info(f"  - 每分钟: {sending_stats.get('emails_per_minute', 0):.1f}")
                logger.info(f"  - 账号切换: {account_monitoring.get('total_switches', 0)}")
                
                # 显示浏览器状态
                browser_details = stats.get('browser_details', {})
                for browser_id, details in browser_details.items():
                    current_account = details.get('current_account', '无')
                    is_working = details.get('is_working', False)
                    total_sent = details.get('total_sent', 0)
                    queue_size = details.get('queue_size', 0)
                    
                    status_icon = "🟢" if is_working else "🔴"
                    logger.info(f"  {status_icon} {browser_id}: {current_account} (发送:{total_sent}, 队列:{queue_size})")
                
                # 检查是否完成所有任务
                completed = sending_stats.get('completed_tasks', 0)
                total = sending_stats.get('total_tasks', 0)
                if completed >= total and total > 0:
                    logger.info("🎉 所有任务已完成！")
                    break
                
                time.sleep(5)  # 每5秒报告一次
                
            except Exception as e:
                logger.error(f"❌ 监控过程异常: {e}")
                time.sleep(1)
        
        # 8. 获取最终统计
        logger.info("📊 获取最终统计信息...")
        final_stats = sender.get_comprehensive_stats()
        
        logger.info("🏁 最终统计报告:")
        logger.info(f"  - 总任务: {final_stats['sending_stats'].get('total_tasks', 0)}")
        logger.info(f"  - 已完成: {final_stats['sending_stats'].get('completed_tasks', 0)}")
        logger.info(f"  - 失败: {final_stats['sending_stats'].get('failed_tasks', 0)}")
        logger.info(f"  - 成功率: {(final_stats['sending_stats'].get('completed_tasks', 0) / max(final_stats['sending_stats'].get('total_tasks', 1), 1)) * 100:.1f}%")
        logger.info(f"  - 账号切换次数: {final_stats['account_monitoring'].get('total_switches', 0)}")
        logger.info(f"  - 切换成功率: {final_stats['account_monitoring'].get('switch_success_rate', 0):.1f}%")
        
        # 9. 停止发送
        logger.info("🛑 停止发送系统...")
        sender.stop_sending()
        
        # 10. 清理资源
        logger.info("🧹 清理资源...")
        sender.cleanup()
        
        logger.info("✅ 测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程异常: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_account_switching():
    """测试账号切换功能"""
    logger.info("🔄 开始测试账号切换功能...")
    
    try:
        # 创建少量账号进行快速切换测试
        accounts = create_test_accounts()[:3]  # 只用3个账号
        
        config = IntegratedSendingConfig(
            max_browsers=2,  # 2个浏览器
            emails_per_account=1,  # 每个账号只发送1封邮件，快速触发切换
            send_interval=0.5,  # 0.5秒间隔
            auto_switch_enabled=True,
            monitor_interval=0.2  # 0.2秒监控间隔
        )
        
        sender = IntegratedConcurrentSender(config)
        
        if not sender.initialize(accounts):
            logger.error("❌ 初始化失败")
            return False
        
        if not sender.start_sending():
            logger.error("❌ 启动失败")
            return False
        
        # 添加足够的任务来触发账号切换
        test_emails = [(f"test{i}@example.com", f"测试邮件{i}", f"内容{i}") for i in range(10)]
        sender.add_batch_tasks(test_emails)
        
        # 监控切换过程
        switch_count = 0
        last_switch_count = 0
        
        for i in range(30):  # 监控30秒
            stats = sender.get_comprehensive_stats()
            current_switch_count = stats['account_monitoring'].get('total_switches', 0)
            
            if current_switch_count > last_switch_count:
                switch_count += (current_switch_count - last_switch_count)
                logger.info(f"🔄 检测到账号切换！总切换次数: {current_switch_count}")
                last_switch_count = current_switch_count
            
            time.sleep(1)
        
        sender.stop_sending()
        sender.cleanup()
        
        logger.info(f"🔄 账号切换测试完成，总切换次数: {switch_count}")
        return switch_count > 0
        
    except Exception as e:
        logger.error(f"❌ 账号切换测试异常: {e}")
        return False

def main():
    """主函数"""
    logger.info("🧪 开始并发发送器测试...")
    
    # 测试1: 基本并发发送功能
    logger.info("=" * 50)
    logger.info("测试1: 基本并发发送功能")
    logger.info("=" * 50)
    
    success1 = test_concurrent_sender()
    
    # 等待一段时间
    time.sleep(5)
    
    # 测试2: 账号切换功能
    logger.info("=" * 50)
    logger.info("测试2: 账号切换功能")
    logger.info("=" * 50)
    
    success2 = test_account_switching()
    
    # 总结
    logger.info("=" * 50)
    logger.info("测试总结")
    logger.info("=" * 50)
    
    logger.info(f"基本并发发送功能: {'✅ 通过' if success1 else '❌ 失败'}")
    logger.info(f"账号切换功能: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        logger.info("🎉 所有测试通过！多账号并发发送功能正常工作")
        return 0
    else:
        logger.error("❌ 部分测试失败，需要检查和修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
