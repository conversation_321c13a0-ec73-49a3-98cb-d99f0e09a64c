#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大数据量分批处理管理器
支持几十万封邮件的智能分批处理和逐步发送
"""

import time
import threading
from typing import List, Dict, Optional, Any, Iterator, Callable
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import pandas as pd
from loguru import logger

from .smart_task_queue import SmartTaskQueue, QueueConfig, TaskPriority


class ProcessingStrategy(Enum):
    """处理策略枚举"""
    CONSERVATIVE = "conservative"    # 保守策略：小批量，慢速度
    BALANCED = "balanced"           # 平衡策略：中等批量，中等速度
    AGGRESSIVE = "aggressive"       # 激进策略：大批量，高速度
    CUSTOM = "custom"              # 自定义策略


@dataclass
class BatchConfig:
    """分批配置"""
    strategy: ProcessingStrategy = ProcessingStrategy.BALANCED
    batch_size: int = 1000                    # 每批任务数量
    allocation_interval: int = 60             # 分配间隔(秒)
    max_concurrent_batches: int = 3           # 最大并发批次数
    auto_adjust: bool = True                  # 自动调整批次大小
    min_batch_size: int = 100                # 最小批次大小
    max_batch_size: int = 5000               # 最大批次大小
    success_rate_threshold: float = 0.95     # 成功率阈值
    performance_window: int = 300             # 性能监控窗口(秒)


class BatchProcessor:
    """
    大数据量分批处理管理器
    
    功能特性：
    1. 智能分批策略
    2. 自适应批次大小
    3. 性能监控和调优
    4. 大数据量支持
    5. 内存优化
    """
    
    def __init__(self, task_queue: SmartTaskQueue, config: BatchConfig):
        """
        初始化分批处理管理器
        
        Args:
            task_queue: 智能任务队列
            config: 分批配置
        """
        self.task_queue = task_queue
        self.config = config
        
        # 数据源管理
        self.data_sources: Dict[str, Dict[str, Any]] = {}
        self.current_source: Optional[str] = None
        
        # 处理状态
        self.is_processing = False
        self.is_paused = False
        self.processing_thread: Optional[threading.Thread] = None
        
        # 性能监控
        self.performance_history: List[Dict[str, Any]] = []
        self.last_adjustment_time = 0
        
        # 统计信息
        self.stats = {
            'total_records': 0,
            'processed_records': 0,
            'current_batch_size': config.batch_size,
            'batches_created': 0,
            'avg_processing_time': 0.0,
            'current_throughput': 0.0,
            'estimated_completion_time': None
        }
        
        # 回调函数
        self.on_batch_created: Optional[Callable] = None
        self.on_progress_updated: Optional[Callable] = None
        self.on_processing_completed: Optional[Callable] = None
        
        logger.info("📊 大数据量分批处理管理器初始化完成")
    
    def load_data_source(self, source_id: str, file_path: str, 
                        email_column: str = 'email',
                        subject_template: str = None,
                        content_template: str = None) -> bool:
        """
        加载数据源
        
        Args:
            source_id: 数据源ID
            file_path: 文件路径
            email_column: 邮箱列名
            subject_template: 主题模板
            content_template: 内容模板
            
        Returns:
            是否加载成功
        """
        try:
            logger.info(f"📂 加载数据源: {source_id} from {file_path}")
            
            # 检测文件类型并加载
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8')
            elif file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                df = pd.read_excel(file_path)
            else:
                # 尝试多种编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
                df = None
                for encoding in encodings:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding)
                        break
                    except:
                        continue
                
                if df is None:
                    raise ValueError("无法读取文件，请检查文件格式和编码")
            
            # 验证必要列
            if email_column not in df.columns:
                raise ValueError(f"未找到邮箱列: {email_column}")
            
            # 数据清洗
            df = df.dropna(subset=[email_column])  # 删除邮箱为空的行
            df = df.drop_duplicates(subset=[email_column])  # 删除重复邮箱
            
            # 保存数据源信息
            self.data_sources[source_id] = {
                'dataframe': df,
                'file_path': file_path,
                'email_column': email_column,
                'subject_template': subject_template or "邮件主题",
                'content_template': content_template or "邮件内容",
                'total_records': len(df),
                'processed_records': 0,
                'created_time': datetime.now()
            }
            
            logger.info(f"✅ 数据源加载成功: {len(df)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据源加载失败: {e}")
            return False
    
    def set_current_source(self, source_id: str) -> bool:
        """设置当前数据源"""
        if source_id not in self.data_sources:
            logger.error(f"❌ 数据源不存在: {source_id}")
            return False
        
        self.current_source = source_id
        source = self.data_sources[source_id]
        self.stats['total_records'] = source['total_records']
        self.stats['processed_records'] = source['processed_records']
        
        logger.info(f"📋 设置当前数据源: {source_id}")
        return True
    
    def start_processing(self) -> bool:
        """开始分批处理"""
        if not self.current_source:
            logger.error("❌ 未设置当前数据源")
            return False
        
        if self.is_processing:
            logger.warning("⚠️ 分批处理已在进行中")
            return True
        
        self.is_processing = True
        self.is_paused = False
        
        # 启动处理线程
        self.processing_thread = threading.Thread(target=self._processing_worker, daemon=True)
        self.processing_thread.start()
        
        logger.info("🚀 开始大数据量分批处理")
        return True
    
    def stop_processing(self):
        """停止分批处理"""
        self.is_processing = False
        
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=10)
        
        logger.info("🛑 分批处理已停止")
    
    def pause_processing(self):
        """暂停分批处理"""
        self.is_paused = True
        logger.info("⏸️ 分批处理已暂停")
    
    def resume_processing(self):
        """恢复分批处理"""
        self.is_paused = False
        logger.info("▶️ 分批处理已恢复")
    
    def _processing_worker(self):
        """分批处理工作线程"""
        logger.info("🔄 分批处理工作线程启动")
        
        source = self.data_sources[self.current_source]
        df = source['dataframe']
        
        start_index = source['processed_records']
        current_batch_size = self.config.batch_size
        
        while self.is_processing and start_index < len(df):
            try:
                # 检查暂停状态
                while self.is_paused and self.is_processing:
                    time.sleep(1)
                
                if not self.is_processing:
                    break
                
                # 检查队列容量
                if self._should_wait_for_queue():
                    time.sleep(5)
                    continue
                
                # 获取当前批次数据
                end_index = min(start_index + current_batch_size, len(df))
                batch_df = df.iloc[start_index:end_index]
                
                # 创建批次
                batch_id = self._create_batch_from_dataframe(batch_df, source)
                
                if batch_id:
                    # 更新统计
                    self.stats['batches_created'] += 1
                    self.stats['processed_records'] = end_index
                    source['processed_records'] = end_index
                    
                    # 计算进度
                    progress = (end_index / len(df)) * 100
                    
                    # 估算完成时间
                    if self.stats['current_throughput'] > 0:
                        remaining_records = len(df) - end_index
                        estimated_seconds = remaining_records / self.stats['current_throughput']
                        self.stats['estimated_completion_time'] = datetime.now().timestamp() + estimated_seconds
                    
                    logger.info(f"📦 创建批次: {batch_id}, 进度: {progress:.1f}%")
                    
                    if self.on_batch_created:
                        self.on_batch_created(batch_id, batch_df)
                    
                    if self.on_progress_updated:
                        self.on_progress_updated(self.get_progress_info())
                    
                    # 移动到下一批
                    start_index = end_index
                    
                    # 自适应调整批次大小
                    if self.config.auto_adjust:
                        current_batch_size = self._adjust_batch_size()
                    
                    # 等待间隔
                    time.sleep(self.config.allocation_interval)
                else:
                    logger.error("❌ 批次创建失败，停止处理")
                    break
                
            except Exception as e:
                logger.error(f"❌ 分批处理异常: {e}")
                time.sleep(10)
        
        # 处理完成
        self.is_processing = False
        
        if start_index >= len(df):
            logger.info("🎉 大数据量分批处理完成")
            if self.on_processing_completed:
                self.on_processing_completed()
        
        logger.info("🔄 分批处理工作线程结束")
    
    def _create_batch_from_dataframe(self, batch_df: pd.DataFrame, source: Dict[str, Any]) -> Optional[str]:
        """从DataFrame创建批次"""
        try:
            # 生成批次名称
            batch_name = f"{self.current_source}_batch_{self.stats['batches_created'] + 1}"
            
            # 创建批次
            batch_id = self.task_queue.create_batch(
                name=batch_name,
                total_tasks=len(batch_df),
                priority=TaskPriority.NORMAL,
                batch_size=len(batch_df)
            )
            
            # 准备任务数据
            tasks_data = []
            for _, row in batch_df.iterrows():
                # 生成邮件内容
                email = row[source['email_column']]
                subject = self._render_template(source['subject_template'], row)
                content = self._render_template(source['content_template'], row)
                
                tasks_data.append({
                    'to_email': email,
                    'subject': subject,
                    'content': content,
                    'content_type': 'text/plain',
                    'metadata': {
                        'source_id': self.current_source,
                        'batch_index': self.stats['batches_created'],
                        'row_data': row.to_dict()
                    }
                })
            
            # 添加任务到批次
            task_ids = self.task_queue.add_tasks_to_batch(batch_id, tasks_data)
            
            logger.info(f"✅ 批次创建成功: {batch_id}, 任务数: {len(task_ids)}")
            return batch_id
            
        except Exception as e:
            logger.error(f"❌ 创建批次失败: {e}")
            return None
    
    def _render_template(self, template: str, row: pd.Series) -> str:
        """渲染模板"""
        try:
            # 简单的模板渲染，支持 {column_name} 格式
            result = template
            for column, value in row.items():
                placeholder = f"{{{column}}}"
                if placeholder in result:
                    result = result.replace(placeholder, str(value))
            return result
        except Exception as e:
            logger.error(f"❌ 模板渲染失败: {e}")
            return template
    
    def _should_wait_for_queue(self) -> bool:
        """检查是否应该等待队列"""
        queue_stats = self.task_queue.get_stats()
        
        # 检查队列大小
        if queue_stats['queued_tasks'] > self.config.max_batch_size:
            return True
        
        # 检查并发批次数
        active_batches = queue_stats.get('active_batches', 0)
        if active_batches >= self.config.max_concurrent_batches:
            return True
        
        return False
    
    def _adjust_batch_size(self) -> int:
        """自适应调整批次大小"""
        current_time = time.time()
        
        # 避免频繁调整
        if current_time - self.last_adjustment_time < 60:
            return self.stats['current_batch_size']
        
        # 获取性能数据
        queue_stats = self.task_queue.get_stats()
        success_rate = queue_stats.get('success_rate', 0)
        
        current_size = self.stats['current_batch_size']
        new_size = current_size
        
        # 根据成功率调整
        if success_rate >= self.config.success_rate_threshold:
            # 成功率高，可以增加批次大小
            new_size = min(current_size * 1.2, self.config.max_batch_size)
        elif success_rate < 0.8:
            # 成功率低，减少批次大小
            new_size = max(current_size * 0.8, self.config.min_batch_size)
        
        # 根据处理速度调整
        throughput = queue_stats.get('throughput', 0)
        if throughput > 0:
            self.stats['current_throughput'] = throughput
            
            # 如果处理速度太慢，减少批次大小
            if throughput < 10:  # 每分钟少于10个任务
                new_size = max(new_size * 0.9, self.config.min_batch_size)
        
        new_size = int(new_size)
        
        if new_size != current_size:
            logger.info(f"🔧 调整批次大小: {current_size} → {new_size}")
            self.stats['current_batch_size'] = new_size
            self.last_adjustment_time = current_time
        
        return new_size
    
    def get_progress_info(self) -> Dict[str, Any]:
        """获取处理进度信息"""
        if not self.current_source:
            return {}
        
        source = self.data_sources[self.current_source]
        total = source['total_records']
        processed = source['processed_records']
        
        progress_percent = (processed / total * 100) if total > 0 else 0
        
        return {
            'source_id': self.current_source,
            'total_records': total,
            'processed_records': processed,
            'remaining_records': total - processed,
            'progress_percent': round(progress_percent, 2),
            'batches_created': self.stats['batches_created'],
            'current_batch_size': self.stats['current_batch_size'],
            'is_processing': self.is_processing,
            'is_paused': self.is_paused,
            'estimated_completion_time': self.stats['estimated_completion_time']
        }
    
    def get_data_sources_info(self) -> List[Dict[str, Any]]:
        """获取所有数据源信息"""
        sources_info = []
        for source_id, source in self.data_sources.items():
            sources_info.append({
                'source_id': source_id,
                'file_path': source['file_path'],
                'total_records': source['total_records'],
                'processed_records': source['processed_records'],
                'progress_percent': round((source['processed_records'] / source['total_records'] * 100), 2),
                'created_time': source['created_time'].isoformat(),
                'is_current': source_id == self.current_source
            })
        return sources_info
    
    def remove_data_source(self, source_id: str) -> bool:
        """删除数据源"""
        if source_id not in self.data_sources:
            return False
        
        if source_id == self.current_source:
            self.current_source = None
        
        del self.data_sources[source_id]
        logger.info(f"🗑️ 删除数据源: {source_id}")
        return True
    
    def reset_source_progress(self, source_id: str) -> bool:
        """重置数据源进度"""
        if source_id not in self.data_sources:
            return False
        
        self.data_sources[source_id]['processed_records'] = 0
        logger.info(f"🔄 重置数据源进度: {source_id}")
        return True
