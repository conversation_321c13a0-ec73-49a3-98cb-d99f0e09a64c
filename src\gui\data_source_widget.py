#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源管理界面组件
管理收件人数据源，包括导入数据、文件监控数据等
"""

import csv
import json
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QGroupBox, QComboBox, QLineEdit, QTabWidget,
    QMessageBox, QFileDialog, QDialog, QFormLayout, QDialogButtonBox,
    QTextEdit, QSpinBox, QCheckBox, QHeaderView
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QColor
from typing import List, Dict, Any, Optional
from src.core.data_source_manager import DataSourceManager, RecipientData
from src.models.database import DatabaseManager
from src.utils.logger import get_logger

logger = get_logger("DataSourceWidget")


class RecipientEditDialog(QDialog):
    """收件人编辑对话框"""
    
    def __init__(self, parent=None, recipient: Optional[RecipientData] = None):
        super().__init__(parent)
        self.recipient = recipient
        self.is_edit_mode = recipient is not None
        
        self.init_ui()
        if self.recipient:
            self.load_recipient_data()
    
    def init_ui(self):
        """初始化界面"""
        title = "编辑收件人" if self.is_edit_mode else "新建收件人"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout()
        
        # 表单
        form_layout = QFormLayout()
        
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("请输入邮箱地址")
        form_layout.addRow("邮箱地址:", self.email_edit)
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入姓名")
        form_layout.addRow("姓名:", self.name_edit)
        
        self.company_edit = QLineEdit()
        self.company_edit.setPlaceholderText("请输入公司名称")
        form_layout.addRow("公司:", self.company_edit)
        
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("请输入电话号码")
        form_layout.addRow("电话:", self.phone_edit)
        
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("请输入职位")
        form_layout.addRow("职位:", self.title_edit)
        
        self.source_combo = QComboBox()
        self.source_combo.addItems(["manual", "import", "monitor", "database"])
        form_layout.addRow("数据源:", self.source_combo)
        
        layout.addLayout(form_layout)
        
        # 变量编辑
        variables_group = QGroupBox("自定义变量")
        variables_layout = QVBoxLayout()
        
        self.variables_edit = QTextEdit()
        self.variables_edit.setPlaceholderText('JSON格式:\n{\n  "key1": "value1",\n  "key2": "value2"\n}')
        self.variables_edit.setMaximumHeight(100)
        variables_layout.addWidget(self.variables_edit)
        
        variables_group.setLayout(variables_layout)
        layout.addWidget(variables_group)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def load_recipient_data(self):
        """加载收件人数据"""
        if not self.recipient:
            return
        
        self.email_edit.setText(self.recipient.email)
        self.name_edit.setText(self.recipient.name or "")
        self.company_edit.setText(self.recipient.company or "")
        self.phone_edit.setText(self.recipient.phone or "")
        self.title_edit.setText(self.recipient.title or "")
        
        # 设置数据源
        index = self.source_combo.findText(self.recipient.source)
        if index >= 0:
            self.source_combo.setCurrentIndex(index)
        
        # 加载变量
        if self.recipient.variables:
            variables_json = json.dumps(self.recipient.variables, ensure_ascii=False, indent=2)
            self.variables_edit.setPlainText(variables_json)
    
    def get_recipient_data(self) -> RecipientData:
        """获取收件人数据"""
        # 解析变量
        variables = {}
        variables_text = self.variables_edit.toPlainText().strip()
        if variables_text:
            try:
                variables = json.loads(variables_text)
            except json.JSONDecodeError:
                pass
        
        return RecipientData(
            email=self.email_edit.text().strip(),
            name=self.name_edit.text().strip() or None,
            company=self.company_edit.text().strip() or None,
            phone=self.phone_edit.text().strip() or None,
            title=self.title_edit.text().strip() or None,
            source=self.source_combo.currentText(),
            variables=variables
        )
    
    def accept(self):
        """确认保存"""
        # 验证数据
        if not self.email_edit.text().strip():
            QMessageBox.warning(self, "警告", "请输入邮箱地址")
            self.email_edit.setFocus()
            return
        
        # 验证邮箱格式
        import re
        email = self.email_edit.text().strip()
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, email):
            QMessageBox.warning(self, "警告", "邮箱格式不正确")
            self.email_edit.setFocus()
            return
        
        # 验证变量格式
        variables_text = self.variables_edit.toPlainText().strip()
        if variables_text:
            try:
                json.loads(variables_text)
            except json.JSONDecodeError:
                QMessageBox.warning(self, "警告", "变量格式不正确，请使用JSON格式")
                self.variables_edit.setFocus()
                return
        
        super().accept()


class DataSourceWidget(QWidget):
    """数据源管理主界面"""
    
    # 信号定义
    recipients_selected = pyqtSignal(list)  # 收件人被选中
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.data_source_manager = DataSourceManager(db_manager)
        
        self.init_ui()
        self.load_recipients()
        self.load_statistics()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 收件人管理选项卡
        recipients_tab = self.create_recipients_tab()
        self.tab_widget.addTab(recipients_tab, "收件人管理")
        
        # 数据源统计选项卡
        statistics_tab = self.create_statistics_tab()
        self.tab_widget.addTab(statistics_tab, "数据源统计")
        
        layout.addWidget(self.tab_widget)
        self.setLayout(layout)
    
    def create_recipients_tab(self) -> QWidget:
        """创建收件人管理选项卡"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.add_recipient_btn = QPushButton("新建收件人")
        self.add_recipient_btn.clicked.connect(self.add_recipient)
        toolbar_layout.addWidget(self.add_recipient_btn)
        
        self.edit_recipient_btn = QPushButton("编辑收件人")
        self.edit_recipient_btn.clicked.connect(self.edit_recipient)
        self.edit_recipient_btn.setEnabled(False)
        toolbar_layout.addWidget(self.edit_recipient_btn)
        
        self.delete_recipient_btn = QPushButton("删除收件人")
        self.delete_recipient_btn.clicked.connect(self.delete_recipient)
        self.delete_recipient_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_recipient_btn)
        
        toolbar_layout.addWidget(QLabel("|"))
        
        self.import_recipients_btn = QPushButton("导入收件人")
        self.import_recipients_btn.clicked.connect(self.import_recipients)
        toolbar_layout.addWidget(self.import_recipients_btn)

        self.download_template_btn = QPushButton("下载模板")
        self.download_template_btn.clicked.connect(self.download_template)
        toolbar_layout.addWidget(self.download_template_btn)

        self.export_recipients_btn = QPushButton("导出收件人")
        self.export_recipients_btn.clicked.connect(self.export_recipients)
        toolbar_layout.addWidget(self.export_recipients_btn)
        
        toolbar_layout.addStretch()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_recipients)
        toolbar_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 过滤器
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("数据源:"))
        self.source_filter = QComboBox()
        self.source_filter.addItem("全部", None)
        self.source_filter.addItems(["manual", "import", "monitor", "database"])
        self.source_filter.currentIndexChanged.connect(self.filter_recipients)
        filter_layout.addWidget(self.source_filter)
        
        filter_layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索邮箱、姓名或公司")
        self.search_edit.textChanged.connect(self.search_recipients)
        filter_layout.addWidget(self.search_edit)
        
        filter_layout.addStretch()
        layout.addLayout(filter_layout)
        
        # 收件人表格
        self.recipients_table = QTableWidget()
        self.recipients_table.setColumnCount(7)
        self.recipients_table.setHorizontalHeaderLabels([
            "邮箱", "姓名", "公司", "电话", "职位", "数据源", "创建时间"
        ])
        
        # 设置列宽
        header = self.recipients_table.horizontalHeader()
        header.resizeSection(0, 200)  # 邮箱
        header.resizeSection(1, 100)  # 姓名
        header.resizeSection(2, 150)  # 公司
        header.resizeSection(3, 120)  # 电话
        header.resizeSection(4, 100)  # 职位
        header.resizeSection(5, 80)   # 数据源
        header.setStretchLastSection(True)  # 创建时间
        
        self.recipients_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.recipients_table.setAlternatingRowColors(True)
        self.recipients_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.recipients_table.itemDoubleClicked.connect(self.edit_recipient)
        
        layout.addWidget(self.recipients_table)
        
        # 批量操作
        batch_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_recipients)
        batch_layout.addWidget(self.select_all_btn)
        
        self.use_selected_btn = QPushButton("使用选中的收件人")
        self.use_selected_btn.clicked.connect(self.use_selected_recipients)
        self.use_selected_btn.setEnabled(False)
        batch_layout.addWidget(self.use_selected_btn)
        
        self.clear_source_btn = QPushButton("清空数据源")
        self.clear_source_btn.clicked.connect(self.clear_data_source)
        batch_layout.addWidget(self.clear_source_btn)
        
        batch_layout.addStretch()
        layout.addLayout(batch_layout)
        
        tab.setLayout(layout)
        return tab
    
    def create_statistics_tab(self) -> QWidget:
        """创建数据源统计选项卡"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 统计信息
        stats_group = QGroupBox("数据源统计")
        stats_layout = QVBoxLayout()
        
        self.stats_table = QTableWidget()
        self.stats_table.setColumnCount(2)
        self.stats_table.setHorizontalHeaderLabels(["数据源", "收件人数量"])
        self.stats_table.horizontalHeader().setStretchLastSection(True)
        stats_layout.addWidget(self.stats_table)
        
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)
        
        # 操作按钮
        operations_layout = QHBoxLayout()
        
        self.refresh_stats_btn = QPushButton("刷新统计")
        self.refresh_stats_btn.clicked.connect(self.load_statistics)
        operations_layout.addWidget(self.refresh_stats_btn)
        
        self.clear_all_btn = QPushButton("清空所有数据")
        self.clear_all_btn.clicked.connect(self.clear_all_data)
        operations_layout.addWidget(self.clear_all_btn)
        
        operations_layout.addStretch()
        layout.addLayout(operations_layout)
        
        tab.setLayout(layout)
        return tab

    def load_recipients(self):
        """加载收件人列表"""
        try:
            # 限制一次性加载的数量，避免界面卡顿
            max_load_count = 1000  # 减少到1000条以提高性能
            recipients = self.data_source_manager.get_all_recipients(max_load_count)
            self.update_recipients_table(recipients)

            # 如果加载了最大数量，提示用户
            if len(recipients) == max_load_count:
                logger.warning(f"已加载 {len(recipients)} 个收件人（达到最大显示数量），如需查看更多请使用搜索功能")
            else:
                logger.info(f"加载了 {len(recipients)} 个收件人")

        except Exception as e:
            logger.error(f"加载收件人失败: {e}")
            QMessageBox.critical(self, "错误", f"加载收件人失败: {e}")

    def update_recipients_table(self, recipients: List[RecipientData]):
        """更新收件人表格"""
        try:
            # 禁用排序和信号以提高性能
            self.recipients_table.setSortingEnabled(False)
            self.recipients_table.blockSignals(True)

            self.recipients_table.setRowCount(len(recipients))

            for row, recipient in enumerate(recipients):
                # 邮箱
                self.recipients_table.setItem(row, 0, QTableWidgetItem(recipient.email))

                # 姓名
                self.recipients_table.setItem(row, 1, QTableWidgetItem(recipient.name or ""))

                # 公司
                self.recipients_table.setItem(row, 2, QTableWidgetItem(recipient.company or ""))

                # 电话
                self.recipients_table.setItem(row, 3, QTableWidgetItem(recipient.phone or ""))

                # 职位
                self.recipients_table.setItem(row, 4, QTableWidgetItem(recipient.title or ""))

                # 数据源
                source_item = QTableWidgetItem(recipient.source)
                if recipient.source == "import":
                    source_item.setBackground(QColor(144, 238, 144))  # 浅绿色
                elif recipient.source == "monitor":
                    source_item.setBackground(QColor(255, 255, 144))  # 浅黄色
                elif recipient.source == "database":
                    source_item.setBackground(QColor(173, 216, 230))  # 浅蓝色
                self.recipients_table.setItem(row, 5, source_item)

                # 创建时间
                create_time = recipient.created_time.strftime("%Y-%m-%d %H:%M") if recipient.created_time else ""
                self.recipients_table.setItem(row, 6, QTableWidgetItem(create_time))

                # 存储收件人对象
                self.recipients_table.item(row, 0).setData(Qt.UserRole, recipient)

        except Exception as e:
            logger.error(f"更新收件人表格失败: {e}")
        finally:
            # 重新启用排序和信号
            self.recipients_table.setSortingEnabled(True)
            self.recipients_table.blockSignals(False)

    def on_selection_changed(self):
        """选择改变时的处理"""
        selected_rows = self.recipients_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        self.edit_recipient_btn.setEnabled(has_selection and len(selected_rows) == 1)
        self.delete_recipient_btn.setEnabled(has_selection)
        self.use_selected_btn.setEnabled(has_selection)

    def add_recipient(self):
        """新建收件人"""
        dialog = RecipientEditDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                recipient = dialog.get_recipient_data()
                success = self.data_source_manager.add_recipient(recipient)

                if success:
                    QMessageBox.information(self, "成功", "收件人添加成功")
                    self.load_recipients()
                    self.load_statistics()
                else:
                    QMessageBox.warning(self, "警告", "收件人添加失败")

            except Exception as e:
                logger.error(f"添加收件人失败: {e}")
                QMessageBox.critical(self, "错误", f"添加收件人失败: {e}")

    def edit_recipient(self):
        """编辑收件人"""
        selected_rows = self.recipients_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        recipient = self.recipients_table.item(row, 0).data(Qt.UserRole)

        if not recipient:
            return

        dialog = RecipientEditDialog(self, recipient)
        if dialog.exec_() == QDialog.Accepted:
            try:
                updated_recipient = dialog.get_recipient_data()
                success = self.data_source_manager.update_recipient(updated_recipient)

                if success:
                    QMessageBox.information(self, "成功", "收件人更新成功")
                    self.load_recipients()
                else:
                    QMessageBox.warning(self, "警告", "收件人更新失败")

            except Exception as e:
                logger.error(f"更新收件人失败: {e}")
                QMessageBox.critical(self, "错误", f"更新收件人失败: {e}")

    def delete_recipient(self):
        """删除收件人"""
        selected_rows = self.recipients_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        emails = []
        for row_index in selected_rows:
            row = row_index.row()
            recipient = self.recipients_table.item(row, 0).data(Qt.UserRole)
            if recipient:
                emails.append(recipient.email)

        if not emails:
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除选中的 {len(emails)} 个收件人吗？\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success_count = 0
                for email in emails:
                    if self.data_source_manager.delete_recipient(email):
                        success_count += 1

                QMessageBox.information(self, "成功", f"删除了 {success_count} 个收件人")
                self.load_recipients()
                self.load_statistics()

            except Exception as e:
                logger.error(f"删除收件人失败: {e}")
                QMessageBox.critical(self, "错误", f"删除收件人失败: {e}")

    def filter_recipients(self):
        """过滤收件人"""
        try:
            source_filter = self.source_filter.currentData()

            if source_filter:
                recipients = self.data_source_manager.get_recipients_by_source(source_filter, 2000)
            else:
                recipients = self.data_source_manager.get_all_recipients(2000)

            self.update_recipients_table(recipients)

        except Exception as e:
            logger.error(f"过滤收件人失败: {e}")

    def search_recipients(self):
        """搜索收件人"""
        keyword = self.search_edit.text().strip()

        try:
            if keyword:
                recipients = self.data_source_manager.search_recipients(keyword, 500)
            else:
                # 如果搜索框为空，应用数据源过滤
                self.filter_recipients()
                return

            self.update_recipients_table(recipients)

        except Exception as e:
            logger.error(f"搜索收件人失败: {e}")

    def select_all_recipients(self):
        """全选收件人"""
        try:
            # 禁用信号以提高性能
            self.recipients_table.blockSignals(True)

            # 检查当前是否已全选
            total_rows = self.recipients_table.rowCount()
            selected_rows = len(self.recipients_table.selectionModel().selectedRows())

            if selected_rows == total_rows and total_rows > 0:
                # 如果已全选，则取消全选
                self.recipients_table.clearSelection()
                self.select_all_btn.setText("全选")
            else:
                # 否则执行全选
                self.recipients_table.selectAll()
                self.select_all_btn.setText("取消全选")

        except Exception as e:
            logger.error(f"全选操作失败: {e}")
        finally:
            # 重新启用信号
            self.recipients_table.blockSignals(False)
            # 手动触发选择变化事件
            self.on_selection_changed()

    def use_selected_recipients(self):
        """使用选中的收件人"""
        selected_rows = self.recipients_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        recipients = []
        for row_index in selected_rows:
            row = row_index.row()
            recipient = self.recipients_table.item(row, 0).data(Qt.UserRole)
            if recipient:
                recipients.append(recipient)

        if recipients:
            self.recipients_selected.emit(recipients)
            QMessageBox.information(self, "成功", f"已选择 {len(recipients)} 个收件人")

    def import_recipients(self):
        """导入收件人"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择收件人文件", "",
            "CSV Files (*.csv);;Excel Files (*.xlsx);;JSON Files (*.json)"
        )

        if file_path:
            try:
                self.import_recipients_from_file(file_path)
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导入收件人失败: {e}")
                logger.error(f"导入收件人失败: {e}")

    def import_recipients_from_file(self, file_path: str):
        """从文件导入收件人"""
        # 这里可以复用之前的导入逻辑
        # 简化版本，只处理CSV
        if not file_path.endswith('.csv'):
            raise ValueError("目前只支持CSV格式")

        recipients = []

        # 尝试多种编码格式
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
        csvfile = None

        for encoding in encodings:
            try:
                csvfile = open(file_path, 'r', encoding=encoding)
                reader = csv.DictReader(csvfile)
                # 尝试读取第一行来验证编码是否正确
                first_row = next(reader, None)
                if first_row:
                    # 重新打开文件从头开始读取
                    csvfile.close()
                    csvfile = open(file_path, 'r', encoding=encoding)
                    reader = csv.DictReader(csvfile)

                    for row in reader:
                        email = row.get('email', '').strip()
                        if email:
                            recipient = RecipientData(
                                email=email,
                                name=row.get('name', '').strip() or None,
                                company=row.get('company', '').strip() or None,
                                phone=row.get('phone', '').strip() or None,
                                title=row.get('title', '').strip() or None,
                                source='import'
                            )
                            recipients.append(recipient)
                    break
                else:
                    csvfile.close()

            except (UnicodeDecodeError, UnicodeError):
                if csvfile:
                    csvfile.close()
                continue
            except Exception as e:
                if csvfile:
                    csvfile.close()
                raise e

        if csvfile:
            csvfile.close()

        if not recipients:
            raise ValueError("无法读取文件或文件中没有有效的收件人数据，请检查文件格式和编码")

        success_count = self.data_source_manager.add_recipients_batch(recipients)
        QMessageBox.information(self, "成功", f"导入了 {success_count} 个收件人")
        self.load_recipients()
        self.load_statistics()

    def export_recipients(self):
        """导出收件人"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存收件人文件", "recipients.csv", "CSV Files (*.csv)"
        )

        if file_path:
            try:
                recipients = self.data_source_manager.get_all_recipients(10000)

                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['email', 'name', 'company', 'phone', 'title', 'source']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                    writer.writeheader()
                    for recipient in recipients:
                        writer.writerow({
                            'email': recipient.email,
                            'name': recipient.name or '',
                            'company': recipient.company or '',
                            'phone': recipient.phone or '',
                            'title': recipient.title or '',
                            'source': recipient.source
                        })

                QMessageBox.information(self, "成功", f"导出了 {len(recipients)} 个收件人到 {file_path}")

            except Exception as e:
                logger.error(f"导出收件人失败: {e}")
                QMessageBox.critical(self, "错误", f"导出收件人失败: {e}")

    def load_statistics(self):
        """加载统计信息"""
        try:
            stats = self.data_source_manager.get_source_statistics()

            self.stats_table.setRowCount(len(stats))

            source_names = {
                'manual': '手动输入',
                'import': '导入数据',
                'monitor': '文件监控',
                'database': '数据库查询'
            }

            for row, (source, count) in enumerate(stats.items()):
                source_name = source_names.get(source, source)
                self.stats_table.setItem(row, 0, QTableWidgetItem(source_name))
                self.stats_table.setItem(row, 1, QTableWidgetItem(str(count)))

        except Exception as e:
            logger.error(f"加载统计信息失败: {e}")

    def clear_data_source(self):
        """清空数据源"""
        source_filter = self.source_filter.currentData()
        if not source_filter:
            QMessageBox.warning(self, "警告", "请先选择要清空的数据源")
            return

        reply = QMessageBox.question(
            self, "确认清空",
            f"确定要清空数据源 '{source_filter}' 的所有收件人吗？\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                deleted_count = self.data_source_manager.delete_recipients_by_source(source_filter)
                QMessageBox.information(self, "成功", f"清空了 {deleted_count} 个收件人")
                self.load_recipients()
                self.load_statistics()

            except Exception as e:
                logger.error(f"清空数据源失败: {e}")
                QMessageBox.critical(self, "错误", f"清空数据源失败: {e}")

    def clear_all_data(self):
        """清空所有数据"""
        reply = QMessageBox.question(
            self, "确认清空",
            "确定要清空所有收件人数据吗？\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                deleted_count = self.data_source_manager.clear_all_recipients()
                QMessageBox.information(self, "成功", f"清空了 {deleted_count} 个收件人")
                self.load_recipients()
                self.load_statistics()

            except Exception as e:
                logger.error(f"清空所有数据失败: {e}")
                QMessageBox.critical(self, "错误", f"清空所有数据失败: {e}")

    def download_template(self):
        """下载收件人导入模板"""
        import os
        import shutil

        # 模板文件路径
        template_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                   'resources', 'recipients_template.csv')

        if not os.path.exists(template_path):
            QMessageBox.warning(self, "警告", "模板文件不存在，请联系管理员")
            return

        # 让用户选择保存位置
        save_path, _ = QFileDialog.getSaveFileName(
            self, "保存收件人导入模板", "收件人导入模板.csv",
            "CSV Files (*.csv)"
        )

        if save_path:
            try:
                shutil.copy2(template_path, save_path)
                QMessageBox.information(self, "成功", f"模板已保存到: {save_path}")
            except Exception as e:
                logger.error(f"下载模板失败: {e}")
                QMessageBox.critical(self, "错误", f"下载模板失败: {e}")
