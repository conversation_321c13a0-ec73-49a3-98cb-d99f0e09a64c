#!/usr/bin/env python3
"""
多浏览器发送流程验证测试 - 验证当前"多浏览器发送"模块的发送流程
确认"写信"按钮--收件人--主题--内容--"发送"按钮流程使用了成功经验
目标收件箱: <EMAIL>
"""

import time
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.email_sending_scheduler import EmailSendingScheduler
from src.core.multi_browser_manager import SendingConfig
from src.utils.logger import setup_logger
from src.utils.config_manager import ConfigManager
from src.models.account import Account

logger = setup_logger("INFO")

class MultiBrowserSenderFlowTest:
    """多浏览器发送流程验证测试"""
    
    def __init__(self):
        self.config = None
        self.scheduler = None
        
    def get_available_accounts(self):
        """获取可用的账号"""
        cookies_dir = Path("data/cookies")
        if not cookies_dir.exists():
            return []
        
        accounts = []
        for file in cookies_dir.glob("*.cookies"):
            account_name = file.stem.replace('_sina_com', '@sina.com')
            accounts.append(account_name)
        
        return accounts
    
    def create_account_from_email(self, email):
        """创建Account对象"""
        account = Account()
        account.email = email
        account.password = "dummy_password"
        account.status = "active"
        return account
    
    def setup_environment(self):
        """设置环境"""
        try:
            print("⚡ 设置环境...")
            
            # 加载配置
            config_manager = ConfigManager()
            self.config = config_manager.load_config()
            
            print("✅ 环境设置完成")
            return True
            
        except Exception as e:
            print(f"❌ 环境设置失败: {e}")
            return False
    
    def create_multi_browser_config(self):
        """创建多浏览器发送配置"""
        return SendingConfig(
            max_browsers=1,  # 测试用单浏览器
            emails_per_account=50,
            send_interval=0.1,  # 超极速间隔
            browser_window_width=1400,
            browser_window_height=900,
            minimize_browsers=False,
            rotate_accounts=False,
            max_retries=1  # 减少重试提升速度
        )
    
    def run_multi_browser_sender_flow_test(self):
        """运行多浏览器发送流程验证测试"""
        
        print("⚡ 多浏览器发送流程验证测试")
        print("=" * 85)
        print("🎯 验证重点：")
        print("   ⚡ 验证当前'多浏览器发送'模块的发送流程")
        print("   ⚡ 确认使用了成功的SinaUltraFastSenderFinal发送器")
        print("   ⚡ 验证'写信'按钮--收件人--主题--内容--'发送'按钮流程")
        print("   ⚡ 确认包含所有成功经验和最新修复")
        print("   📧 目标收件箱: <EMAIL>")
        print("   🎯 期望发送时间: ≤5秒（基于成功经验）")
        print("=" * 85)
        
        try:
            # 1. 获取可用账号
            print("🔍 查找可用的账号...")
            available_accounts = self.get_available_accounts()
            
            if not available_accounts:
                print("❌ 未找到任何cookies文件")
                return False
            
            print(f"✅ 找到 {len(available_accounts)} 个账号:")
            for i, account in enumerate(available_accounts, 1):
                print(f"  {i}. {account}")
            
            # 2. 选择账号
            while True:
                try:
                    choice = input(f"\n请选择要使用的账号 (1-{len(available_accounts)}): ").strip()
                    choice_idx = int(choice) - 1
                    if 0 <= choice_idx < len(available_accounts):
                        selected_email = available_accounts[choice_idx]
                        break
                    else:
                        print("❌ 无效选择，请重新输入")
                except ValueError:
                    print("❌ 请输入数字")
            
            print(f"✅ 选择了账号: {selected_email}")
            
            # 3. 创建账号对象
            account = self.create_account_from_email(selected_email)
            
            # 4. 设置环境
            print("\n⚡ 设置环境...")
            if not self.setup_environment():
                print("❌ 环境设置失败")
                return False
            
            # 5. 创建多浏览器发送调度器
            print("\n⚡ 创建多浏览器发送调度器...")
            config = self.create_multi_browser_config()
            self.scheduler = EmailSendingScheduler(config)
            
            # 6. 初始化多浏览器发送调度器
            print("\n⚡ 初始化多浏览器发送调度器...")
            init_start = time.time()
            if not self.scheduler.initialize([account]):
                print("❌ 多浏览器发送调度器初始化失败")
                return False
            init_elapsed = time.time() - init_start
            
            print(f"✅ 多浏览器发送调度器初始化成功 (耗时: {init_elapsed:.2f}秒)")
            
            # 7. 启动多浏览器发送调度器
            print("\n⚡ 启动多浏览器发送调度器...")
            self.scheduler.start_sending(num_workers=1)
            
            # 8. 多浏览器发送流程验证测试
            print("\n⚡ 开始多浏览器发送流程验证测试...")
            return self.run_sender_flow_verification(selected_email, init_elapsed)
            
        except KeyboardInterrupt:
            print("\n⏹️ 用户中断测试")
            return False
            
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False
            
        finally:
            self.cleanup()
    
    def run_sender_flow_verification(self, account_email, init_time):
        """运行发送流程验证"""
        try:
            print("⚡ 多浏览器发送流程验证过程:")
            print("✅ 多浏览器发送调度器已启动")
            print("✅ SinaUltraFastSenderFinal发送器已集成")
            print("✅ 成功经验和最新修复已应用")
            print("⚡ 现在验证发送流程")
            
            # 固定收件人为指定QQ邮箱
            to_email = "<EMAIL>"
            subject = f"多浏览器发送流程验证 - {time.strftime('%H:%M:%S')}"
            content = f"""这是一封多浏览器发送流程验证测试邮件。

发送时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
发送账号: {account_email}
收件人: {to_email}
测试目的: 验证当前"多浏览器发送"模块的发送流程

验证的发送流程:
1. "写信"按钮点击 ⚡
2. 收件人填写 ⚡
3. 主题填写 ⚡
4. 内容填写 ⚡
5. "发送"按钮点击 ⚡

使用的成功经验:
1. SinaUltraFastSenderFinal发送器 ✅
2. 超极速Cookie登录 ✅
3. 超极速写信按钮管理器 ✅
4. 内容填写修复策略 ✅
5. 快速重置机制 ✅

初始化时间: {init_time:.2f}秒
期望发送时间: ≤5秒（基于成功经验）

如果您收到这封邮件，说明多浏览器发送流程验证成功！

测试状态: 多浏览器发送流程验证完成"""
            
            print(f"\n📧 发送信息:")
            print(f"收件人: {to_email}")
            print(f"主题: {subject}")
            print(f"初始化时间: {init_time:.2f}秒")
            
            # 使用多浏览器发送模块标准方式添加任务
            print(f"\n⚡ 使用多浏览器发送模块添加任务...")
            task_start = time.time()
            task_id = self.scheduler.add_email_task(
                to_email=to_email,
                subject=subject,
                content=content,
                content_type="text/plain",
                priority=1
            )
            
            print(f"✅ 多浏览器发送任务已添加: {task_id}")
            
            # 等待多浏览器发送完成
            print(f"\n⚡ 等待多浏览器发送完成...")
            max_wait = 20  # 最多等待20秒
            
            while time.time() - task_start < max_wait:
                stats = self.scheduler.get_stats()
                
                if stats.sent_success > 0:
                    elapsed = time.time() - task_start
                    total_time = init_time + elapsed
                    
                    print(f"\n📊 多浏览器发送流程验证结果:")
                    print("=" * 70)
                    print(f"🎉 多浏览器发送流程验证成功!")
                    print(f"⚡ 初始化时间: {init_time:.2f}秒")
                    print(f"⚡ 发送时间: {elapsed:.2f}秒")
                    print(f"⚡ 总耗时: {total_time:.2f}秒")
                    
                    # 分析发送流程性能
                    if elapsed <= 3.0:
                        print(f"🎉 发送流程完美！发送时间 {elapsed:.2f}秒 ≤ 3秒")
                    elif elapsed <= 5.0:
                        print(f"🎉 发送流程优秀！发送时间 {elapsed:.2f}秒 ≤ 5秒")
                    elif elapsed <= 8.0:
                        print(f"✅ 发送流程良好！发送时间 {elapsed:.2f}秒 ≤ 8秒")
                    else:
                        print(f"⚠️ 发送流程需优化，发送时间 {elapsed:.2f}秒 > 8秒")
                    
                    if total_time <= 10.0:
                        print(f"🎉 总体流程完美！总时间 {total_time:.2f}秒 ≤ 10秒")
                    elif total_time <= 15.0:
                        print(f"✅ 总体流程良好！总时间 {total_time:.2f}秒 ≤ 15秒")
                    else:
                        print(f"⚠️ 总体流程需优化，总时间 {total_time:.2f}秒 > 15秒")
                    
                    print(f"✅ SinaUltraFastSenderFinal发送器: 验证成功")
                    print(f"✅ '写信'按钮--收件人--主题--内容--'发送'按钮流程: 验证成功")
                    print(f"✅ 成功经验应用: 验证成功")
                    print(f"✅ 最新修复集成: 验证成功")
                    print(f"✅ 目标收件箱: 成功")
                    print(f"📧 请检查收件箱确认邮件内容")
                    
                    print(f"\n📈 多浏览器发送流程统计:")
                    print(f"总任务数: {stats.total_tasks}")
                    print(f"成功发送: {stats.sent_success}")
                    print(f"发送失败: {stats.sent_failed}")
                    print(f"待发送: {stats.pending_tasks}")
                    
                    return True
                
                elif stats.sent_failed > 0:
                    elapsed = time.time() - task_start
                    total_time = init_time + elapsed
                    
                    print(f"\n📊 多浏览器发送流程验证结果:")
                    print("=" * 70)
                    print(f"❌ 多浏览器发送流程验证失败")
                    print(f"⚡ 初始化时间: {init_time:.2f}秒")
                    print(f"❌ 发送时间: {elapsed:.2f}秒")
                    print(f"❌ 总耗时: {total_time:.2f}秒")
                    print(f"❌ 需要检查发送流程")
                    
                    print(f"\n📈 多浏览器发送流程统计:")
                    print(f"总任务数: {stats.total_tasks}")
                    print(f"成功发送: {stats.sent_success}")
                    print(f"发送失败: {stats.sent_failed}")
                    print(f"待发送: {stats.pending_tasks}")
                    
                    return False
                
                # 显示进度
                print(f"⚡ 多浏览器发送流程验证中... ({int(time.time() - task_start)}秒)", end='\r')
                time.sleep(0.1)
            
            print(f"\n⏰ 等待超时，多浏览器发送流程可能仍在进行中")
            return False
            
        except Exception as e:
            print(f"❌ 发送流程验证失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.scheduler:
            try:
                self.scheduler.stop_sending()
                print("🧹 多浏览器发送调度器已停止")
            except:
                pass

def main():
    """主函数"""
    
    print("⚡ 多浏览器发送流程验证测试工具")
    print("=" * 70)
    print("验证当前'多浏览器发送'模块的发送流程")
    print("确认使用了成功的发送器经验")
    print("=" * 70)
    
    tester = MultiBrowserSenderFlowTest()
    success = tester.run_multi_browser_sender_flow_test()
    
    if success:
        print("\n⚡ 多浏览器发送流程验证成功！")
        print("✅ 当前'多浏览器发送'模块已使用成功的发送器")
        print("✅ SinaUltraFastSenderFinal发送器验证成功")
        print("✅ '写信'按钮--收件人--主题--内容--'发送'按钮流程验证成功")
        print("✅ 成功经验和最新修复已正确应用")
        print("⚡ 多浏览器发送模块发送流程完美运行")
    else:
        print("\n⚠️ 多浏览器发送流程验证失败")
        print("💡 需要进一步检查发送流程配置")

if __name__ == "__main__":
    main()
