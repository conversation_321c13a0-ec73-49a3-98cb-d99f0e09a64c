#!/usr/bin/env python3
"""
发送记录查看器组件
提供完整的发送记录查看和管理功能
"""

import sys
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.models.send_record import SendRecord, SendRecordManager, SendStatus
from src.models.database import DatabaseManager
from src.utils.logger import setup_logger

logger = setup_logger("INFO")

class SendRecordWidget(QWidget):
    """发送记录查看器"""
    
    def __init__(self, db_path: str, parent=None):
        super().__init__(parent)
        
        # 初始化数据库管理器
        try:
            self.db_manager = DatabaseManager(db_path)
            self.record_manager = SendRecordManager(self.db_manager)
        except Exception as e:
            logger.error(f"初始化数据库失败: {e}")
            self.record_manager = None
        
        self.records = []
        self.filtered_records = []
        
        self.init_ui()
        self.load_records()
        
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题和工具栏
        self.create_header(layout)
        
        # 筛选器
        self.create_filters(layout)
        
        # 记录表格
        self.create_record_table(layout)
        
        # 底部状态栏
        self.create_status_bar(layout)
        
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                gridline-color: #e9ecef;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
    
    def create_header(self, layout):
        """创建标题和工具栏"""
        header_layout = QHBoxLayout()
        
        # 标题
        title_label = QLabel("📋 发送记录")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin: 10px 0;
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # 工具按钮
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.load_records)
        header_layout.addWidget(self.refresh_btn)
        
        self.export_btn = QPushButton("📤 导出")
        self.export_btn.clicked.connect(self.export_records)
        header_layout.addWidget(self.export_btn)
        
        self.clear_btn = QPushButton("🗑️ 清理")
        self.clear_btn.clicked.connect(self.clear_old_records)
        header_layout.addWidget(self.clear_btn)
        
        layout.addLayout(header_layout)
    
    def create_filters(self, layout):
        """创建筛选器"""
        filter_group = QGroupBox("筛选条件")
        filter_layout = QGridLayout(filter_group)
        
        # 状态筛选
        filter_layout.addWidget(QLabel("状态:"), 0, 0)
        self.status_filter = QComboBox()
        self.status_filter.addItems(["全部", "发送中", "成功", "失败"])
        self.status_filter.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.status_filter, 0, 1)
        
        # 日期筛选
        filter_layout.addWidget(QLabel("日期范围:"), 0, 2)
        self.date_filter = QComboBox()
        self.date_filter.addItems(["全部", "今天", "昨天", "最近7天", "最近30天", "自定义"])
        self.date_filter.currentTextChanged.connect(self.on_date_filter_changed)
        filter_layout.addWidget(self.date_filter, 0, 3)
        
        # 自定义日期范围
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-7))
        self.start_date_edit.setVisible(False)
        filter_layout.addWidget(self.start_date_edit, 0, 4)
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setVisible(False)
        filter_layout.addWidget(self.end_date_edit, 0, 5)
        
        # 邮箱筛选
        filter_layout.addWidget(QLabel("收件人:"), 1, 0)
        self.email_filter = QLineEdit()
        self.email_filter.setPlaceholderText("输入邮箱地址筛选")
        self.email_filter.textChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.email_filter, 1, 1, 1, 2)
        
        # 应用筛选按钮
        self.apply_filter_btn = QPushButton("应用筛选")
        self.apply_filter_btn.clicked.connect(self.apply_filters)
        filter_layout.addWidget(self.apply_filter_btn, 1, 3)
        
        layout.addWidget(filter_group)
    
    def create_record_table(self, layout):
        """创建记录表格"""
        self.record_table = QTableWidget()
        self.record_table.setColumnCount(9)
        self.record_table.setHorizontalHeaderLabels([
            "ID", "发送邮箱", "收件人", "主题", "状态", "发送时间", "响应时间", "错误信息", "操作"
        ])
        
        # 设置列宽
        header = self.record_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 发送邮箱
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # 收件人
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # 主题
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 状态
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 发送时间
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # 响应时间
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # 错误信息
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # 操作
        
        # 设置表格属性
        self.record_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.record_table.setAlternatingRowColors(True)
        self.record_table.setSortingEnabled(True)
        
        # 连接信号
        self.record_table.cellDoubleClicked.connect(self.on_cell_double_clicked)
        
        layout.addWidget(self.record_table)
    
    def create_status_bar(self, layout):
        """创建状态栏"""
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.record_count_label = QLabel("记录数: 0")
        status_layout.addWidget(self.record_count_label)
        
        layout.addLayout(status_layout)
    
    def load_records(self):
        """加载记录"""
        if not self.record_manager:
            self.status_label.setText("数据库未连接")
            return
        
        try:
            self.status_label.setText("正在加载记录...")
            
            # 获取最近30天的记录
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            self.records = self.record_manager.get_records_by_date_range(start_date, end_date)
            self.filtered_records = self.records.copy()
            
            self.update_record_table()
            self.status_label.setText("记录加载完成")
            
        except Exception as e:
            logger.error(f"加载记录失败: {e}")
            self.status_label.setText(f"加载失败: {e}")
    
    def update_record_table(self):
        """更新记录表格"""
        self.record_table.setRowCount(len(self.filtered_records))
        
        for row, record in enumerate(self.filtered_records):
            # ID
            self.record_table.setItem(row, 0, QTableWidgetItem(str(record.id)))
            
            # 发送邮箱
            self.record_table.setItem(row, 1, QTableWidgetItem(record.from_email))
            
            # 收件人
            self.record_table.setItem(row, 2, QTableWidgetItem(record.to_email))
            
            # 主题
            subject_item = QTableWidgetItem(record.subject[:30] + "..." if len(record.subject) > 30 else record.subject)
            subject_item.setToolTip(record.subject)
            self.record_table.setItem(row, 3, subject_item)
            
            # 状态
            status_item = QTableWidgetItem(self.get_status_text(record.status))
            status_item.setBackground(self.get_status_color(record.status))
            self.record_table.setItem(row, 4, status_item)
            
            # 发送时间
            send_time = record.send_time.strftime("%m-%d %H:%M:%S") if record.send_time else ""
            self.record_table.setItem(row, 5, QTableWidgetItem(send_time))
            
            # 响应时间
            response_time = f"{record.response_time:.2f}s" if record.response_time else ""
            self.record_table.setItem(row, 6, QTableWidgetItem(response_time))
            
            # 错误信息
            error_msg = record.error_msg or ""
            error_item = QTableWidgetItem(error_msg[:20] + "..." if len(error_msg) > 20 else error_msg)
            if error_msg:
                error_item.setToolTip(error_msg)
            self.record_table.setItem(row, 7, error_item)
            
            # 操作按钮
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(4, 4, 4, 4)
            
            view_btn = QPushButton("查看")
            view_btn.setMaximumSize(50, 25)
            view_btn.clicked.connect(lambda checked, r=record: self.view_record_detail(r))
            action_layout.addWidget(view_btn)
            
            self.record_table.setCellWidget(row, 8, action_widget)
        
        # 更新记录数量
        self.record_count_label.setText(f"记录数: {len(self.filtered_records)}")
    
    def get_status_text(self, status: SendStatus) -> str:
        """获取状态文本"""
        status_map = {
            SendStatus.SENDING: "🔄 发送中",
            SendStatus.SUCCESS: "✅ 成功",
            SendStatus.FAILED: "❌ 失败"
        }
        return status_map.get(status, "未知")
    
    def get_status_color(self, status: SendStatus) -> QColor:
        """获取状态颜色"""
        color_map = {
            SendStatus.SENDING: QColor("#cce5ff"),   # 蓝色
            SendStatus.SUCCESS: QColor("#d4edda"),   # 绿色
            SendStatus.FAILED: QColor("#f8d7da")     # 红色
        }
        return color_map.get(status, QColor("#ffffff"))
    
    def on_date_filter_changed(self):
        """日期筛选改变"""
        filter_text = self.date_filter.currentText()
        
        if filter_text == "自定义":
            self.start_date_edit.setVisible(True)
            self.end_date_edit.setVisible(True)
        else:
            self.start_date_edit.setVisible(False)
            self.end_date_edit.setVisible(False)
        
        self.apply_filters()
    
    def apply_filters(self):
        """应用筛选"""
        if not self.records:
            return
        
        self.filtered_records = []
        
        # 获取筛选条件
        status_filter = self.status_filter.currentText()
        date_filter = self.date_filter.currentText()
        email_filter = self.email_filter.text().strip().lower()
        
        # 计算日期范围
        end_date = datetime.now()
        if date_filter == "今天":
            start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
        elif date_filter == "昨天":
            start_date = (end_date - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
        elif date_filter == "最近7天":
            start_date = end_date - timedelta(days=7)
        elif date_filter == "最近30天":
            start_date = end_date - timedelta(days=30)
        elif date_filter == "自定义":
            start_date = datetime.combine(self.start_date_edit.date().toPyDate(), datetime.min.time())
            end_date = datetime.combine(self.end_date_edit.date().toPyDate(), datetime.max.time())
        else:  # 全部
            start_date = None
            end_date = None
        
        # 应用筛选
        for record in self.records:
            # 状态筛选
            if status_filter != "全部":
                status_map = {
                    "发送中": SendStatus.SENDING,
                    "成功": SendStatus.SUCCESS,
                    "失败": SendStatus.FAILED
                }
                if record.status != status_map.get(status_filter):
                    continue
            
            # 日期筛选
            if start_date and end_date:
                record_time = record.send_time or record.create_time
                if not (start_date <= record_time <= end_date):
                    continue
            
            # 邮箱筛选
            if email_filter:
                if email_filter not in record.to_email.lower():
                    continue
            
            self.filtered_records.append(record)
        
        self.update_record_table()
    
    def on_cell_double_clicked(self, row: int, column: int):
        """单元格双击"""
        if row < len(self.filtered_records):
            record = self.filtered_records[row]
            self.view_record_detail(record)
    
    def view_record_detail(self, record: SendRecord):
        """查看记录详情"""
        dialog = RecordDetailDialog(record, self)
        dialog.exec_()
    
    def export_records(self):
        """导出记录"""
        if not self.filtered_records:
            QMessageBox.information(self, "提示", "没有记录可导出")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出记录", f"send_records_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            "CSV Files (*.csv)"
        )
        
        if file_path:
            try:
                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    
                    # 写入标题
                    writer.writerow([
                        "ID", "发送邮箱", "收件人", "主题", "状态", "发送时间", "响应时间", "错误信息"
                    ])
                    
                    # 写入数据
                    for record in self.filtered_records:
                        writer.writerow([
                            record.id,
                            record.from_email,
                            record.to_email,
                            record.subject,
                            record.status.value,
                            record.send_time.strftime("%Y-%m-%d %H:%M:%S") if record.send_time else "",
                            f"{record.response_time:.2f}" if record.response_time else "",
                            record.error_msg or ""
                        ])
                
                QMessageBox.information(self, "成功", f"记录已导出到: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {e}")
    
    def clear_old_records(self):
        """清理旧记录"""
        reply = QMessageBox.question(
            self, "确认", "确定要清理30天前的记录吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 这里可以实现清理逻辑
                QMessageBox.information(self, "提示", "清理功能开发中...")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"清理失败: {e}")

class RecordDetailDialog(QDialog):
    """记录详情对话框"""
    
    def __init__(self, record: SendRecord, parent=None):
        super().__init__(parent)
        self.record = record
        self.setWindowTitle(f"记录详情 - {record.id}")
        self.setModal(True)
        self.resize(600, 500)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 创建表单
        form_layout = QFormLayout()
        
        # 基本信息
        form_layout.addRow("记录ID:", QLabel(str(self.record.id)))
        form_layout.addRow("任务ID:", QLabel(self.record.task_id))
        form_layout.addRow("发送邮箱:", QLabel(self.record.from_email))
        form_layout.addRow("收件人:", QLabel(self.record.to_email))
        
        # 主题
        subject_edit = QTextEdit()
        subject_edit.setPlainText(self.record.subject)
        subject_edit.setMaximumHeight(60)
        subject_edit.setReadOnly(True)
        form_layout.addRow("主题:", subject_edit)
        
        # 内容
        content_edit = QTextEdit()
        content_edit.setPlainText(self.record.content)
        content_edit.setReadOnly(True)
        form_layout.addRow("内容:", content_edit)
        
        # 状态信息
        form_layout.addRow("状态:", QLabel(self.record.status.value))
        form_layout.addRow("内容类型:", QLabel(self.record.content_type))
        
        # 时间信息
        form_layout.addRow("创建时间:", QLabel(self.record.create_time.strftime("%Y-%m-%d %H:%M:%S")))
        
        if self.record.send_time:
            form_layout.addRow("发送时间:", QLabel(self.record.send_time.strftime("%Y-%m-%d %H:%M:%S")))
        
        if self.record.response_time:
            form_layout.addRow("响应时间:", QLabel(f"{self.record.response_time:.2f} 秒"))
        
        # 技术信息
        if self.record.browser_id:
            form_layout.addRow("浏览器ID:", QLabel(self.record.browser_id))
        
        if self.record.proxy_ip:
            form_layout.addRow("代理IP:", QLabel(self.record.proxy_ip))
        
        # 错误信息
        if self.record.error_msg:
            error_edit = QTextEdit()
            error_edit.setPlainText(self.record.error_msg)
            error_edit.setMaximumHeight(80)
            error_edit.setReadOnly(True)
            form_layout.addRow("错误信息:", error_edit)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
