#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能任务队列系统
支持大数据量分批处理、智能分配、优先级管理、负载均衡等功能
"""

import time
import threading
from typing import List, Dict, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from queue import PriorityQueue, Queue
from loguru import logger
import sqlite3
import json


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"           # 待处理
    QUEUED = "queued"            # 已入队
    PROCESSING = "processing"     # 处理中
    COMPLETED = "completed"       # 已完成
    FAILED = "failed"            # 失败
    PAUSED = "paused"            # 暂停
    CANCELLED = "cancelled"       # 取消


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


class BatchStatus(Enum):
    """批次状态枚举"""
    WAITING = "waiting"          # 等待分配
    ALLOCATED = "allocated"      # 已分配
    PROCESSING = "processing"    # 处理中
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"           # 失败


@dataclass
class EmailTask:
    """邮件任务数据类"""
    task_id: str
    batch_id: str
    to_email: str
    subject: str
    content: str
    content_type: str = "text/plain"
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    created_time: datetime = field(default_factory=datetime.now)
    scheduled_time: Optional[datetime] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    error_message: Optional[str] = None
    assigned_worker: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TaskBatch:
    """任务批次数据类"""
    batch_id: str
    name: str
    total_tasks: int
    allocated_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    status: BatchStatus = BatchStatus.WAITING
    created_time: datetime = field(default_factory=datetime.now)
    priority: TaskPriority = TaskPriority.NORMAL
    batch_size: int = 1000  # 每次分配的任务数量
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class QueueConfig:
    """队列配置"""
    max_queue_size: int = 10000          # 最大队列大小
    batch_allocation_size: int = 1000     # 每次分配的批次大小
    auto_allocation: bool = True          # 自动分配
    allocation_threshold: int = 100       # 分配阈值
    max_concurrent_tasks: int = 10        # 最大并发任务数
    task_timeout: int = 300              # 任务超时时间(秒)
    retry_delay: int = 60                # 重试延迟(秒)
    enable_priority: bool = True          # 启用优先级
    enable_load_balance: bool = True      # 启用负载均衡


class SmartTaskQueue:
    """
    智能任务队列系统
    
    功能特性：
    1. 大数据量分批处理
    2. 智能任务分配
    3. 优先级管理
    4. 负载均衡
    5. 自动重试
    6. 状态监控
    """
    
    def __init__(self, config: QueueConfig, db_path: str = "data/task_queue.db"):
        """
        初始化智能任务队列
        
        Args:
            config: 队列配置
            db_path: 数据库路径
        """
        self.config = config
        self.db_path = db_path
        
        # 队列管理
        self.active_queue = PriorityQueue(maxsize=config.max_queue_size)
        self.processing_tasks: Dict[str, EmailTask] = {}
        self.completed_tasks: List[EmailTask] = []
        self.failed_tasks: List[EmailTask] = []
        
        # 批次管理
        self.batches: Dict[str, TaskBatch] = {}
        self.pending_batches: List[str] = []
        
        # 工作线程管理
        self.workers: Dict[str, Dict[str, Any]] = {}
        self.worker_load: Dict[str, int] = {}
        
        # 控制标志
        self.is_running = False
        self.is_paused = False
        self.allocation_thread: Optional[threading.Thread] = None
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'queued_tasks': 0,
            'processing_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'total_batches': 0,
            'active_batches': 0,
            'throughput': 0.0,  # 每分钟处理任务数
            'avg_processing_time': 0.0,
            'success_rate': 0.0
        }
        
        # 回调函数
        self.on_task_allocated: Optional[Callable] = None
        self.on_task_completed: Optional[Callable] = None
        self.on_task_failed: Optional[Callable] = None
        self.on_batch_completed: Optional[Callable] = None
        self.on_stats_updated: Optional[Callable] = None
        
        # 初始化数据库
        self._init_database()
        
        logger.info("🚀 智能任务队列系统初始化完成")
    
    def _init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建任务表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS tasks (
                        task_id TEXT PRIMARY KEY,
                        batch_id TEXT,
                        to_email TEXT,
                        subject TEXT,
                        content TEXT,
                        content_type TEXT,
                        priority INTEGER,
                        status TEXT,
                        created_time TEXT,
                        scheduled_time TEXT,
                        start_time TEXT,
                        end_time TEXT,
                        retry_count INTEGER,
                        max_retries INTEGER,
                        error_message TEXT,
                        assigned_worker TEXT,
                        metadata TEXT
                    )
                """)
                
                # 创建批次表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS batches (
                        batch_id TEXT PRIMARY KEY,
                        name TEXT,
                        total_tasks INTEGER,
                        allocated_tasks INTEGER,
                        completed_tasks INTEGER,
                        failed_tasks INTEGER,
                        status TEXT,
                        created_time TEXT,
                        priority INTEGER,
                        batch_size INTEGER,
                        metadata TEXT
                    )
                """)
                
                # 创建索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_task_status ON tasks(status)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_task_batch ON tasks(batch_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_task_priority ON tasks(priority)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_batch_status ON batches(status)")
                
                conn.commit()
                logger.info("✅ 任务队列数据库初始化完成")
                
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")
            raise
    
    def create_batch(self, name: str, total_tasks: int, priority: TaskPriority = TaskPriority.NORMAL,
                    batch_size: int = None) -> str:
        """
        创建任务批次
        
        Args:
            name: 批次名称
            total_tasks: 总任务数
            priority: 优先级
            batch_size: 批次大小
            
        Returns:
            批次ID
        """
        batch_id = f"batch_{int(time.time() * 1000)}"
        
        batch = TaskBatch(
            batch_id=batch_id,
            name=name,
            total_tasks=total_tasks,
            priority=priority,
            batch_size=batch_size or self.config.batch_allocation_size
        )
        
        self.batches[batch_id] = batch
        self.pending_batches.append(batch_id)
        self.stats['total_batches'] += 1
        
        # 保存到数据库
        self._save_batch_to_db(batch)
        
        logger.info(f"📦 创建任务批次: {batch_id}, 名称: {name}, 总任务数: {total_tasks}")
        return batch_id
    
    def add_tasks_to_batch(self, batch_id: str, tasks_data: List[Dict[str, Any]]) -> List[str]:
        """
        向批次添加任务
        
        Args:
            batch_id: 批次ID
            tasks_data: 任务数据列表
            
        Returns:
            任务ID列表
        """
        if batch_id not in self.batches:
            raise ValueError(f"批次不存在: {batch_id}")
        
        batch = self.batches[batch_id]
        task_ids = []
        
        for i, task_data in enumerate(tasks_data):
            task_id = f"{batch_id}_task_{i}_{int(time.time() * 1000)}"
            
            task = EmailTask(
                task_id=task_id,
                batch_id=batch_id,
                to_email=task_data['to_email'],
                subject=task_data['subject'],
                content=task_data['content'],
                content_type=task_data.get('content_type', 'text/plain'),
                priority=batch.priority,
                metadata=task_data.get('metadata', {})
            )
            
            # 保存到数据库
            self._save_task_to_db(task)
            task_ids.append(task_id)
        
        self.stats['total_tasks'] += len(tasks_data)
        
        logger.info(f"📝 向批次 {batch_id} 添加 {len(tasks_data)} 个任务")
        return task_ids
    
    def start_queue(self):
        """启动队列系统"""
        if self.is_running:
            logger.warning("⚠️ 队列系统已在运行中")
            return
        
        self.is_running = True
        self.is_paused = False
        
        # 启动自动分配线程
        if self.config.auto_allocation:
            self.allocation_thread = threading.Thread(target=self._allocation_worker, daemon=True)
            self.allocation_thread.start()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
        self.monitor_thread.start()
        
        logger.info("🚀 智能任务队列系统已启动")
    
    def stop_queue(self):
        """停止队列系统"""
        self.is_running = False
        
        if self.allocation_thread and self.allocation_thread.is_alive():
            self.allocation_thread.join(timeout=5)
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        logger.info("🛑 智能任务队列系统已停止")
    
    def pause_queue(self):
        """暂停队列"""
        self.is_paused = True
        logger.info("⏸️ 任务队列已暂停")
    
    def resume_queue(self):
        """恢复队列"""
        self.is_paused = False
        logger.info("▶️ 任务队列已恢复")
    
    def allocate_tasks(self, count: int = None) -> List[EmailTask]:
        """
        手动分配任务
        
        Args:
            count: 分配数量，None表示使用默认配置
            
        Returns:
            分配的任务列表
        """
        if not count:
            count = self.config.batch_allocation_size
        
        allocated_tasks = []
        
        # 从待处理批次中分配任务
        for batch_id in self.pending_batches[:]:
            if len(allocated_tasks) >= count:
                break
            
            batch = self.batches[batch_id]
            if batch.status != BatchStatus.WAITING:
                continue
            
            # 从数据库加载待分配的任务
            remaining_count = count - len(allocated_tasks)
            tasks = self._load_pending_tasks_from_batch(batch_id, remaining_count)
            
            for task in tasks:
                task.status = TaskStatus.QUEUED
                self._update_task_in_db(task)
                
                # 添加到活动队列
                priority_value = -task.priority.value  # 负值用于优先队列
                self.active_queue.put((priority_value, task.created_time.timestamp(), task))
                allocated_tasks.append(task)
            
            # 更新批次状态
            batch.allocated_tasks += len(tasks)
            if batch.allocated_tasks >= batch.total_tasks:
                batch.status = BatchStatus.ALLOCATED
                self.pending_batches.remove(batch_id)
            
            self._update_batch_in_db(batch)
        
        self.stats['queued_tasks'] += len(allocated_tasks)
        
        if allocated_tasks:
            logger.info(f"📋 分配了 {len(allocated_tasks)} 个任务到队列")
            
            if self.on_task_allocated:
                for task in allocated_tasks:
                    self.on_task_allocated(task)
        
        return allocated_tasks
    
    def get_next_task(self, worker_id: str) -> Optional[EmailTask]:
        """
        获取下一个任务
        
        Args:
            worker_id: 工作线程ID
            
        Returns:
            下一个任务或None
        """
        if self.is_paused or self.active_queue.empty():
            return None
        
        try:
            priority, timestamp, task = self.active_queue.get_nowait()
            
            # 更新任务状态
            task.status = TaskStatus.PROCESSING
            task.start_time = datetime.now()
            task.assigned_worker = worker_id
            
            # 移动到处理中任务
            self.processing_tasks[task.task_id] = task
            
            # 更新工作线程负载
            if worker_id not in self.worker_load:
                self.worker_load[worker_id] = 0
            self.worker_load[worker_id] += 1
            
            # 更新统计
            self.stats['queued_tasks'] -= 1
            self.stats['processing_tasks'] += 1
            
            # 保存到数据库
            self._update_task_in_db(task)
            
            logger.debug(f"📤 分配任务 {task.task_id} 给工作线程 {worker_id}")
            return task
            
        except:
            return None
    
    def complete_task(self, task_id: str, success: bool = True, error_message: str = None):
        """
        完成任务
        
        Args:
            task_id: 任务ID
            success: 是否成功
            error_message: 错误信息
        """
        if task_id not in self.processing_tasks:
            logger.warning(f"⚠️ 任务不在处理中: {task_id}")
            return
        
        task = self.processing_tasks.pop(task_id)
        task.end_time = datetime.now()
        
        # 更新工作线程负载
        if task.assigned_worker and task.assigned_worker in self.worker_load:
            self.worker_load[task.assigned_worker] = max(0, self.worker_load[task.assigned_worker] - 1)
        
        if success:
            task.status = TaskStatus.COMPLETED
            self.completed_tasks.append(task)
            self.stats['completed_tasks'] += 1
            
            # 更新批次完成数
            if task.batch_id in self.batches:
                self.batches[task.batch_id].completed_tasks += 1
                self._update_batch_in_db(self.batches[task.batch_id])
            
            if self.on_task_completed:
                self.on_task_completed(task)
            
            logger.debug(f"✅ 任务完成: {task_id}")
        else:
            task.status = TaskStatus.FAILED
            task.error_message = error_message
            task.retry_count += 1
            
            # 检查是否需要重试
            if task.retry_count < task.max_retries:
                # 重新入队
                task.status = TaskStatus.PENDING
                task.assigned_worker = None
                task.start_time = None
                
                # 延迟重试
                retry_time = datetime.now() + timedelta(seconds=self.config.retry_delay)
                task.scheduled_time = retry_time
                
                priority_value = -task.priority.value
                self.active_queue.put((priority_value, retry_time.timestamp(), task))
                
                logger.info(f"🔄 任务重试: {task_id} (第{task.retry_count}次)")
            else:
                # 最终失败
                self.failed_tasks.append(task)
                self.stats['failed_tasks'] += 1
                
                # 更新批次失败数
                if task.batch_id in self.batches:
                    self.batches[task.batch_id].failed_tasks += 1
                    self._update_batch_in_db(self.batches[task.batch_id])
                
                if self.on_task_failed:
                    self.on_task_failed(task)
                
                logger.error(f"❌ 任务最终失败: {task_id}")
        
        self.stats['processing_tasks'] -= 1
        self._update_task_in_db(task)
        self._update_stats()
    
    def _allocation_worker(self):
        """自动分配工作线程"""
        logger.info("🔄 自动分配线程启动")
        
        while self.is_running:
            try:
                if not self.is_paused and self.active_queue.qsize() < self.config.allocation_threshold:
                    self.allocate_tasks()
                
                time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 自动分配异常: {e}")
                time.sleep(10)
        
        logger.info("🔄 自动分配线程结束")
    
    def _monitor_worker(self):
        """监控工作线程"""
        logger.info("📊 监控线程启动")
        
        while self.is_running:
            try:
                self._update_stats()
                self._check_timeouts()
                self._check_batch_completion()
                
                if self.on_stats_updated:
                    self.on_stats_updated(self.get_stats())
                
                time.sleep(10)  # 每10秒更新一次
                
            except Exception as e:
                logger.error(f"❌ 监控异常: {e}")
                time.sleep(30)
        
        logger.info("📊 监控线程结束")

    def _check_timeouts(self):
        """检查任务超时"""
        current_time = datetime.now()
        timeout_tasks = []

        for task_id, task in self.processing_tasks.items():
            if task.start_time:
                elapsed = (current_time - task.start_time).total_seconds()
                if elapsed > self.config.task_timeout:
                    timeout_tasks.append(task_id)

        for task_id in timeout_tasks:
            logger.warning(f"⏰ 任务超时: {task_id}")
            self.complete_task(task_id, False, "任务超时")

    def _check_batch_completion(self):
        """检查批次完成状态"""
        for batch_id, batch in self.batches.items():
            if batch.status == BatchStatus.ALLOCATED:
                total_processed = batch.completed_tasks + batch.failed_tasks
                if total_processed >= batch.allocated_tasks:
                    batch.status = BatchStatus.COMPLETED
                    self._update_batch_in_db(batch)

                    if self.on_batch_completed:
                        self.on_batch_completed(batch)

                    logger.info(f"🎉 批次完成: {batch_id}")

    def _update_stats(self):
        """更新统计信息"""
        # 计算成功率
        total_processed = self.stats['completed_tasks'] + self.stats['failed_tasks']
        if total_processed > 0:
            self.stats['success_rate'] = (self.stats['completed_tasks'] / total_processed) * 100

        # 计算吞吐量（每分钟处理任务数）
        # 这里简化计算，实际应该基于时间窗口
        self.stats['throughput'] = self.stats['completed_tasks']  # 简化版本

        # 更新队列状态
        self.stats['queued_tasks'] = self.active_queue.qsize()
        self.stats['processing_tasks'] = len(self.processing_tasks)
        self.stats['active_batches'] = len([b for b in self.batches.values()
                                          if b.status in [BatchStatus.ALLOCATED, BatchStatus.PROCESSING]])

    def _save_task_to_db(self, task: EmailTask):
        """保存任务到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO tasks VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    task.task_id, task.batch_id, task.to_email, task.subject, task.content,
                    task.content_type, task.priority.value, task.status.value,
                    task.created_time.isoformat(),
                    task.scheduled_time.isoformat() if task.scheduled_time else None,
                    task.start_time.isoformat() if task.start_time else None,
                    task.end_time.isoformat() if task.end_time else None,
                    task.retry_count, task.max_retries, task.error_message,
                    task.assigned_worker, json.dumps(task.metadata)
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"❌ 保存任务到数据库失败: {e}")

    def _update_task_in_db(self, task: EmailTask):
        """更新数据库中的任务"""
        self._save_task_to_db(task)

    def _save_batch_to_db(self, batch: TaskBatch):
        """保存批次到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO batches VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    batch.batch_id, batch.name, batch.total_tasks,
                    batch.allocated_tasks, batch.completed_tasks, batch.failed_tasks,
                    batch.status.value, batch.created_time.isoformat(),
                    batch.priority.value, batch.batch_size, json.dumps(batch.metadata)
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"❌ 保存批次到数据库失败: {e}")

    def _update_batch_in_db(self, batch: TaskBatch):
        """更新数据库中的批次"""
        self._save_batch_to_db(batch)

    def _load_pending_tasks_from_batch(self, batch_id: str, limit: int) -> List[EmailTask]:
        """从数据库加载批次中的待处理任务"""
        tasks = []
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM tasks
                    WHERE batch_id = ? AND status = ?
                    ORDER BY priority DESC, created_time ASC
                    LIMIT ?
                """, (batch_id, TaskStatus.PENDING.value, limit))

                rows = cursor.fetchall()
                for row in rows:
                    task = self._row_to_task(row)
                    tasks.append(task)
        except Exception as e:
            logger.error(f"❌ 从数据库加载任务失败: {e}")

        return tasks

    def _row_to_task(self, row) -> EmailTask:
        """将数据库行转换为任务对象"""
        return EmailTask(
            task_id=row[0],
            batch_id=row[1],
            to_email=row[2],
            subject=row[3],
            content=row[4],
            content_type=row[5],
            priority=TaskPriority(row[6]),
            status=TaskStatus(row[7]),
            created_time=datetime.fromisoformat(row[8]),
            scheduled_time=datetime.fromisoformat(row[9]) if row[9] else None,
            start_time=datetime.fromisoformat(row[10]) if row[10] else None,
            end_time=datetime.fromisoformat(row[11]) if row[11] else None,
            retry_count=row[12],
            max_retries=row[13],
            error_message=row[14],
            assigned_worker=row[15],
            metadata=json.loads(row[16]) if row[16] else {}
        )

    def get_stats(self) -> Dict[str, Any]:
        """获取详细统计信息"""
        return {
            **self.stats,
            'queue_size': self.active_queue.qsize(),
            'max_queue_size': self.config.max_queue_size,
            'worker_count': len(self.workers),
            'worker_load': dict(self.worker_load),
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'config': {
                'batch_allocation_size': self.config.batch_allocation_size,
                'max_concurrent_tasks': self.config.max_concurrent_tasks,
                'auto_allocation': self.config.auto_allocation
            }
        }

    def get_batch_info(self, batch_id: str) -> Optional[Dict[str, Any]]:
        """获取批次信息"""
        if batch_id not in self.batches:
            return None

        batch = self.batches[batch_id]
        progress = 0.0
        if batch.total_tasks > 0:
            progress = ((batch.completed_tasks + batch.failed_tasks) / batch.total_tasks) * 100

        return {
            'batch_id': batch.batch_id,
            'name': batch.name,
            'status': batch.status.value,
            'total_tasks': batch.total_tasks,
            'allocated_tasks': batch.allocated_tasks,
            'completed_tasks': batch.completed_tasks,
            'failed_tasks': batch.failed_tasks,
            'pending_tasks': batch.total_tasks - batch.allocated_tasks,
            'progress': round(progress, 2),
            'success_rate': round((batch.completed_tasks / max(1, batch.completed_tasks + batch.failed_tasks)) * 100, 2),
            'created_time': batch.created_time.isoformat(),
            'priority': batch.priority.value,
            'batch_size': batch.batch_size
        }

    def get_all_batches_info(self) -> List[Dict[str, Any]]:
        """获取所有批次信息"""
        return [self.get_batch_info(batch_id) for batch_id in self.batches.keys()]

    def cancel_batch(self, batch_id: str) -> bool:
        """取消批次"""
        if batch_id not in self.batches:
            return False

        batch = self.batches[batch_id]
        batch.status = BatchStatus.FAILED

        # 取消所有相关任务
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE tasks SET status = ? WHERE batch_id = ? AND status IN (?, ?)
                """, (TaskStatus.CANCELLED.value, batch_id, TaskStatus.PENDING.value, TaskStatus.QUEUED.value))
                conn.commit()
        except Exception as e:
            logger.error(f"❌ 取消批次任务失败: {e}")

        self._update_batch_in_db(batch)
        logger.info(f"🚫 批次已取消: {batch_id}")
        return True

    def pause_batch(self, batch_id: str) -> bool:
        """暂停批次"""
        if batch_id not in self.batches:
            return False

        # 暂停相关任务的分配
        # 这里可以实现更复杂的暂停逻辑
        logger.info(f"⏸️ 批次已暂停: {batch_id}")
        return True

    def resume_batch(self, batch_id: str) -> bool:
        """恢复批次"""
        if batch_id not in self.batches:
            return False

        # 恢复相关任务的分配
        logger.info(f"▶️ 批次已恢复: {batch_id}")
        return True

    def clear_completed_tasks(self):
        """清理已完成的任务"""
        before_count = len(self.completed_tasks)
        self.completed_tasks.clear()

        # 从数据库删除已完成的任务（可选）
        # 这里保留数据库记录用于历史查询

        logger.info(f"🧹 清理了 {before_count} 个已完成任务")

    def export_batch_report(self, batch_id: str) -> Dict[str, Any]:
        """导出批次报告"""
        if batch_id not in self.batches:
            return {}

        batch_info = self.get_batch_info(batch_id)

        # 获取任务详情
        task_details = []
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM tasks WHERE batch_id = ?", (batch_id,))
                rows = cursor.fetchall()

                for row in rows:
                    task = self._row_to_task(row)
                    task_details.append({
                        'task_id': task.task_id,
                        'to_email': task.to_email,
                        'status': task.status.value,
                        'created_time': task.created_time.isoformat(),
                        'start_time': task.start_time.isoformat() if task.start_time else None,
                        'end_time': task.end_time.isoformat() if task.end_time else None,
                        'retry_count': task.retry_count,
                        'error_message': task.error_message
                    })
        except Exception as e:
            logger.error(f"❌ 导出批次报告失败: {e}")

        return {
            'batch_info': batch_info,
            'task_details': task_details,
            'export_time': datetime.now().isoformat()
        }
