#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终一致性检查
确保5步骤与测试成功的细节逻辑完全一样，剔除掉不要的
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_exact_consistency():
    """检查与测试成功逻辑的完全一致性"""
    print("🔍 最终一致性检查")
    print("目标：与测试成功的细节逻辑完全一样，剔除掉不要的")
    print("=" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        # 获取所有步骤的源码
        step1_source = inspect.getsource(UnifiedEmailSender._step1_click_compose)
        step2_source = inspect.getsource(UnifiedEmailSender._step2_fill_recipient)
        step3_source = inspect.getsource(UnifiedEmailSender._step3_fill_subject)
        step4_source = inspect.getsource(UnifiedEmailSender._step4_fill_content)
        step5_source = inspect.getsource(UnifiedEmailSender._step5_click_send)
        
        consistency_checks = []
        
        print("📋 第1步一致性检查：点击写信按钮")
        step1_checks = [
            ("prepare_compose_page()" in step1_source, "✅ 调用正确方法"),
            ("switch_to" not in step1_source, "✅ 无多余的iframe操作"),
            ("querySelector" not in step1_source, "✅ 无多余的选择器")
        ]
        for check, desc in step1_checks:
            print(f"  {desc if check else '❌ ' + desc[4:]}")
            consistency_checks.append(check)
        
        print("\n📋 第2步一致性检查：填写收件人")
        step2_checks = [
            ("input[type=\"text\"]" in step2_source, "✅ 主要选择器正确"),
            ("querySelectorAll('input[type=\"text\"]')[0]" in step2_source, "✅ 备用选择器正确"),
            ("input[name=\"to\"]" not in step2_source, "✅ 已剔除不需要的选择器"),
            ("placeholder" not in step2_source, "✅ 已剔除placeholder选择器"),
            ("offsetParent !== null" in step2_source, "✅ 包含可见性检查"),
            ("focus()" in step2_source, "✅ 包含focus操作"),
            ("Event('input'" in step2_source, "✅ 包含input事件"),
            ("Event('change'" in step2_source, "✅ 包含change事件"),
            ("Event('blur'" in step2_source, "✅ 包含blur事件")
        ]
        for check, desc in step2_checks:
            print(f"  {desc if check else '❌ ' + desc[4:]}")
            consistency_checks.append(check)
        
        print("\n📋 第3步一致性检查：填写主题")
        step3_checks = [
            ("input[name=\"subj\"][class=\"input inp_base\"]" in step3_source, "✅ 精确选择器正确"),
            ("input[name=\"subj\"]" in step3_source, "✅ 备用选择器正确"),
            ("subject" not in step3_source or "input[name=\"subject\"]" not in step3_source, "✅ 已剔除不需要的选择器"),
            ("title" not in step3_source, "✅ 已剔除title选择器"),
            ("placeholder" not in step3_source, "✅ 已剔除placeholder选择器"),
            ("offsetParent !== null" in step3_source, "✅ 包含可见性检查"),
            ("focus()" in step3_source, "✅ 包含focus操作"),
            ("Event('input'" in step3_source, "✅ 包含input事件"),
            ("Event('change'" in step3_source, "✅ 包含change事件"),
            ("Event('blur'" not in step3_source, "✅ 正确无blur事件（与测试一致）")
        ]
        for check, desc in step3_checks:
            print(f"  {desc if check else '❌ ' + desc[4:]}")
            consistency_checks.append(check)
        
        print("\n📋 第4步一致性检查：填写内容")
        step4_checks = [
            ("iframe[class=\"iframe\"]" in step4_source, "✅ 主要iframe选择器"),
            ("contentDocument || iframe.contentWindow.document" in step4_source, "✅ 使用contentDocument访问"),
            ("switch_to.frame" not in step4_source, "✅ 无Selenium iframe切换"),
            ("div[contenteditable=\"true\"]" in step4_source, "✅ 富文本编辑器选择器"),
            ("textarea[name=\"content\"]" in step4_source, "✅ textarea选择器"),
            ("offsetParent !== null" in step4_source, "✅ 包含可见性检查"),
            ("console.log" in step4_source, "✅ 包含日志输出")
        ]
        for check, desc in step4_checks:
            print(f"  {desc if check else '❌ ' + desc[4:]}")
            consistency_checks.append(check)
        
        print("\n📋 第5步一致性检查：点击发送")
        step5_checks = [
            ("input[type=\"submit\"][value=\"发送\"]" in step5_source, "✅ 主要发送按钮选择器"),
            ("input[value=\"发送\"]" in step5_source, "✅ 发送值选择器"),
            ("button[type=\"submit\"]" in step5_source, "✅ button submit选择器"),
            ("offsetParent !== null" in step5_source, "✅ 包含可见性检查"),
            ("focus()" in step5_source, "✅ 包含focus操作"),
            ("click()" in step5_source, "✅ 包含click操作"),
            ("console.log" in step5_source, "✅ 包含日志输出"),
            ("您的邮件已发送" in step5_source, "✅ 成功检查文本"),
            ("time.sleep(0.3)" in step5_source, "✅ 等待检查时间")
        ]
        for check, desc in step5_checks:
            print(f"  {desc if check else '❌ ' + desc[4:]}")
            consistency_checks.append(check)
        
        return consistency_checks
        
    except Exception as e:
        print(f"❌ 一致性检查失败: {e}")
        return [False]

def main():
    """主函数"""
    print("🎯 最终一致性检查")
    print("要求：与测试成功的细节逻辑完全一样，剔除掉不要的")
    
    # 执行一致性检查
    consistency_results = check_exact_consistency()
    
    # 结果汇总
    print("\n" + "=" * 60)
    print("📊 最终一致性检查结果:")
    print("=" * 60)
    
    passed = sum(consistency_results)
    total = len(consistency_results)
    success_rate = (passed / total) * 100 if total > 0 else 0
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    print(f"📈 一致性: {success_rate:.1f}%")
    
    if success_rate >= 95:
        print("\n🎉 最终一致性检查完全通过！")
        print("✅ 与测试成功逻辑完全一致")
        print("✅ 已剔除所有不需要的选择器和操作")
        print("✅ 保留了所有测试成功的关键要素")
        print("✅ JavaScript操作方式完全一致")
        print("✅ 可见性检查逻辑完全一致")
        print("✅ 事件触发方式完全一致")
        
        print("\n🎯 一致性特点:")
        print("  🔧 选择器：只保留测试成功的选择器")
        print("  ⚡ 操作：JavaScript操作方式完全一致")
        print("  🛡️ 检查：可见性检查逻辑完全一致")
        print("  📝 日志：console.log输出完全一致")
        print("  🎯 纯粹：剔除了所有不需要的复杂逻辑")
        
    elif success_rate >= 90:
        print("\n✅ 一致性检查基本通过，但还有细节需要调整")
    else:
        print("\n❌ 一致性检查未通过，需要进一步调整")
    
    print("\n📈 最终成果:")
    print("🎯 第1步：点击写信按钮 - 调用prepare_compose_page()")
    print("🎯 第2步：填写收件人 - input[type='text'] + 可见性检查")
    print("🎯 第3步：填写主题 - input[name='subj'] + 可见性检查")
    print("🎯 第4步：填写内容 - iframe/contenteditable/textarea + 可见性检查")
    print("🎯 第5步：点击发送 - 发送按钮 + 可见性检查 + 成功确认")
    
    return 0 if success_rate >= 95 else 1

if __name__ == "__main__":
    sys.exit(main())
