#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实验证发送测试
测试基于真实验证的超高速邮件发送功能
"""

import sys
import os
import time
import tempfile
import sqlite3
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import get_logger
from src.core.verified_ultra_fast_sender import VerifiedUltraFastSender
from src.core.cookie_manager import CookieManager

logger = get_logger("RealVerifiedSendingTest")

class RealVerifiedSendingTest:
    """真实验证发送测试类"""
    
    def __init__(self):
        """初始化测试"""
        self.test_name = "真实验证发送测试"
        self.browser_drivers = []
        self.test_accounts = [
            {'email': '<EMAIL>', 'name': '账号1'},
            {'email': '<EMAIL>', 'name': '账号2'}
        ]
        self.test_recipients = []
        self.test_emails_per_account = 2  # 每个账号发送2封邮件进行真实验证
        
        logger.info(f"🧪 {self.test_name} 初始化完成")
    
    def load_test_recipients(self):
        """加载测试收件人"""
        try:
            logger.info("📧 加载测试收件人...")
            
            # 连接数据库
            db_path = "data/sina_email_automation.db"
            if not os.path.exists(db_path):
                raise Exception(f"数据库文件不存在: {db_path}")
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询前4个收件人（每个账号2个）
            cursor.execute("""
                SELECT email, name FROM recipient_data 
                WHERE email IS NOT NULL AND email != ''
                LIMIT 4
            """)
            
            rows = cursor.fetchall()
            conn.close()
            
            if not rows:
                raise Exception("没有找到收件人数据")
            
            self.test_recipients = []
            for email, name in rows:
                self.test_recipients.append({
                    'email': email,
                    'name': name or email.split('@')[0]
                })
            
            logger.info(f"✅ 加载了 {len(self.test_recipients)} 个测试收件人")
            for i, recipient in enumerate(self.test_recipients):
                logger.info(f"  收件人{i+1}: {recipient['email']}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载收件人数据失败: {e}")
            return False
    
    def create_browsers_with_cookies(self):
        """创建浏览器并应用Cookie"""
        try:
            logger.info("🌐 创建浏览器并应用Cookie...")
            
            for i, account in enumerate(self.test_accounts):
                logger.info(f"🌐 创建浏览器 {i+1}/{len(self.test_accounts)} - {account['email']}")
                
                # 配置Chrome选项 - 使用45个极速启动参数
                chrome_options = Options()
                
                # 基础优化参数
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                
                # 45个极速启动参数
                ultra_fast_options = [
                    '--single-process',
                    '--no-first-run',
                    '--disable-background-downloads',
                    '--memory-pressure-off',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-client-side-phishing-detection',
                    '--disable-sync',
                    '--disable-translate',
                    '--hide-scrollbars',
                    '--disable-logging',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-images',
                    '--disable-javascript-harmony-shipping',
                    '--disable-background-networking',
                    '--disable-background-mode',
                    '--disable-default-apps',
                    '--disable-hang-monitor',
                    '--disable-prompt-on-repost',
                    '--disable-web-security',
                    '--disable-features=TranslateUI',
                    '--disable-ipc-flooding-protection',
                    '--disable-component-extensions-with-background-pages',
                    '--disable-domain-reliability',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-breakpad',
                    '--disable-component-update',
                    '--disable-print-preview',
                    '--disable-software-rasterizer',
                    '--disable-speech-api',
                    '--no-default-browser-check',
                    '--no-pings',
                    '--no-zygote',
                    '--disable-gpu-sandbox',
                    '--disable-software-rasterizer',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-field-trial-config',
                    '--disable-back-forward-cache',
                    '--disable-ipc-flooding-protection',
                    '--enable-features=NetworkService,NetworkServiceInProcess',
                    '--force-color-profile=srgb',
                    '--metrics-recording-only'
                ]
                
                for option in ultra_fast_options:
                    chrome_options.add_argument(option)
                
                # 设置用户数据目录
                temp_dir = tempfile.gettempdir()
                user_data_dir = os.path.join(temp_dir, f"test_verified_chrome_{i}_{int(time.time())}")
                chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
                
                # 设置窗口位置
                window_x = i * 450
                window_y = i * 100
                chrome_options.add_argument(f'--window-position={window_x},{window_y}')
                chrome_options.add_argument('--window-size=1300,900')
                
                # 启用性能日志
                chrome_options.add_argument('--enable-logging')
                chrome_options.add_argument('--log-level=0')
                chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
                
                # 创建浏览器
                driver = webdriver.Chrome(options=chrome_options)
                driver.set_page_load_timeout(30)
                driver.implicitly_wait(5)
                
                # 设置反检测
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                driver.execute_script(f"document.title = '真实验证发送测试 - {account['name']}';")
                
                # 导航到新浪邮箱
                driver.get("https://mail.sina.com.cn")
                time.sleep(2)
                
                # 应用Cookie
                success = self._apply_cookies_and_login(driver, account, i+1)
                if success:
                    logger.info(f"🔑 浏览器 {i+1} Cookie登录成功: {account['email']}")
                else:
                    logger.warning(f"⚠️ 浏览器 {i+1} Cookie登录失败: {account['email']}")
                
                self.browser_drivers.append({
                    'driver': driver,
                    'account': account,
                    'browser_num': i+1
                })
                
                logger.info(f"✅ 浏览器 {i+1} 创建成功")
                time.sleep(1)
            
            logger.info(f"✅ 创建了 {len(self.browser_drivers)} 个测试浏览器")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建测试浏览器失败: {e}")
            return False
    
    def _apply_cookies_and_login(self, driver, account, browser_num):
        """应用Cookie并验证登录状态"""
        try:
            username = account['email']
            logger.info(f"🔑 浏览器 {browser_num} 开始应用Cookie: {username}")
            
            # 获取Cookie管理器
            config = {
                'performance.max_concurrent_sessions': 100,
                'session.timeout': 3600
            }
            cookie_manager = CookieManager(config)
            
            # 获取账号的Cookie
            cookie_data = cookie_manager.get_cookies(username)
            if not cookie_data or 'cookies' not in cookie_data:
                logger.warning(f"⚠️ 浏览器 {browser_num} 没有找到Cookie: {username}")
                return False
            
            cookies = cookie_data['cookies']
            logger.info(f"🍪 浏览器 {browser_num} 开始应用 {len(cookies)} 个Cookie")
            
            # 应用Cookie
            for cookie in cookies:
                try:
                    cookie_dict = {
                        'name': cookie.get('name'),
                        'value': cookie.get('value'),
                        'domain': cookie.get('domain', '.sina.com.cn'),
                        'path': cookie.get('path', '/'),
                    }
                    
                    if 'secure' in cookie:
                        cookie_dict['secure'] = cookie['secure']
                    if 'httpOnly' in cookie:
                        cookie_dict['httpOnly'] = cookie['httpOnly']
                    
                    driver.add_cookie(cookie_dict)
                    
                except Exception as e:
                    logger.debug(f"跳过无效Cookie: {e}")
                    continue
            
            # 刷新页面以应用Cookie
            driver.refresh()
            time.sleep(2)
            
            # 验证登录状态
            return self._verify_login_status(driver, username, browser_num)
            
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} Cookie应用失败: {e}")
            return False
    
    def _verify_login_status(self, driver, username, browser_num):
        """验证登录状态"""
        try:
            logger.info(f"🔍 浏览器 {browser_num} 验证登录状态: {username}")
            
            current_url = driver.current_url
            page_title = driver.title
            
            logger.info(f"📍 浏览器 {browser_num} 当前URL: {current_url}")
            logger.info(f"📄 浏览器 {browser_num} 页面标题: {page_title}")
            
            # 检查是否在邮箱主页
            if "mail.sina.com.cn" in current_url and "登录" not in page_title:
                # 尝试查找写信按钮或其他登录标识
                try:
                    # 查找写信按钮
                    write_buttons = driver.find_elements("xpath", "//a[contains(text(), '写信') or contains(@title, '写信')]")
                    if write_buttons:
                        logger.info(f"✅ 浏览器 {browser_num} 登录验证成功: 找到写信按钮")
                        return True
                    
                    # 查找其他登录标识
                    user_elements = driver.find_elements("xpath", "//*[contains(text(), '收件箱') or contains(text(), '发件箱')]")
                    if user_elements:
                        logger.info(f"✅ 浏览器 {browser_num} 登录验证成功: 找到邮箱功能")
                        return True
                    
                except Exception as e:
                    logger.debug(f"查找登录标识时出错: {e}")
            
            logger.warning(f"⚠️ 浏览器 {browser_num} 可能未登录，需要手动登录")
            return False
            
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} 登录状态验证失败: {e}")
            return False
    
    def test_real_verified_sending(self):
        """测试真实验证发送"""
        try:
            logger.info("🚀 开始真实验证发送测试...")
            
            total_tests = 0
            total_success = 0
            total_real_success = 0
            verification_results = []
            
            # 为每个浏览器分配收件人
            for browser_info in self.browser_drivers:
                driver = browser_info['driver']
                account = browser_info['account']
                browser_num = browser_info['browser_num']
                
                logger.info("=" * 60)
                logger.info(f"🌐 浏览器 {browser_num} ({account['email']}) 开始真实验证发送")
                logger.info("=" * 60)
                
                # 创建真实验证发送器
                verified_sender = VerifiedUltraFastSender(driver)
                
                # 计算该浏览器要发送的邮件
                start_idx = (browser_num - 1) * self.test_emails_per_account
                end_idx = start_idx + self.test_emails_per_account
                recipients_for_browser = self.test_recipients[start_idx:end_idx]
                
                logger.info(f"📧 浏览器 {browser_num} 将发送 {len(recipients_for_browser)} 封邮件")
                
                # 发送每封邮件
                for email_idx, recipient in enumerate(recipients_for_browser):
                    email_num = email_idx + 1
                    total_tests += 1
                    
                    logger.info(f"📮 浏览器 {browser_num} 开始发送第 {email_num} 封邮件到: {recipient['email']}")
                    
                    # 构建邮件内容
                    subject = f"🧪 真实验证测试邮件 {email_num} - 来自{account['email']}"
                    content = f"""
                    <div style="font-family: Arial, sans-serif;">
                        <h3>🧪 真实验证发送测试</h3>
                        <p>尊敬的 {recipient['name']}，</p>
                        <p>这是一封来自真实验证发送系统的测试邮件。</p>
                        <ul>
                            <li>发送账号: {account['email']}</li>
                            <li>邮件编号: {email_num}</li>
                            <li>发送时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</li>
                            <li>浏览器编号: {browser_num}</li>
                            <li>验证系统: 多维度真实发送验证</li>
                        </ul>
                        <p><strong>如果您收到此邮件，说明我们的真实验证发送系统运行正常！</strong></p>
                        <p style="color: #666; font-size: 12px;">此邮件由自动化测试系统发送，请勿回复。</p>
                    </div>
                    """
                    
                    # 使用真实验证发送器发送邮件
                    result = verified_sender.send_email_with_verification(
                        recipient['email'], 
                        subject, 
                        content
                    )
                    
                    # 记录结果
                    verification_results.append({
                        'browser_num': browser_num,
                        'email_num': email_num,
                        'account': account['email'],
                        'recipient': recipient['email'],
                        'result': result
                    })
                    
                    if result.get('success'):
                        total_real_success += 1
                        confidence = result.get('verification_confidence', 0)
                        total_time = result.get('total_time', 0)
                        strategy = result.get('strategy_used', 'unknown')
                        
                        logger.info(f"✅ 浏览器 {browser_num} 第 {email_num} 封邮件真实发送成功！")
                        logger.info(f"   策略: {strategy}")
                        logger.info(f"   置信度: {confidence:.2f}")
                        logger.info(f"   总耗时: {total_time:.2f}秒")
                        
                        # 显示验证详情
                        verification_summary = verified_sender.verifier.get_verification_summary(
                            result.get('verification_details', {})
                        )
                        logger.info(f"   验证详情: {verification_summary}")
                        
                    else:
                        error = result.get('error', '未知错误')
                        total_time = result.get('total_time', 0)
                        logger.error(f"❌ 浏览器 {browser_num} 第 {email_num} 封邮件发送失败: {error}")
                        logger.error(f"   总耗时: {total_time:.2f}秒")
                    
                    # 邮件间隔
                    time.sleep(2)
                
                # 显示该浏览器的性能统计
                stats = verified_sender.get_performance_stats()
                logger.info(f"📊 浏览器 {browser_num} 性能统计:")
                for key, value in stats.items():
                    logger.info(f"   {key}: {value}")
                
                logger.info(f"🎉 浏览器 {browser_num} ({account['email']}) 测试完成")
            
            # 显示总体测试结果
            logger.info("=" * 60)
            logger.info("📊 真实验证发送测试总结")
            logger.info("=" * 60)
            
            real_success_rate = (total_real_success / total_tests) * 100 if total_tests > 0 else 0
            
            logger.info(f"📧 总测试邮件: {total_tests} 封")
            logger.info(f"✅ 真实发送成功: {total_real_success} 封")
            logger.info(f"📈 真实成功率: {real_success_rate:.1f}%")
            
            # 显示详细的验证结果分析
            self._analyze_verification_results(verification_results)
            
            logger.info("🎉 真实验证发送测试完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 真实验证发送测试失败: {e}")
            return False
    
    def _analyze_verification_results(self, results):
        """分析验证结果"""
        try:
            logger.info("🔍 分析验证结果...")
            
            strategy_stats = {}
            confidence_levels = []
            time_stats = []
            
            for result_data in results:
                result = result_data['result']
                
                if result.get('success'):
                    # 统计策略使用情况
                    strategy = result.get('strategy_used', 'unknown')
                    if strategy not in strategy_stats:
                        strategy_stats[strategy] = {'count': 0, 'total_time': 0}
                    strategy_stats[strategy]['count'] += 1
                    strategy_stats[strategy]['total_time'] += result.get('total_time', 0)
                    
                    # 收集置信度和时间数据
                    confidence_levels.append(result.get('verification_confidence', 0))
                    time_stats.append(result.get('total_time', 0))
            
            # 显示策略统计
            logger.info("📊 发送策略统计:")
            for strategy, stats in strategy_stats.items():
                avg_time = stats['total_time'] / stats['count'] if stats['count'] > 0 else 0
                logger.info(f"   {strategy}: {stats['count']}次, 平均耗时: {avg_time:.2f}秒")
            
            # 显示置信度统计
            if confidence_levels:
                avg_confidence = sum(confidence_levels) / len(confidence_levels)
                min_confidence = min(confidence_levels)
                max_confidence = max(confidence_levels)
                logger.info(f"📊 验证置信度统计:")
                logger.info(f"   平均置信度: {avg_confidence:.2f}")
                logger.info(f"   最低置信度: {min_confidence:.2f}")
                logger.info(f"   最高置信度: {max_confidence:.2f}")
            
            # 显示时间统计
            if time_stats:
                avg_time = sum(time_stats) / len(time_stats)
                min_time = min(time_stats)
                max_time = max(time_stats)
                logger.info(f"📊 发送时间统计:")
                logger.info(f"   平均发送时间: {avg_time:.2f}秒")
                logger.info(f"   最快发送时间: {min_time:.2f}秒")
                logger.info(f"   最慢发送时间: {max_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"❌ 分析验证结果失败: {e}")
    
    def run_complete_test(self):
        """运行完整测试"""
        try:
            logger.info(f"🧪 开始 {self.test_name}")
            
            # 1. 加载测试数据
            logger.info("=" * 50)
            logger.info("步骤1: 加载测试数据")
            logger.info("=" * 50)
            
            if not self.load_test_recipients():
                return False
            
            # 2. 创建浏览器并应用Cookie
            logger.info("=" * 50)
            logger.info("步骤2: 创建浏览器并应用Cookie")
            logger.info("=" * 50)
            
            if not self.create_browsers_with_cookies():
                return False
            
            # 3. 等待用户确认
            logger.info("=" * 50)
            logger.info("测试信息确认")
            logger.info("=" * 50)
            
            logger.info(f"🧪 将测试真实验证发送功能")
            logger.info(f"🌐 使用 {len(self.browser_drivers)} 个浏览器")
            logger.info(f"📮 每个浏览器发送 {self.test_emails_per_account} 封邮件")
            logger.info(f"👥 总共发送 {len(self.browser_drivers) * self.test_emails_per_account} 封邮件")
            logger.info(f"🔍 每封邮件都将进行多维度真实发送验证")
            
            logger.info("⏰ 测试将在5秒后开始...")
            time.sleep(5)
            
            # 4. 执行真实验证发送测试
            logger.info("=" * 50)
            logger.info("步骤3: 执行真实验证发送测试")
            logger.info("=" * 50)
            
            success = self.test_real_verified_sending()
            
            if success:
                logger.info("🎉 真实验证发送测试成功！")
            else:
                logger.error("❌ 真实验证发送测试失败！")
            
            # 5. 保持浏览器打开供观察
            logger.info("⏰ 浏览器将保持打开30秒供观察...")
            time.sleep(30)
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}")
            return False
            
        finally:
            # 清理浏览器
            self.cleanup_browsers()
    
    def cleanup_browsers(self):
        """清理浏览器"""
        try:
            logger.info("🧹 清理测试浏览器...")
            
            for i, browser_info in enumerate(self.browser_drivers):
                try:
                    browser_info['driver'].quit()
                    logger.info(f"✅ 浏览器 {i+1} 已关闭")
                except Exception as e:
                    logger.warning(f"⚠️ 关闭浏览器 {i+1} 失败: {e}")
            
            self.browser_drivers.clear()
            logger.info("✅ 浏览器清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理浏览器失败: {e}")

def main():
    """主函数"""
    try:
        logger.info("🧪 启动真实验证发送测试程序")
        
        # 创建测试实例
        test = RealVerifiedSendingTest()
        
        # 运行完整测试
        success = test.run_complete_test()
        
        if success:
            logger.info("🎉 测试成功完成！")
            return 0
        else:
            logger.error("❌ 测试失败！")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 测试程序异常: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n测试完成，退出码: {exit_code}")
    sys.exit(exit_code)
