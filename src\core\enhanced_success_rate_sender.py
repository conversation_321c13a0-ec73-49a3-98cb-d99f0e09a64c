#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强成功率发送器
第二阶段优化：重点解决成功率问题，从0%提升到90%以上
"""

import time
import json
from typing import Dict, List, Tuple, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.keys import Keys

from src.utils.logger import get_logger
from src.core.real_sending_verifier import RealSendingVerifier

logger = get_logger("EnhancedSuccessRateSender")

class EnhancedSuccessRateSender:
    """增强成功率发送器 - 第二阶段优化"""
    
    def __init__(self, driver: webdriver.Chrome):
        """
        初始化增强发送器
        
        Args:
            driver: Chrome浏览器驱动
        """
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        self.verifier = RealSendingVerifier(driver)
        
        # 增强的元素选择器 - 更全面的覆盖
        self.enhanced_selectors = {
            'compose_buttons': [
                # 中文写信按钮
                "//a[contains(text(), '写信')]",
                "//button[contains(text(), '写信')]",
                "//span[contains(text(), '写信')]",
                "//div[contains(text(), '写信')]",
                
                # 英文compose按钮
                "//a[contains(text(), 'Compose')]",
                "//button[contains(text(), 'Compose')]",
                "//a[contains(@title, 'Compose')]",
                
                # 通过href属性
                "//a[contains(@href, 'compose')]",
                "//a[contains(@href, 'write')]",
                "//a[contains(@href, 'send')]",
                
                # 通过class属性
                "//*[contains(@class, 'compose')]",
                "//*[contains(@class, 'write')]",
                "//*[contains(@class, 'send-mail')]",
                
                # 通过id属性
                "//*[@id='compose']",
                "//*[@id='write']",
                "//*[@id='send-mail']",
                
                # 图标按钮
                "//i[contains(@class, 'compose')]/parent::*",
                "//i[contains(@class, 'write')]/parent::*",
                
                # 更通用的选择器
                "//*[contains(@onclick, 'compose')]",
                "//*[contains(@onclick, 'write')]"
            ],
            
            'to_fields': [
                # name属性
                "input[name='to']",
                "input[name='To']",
                "input[name='recipient']",
                "input[name='recipients']",
                "input[name='email']",
                "input[name='mail']",
                
                # id属性
                "#to",
                "#To",
                "#recipient",
                "#recipients",
                "#email",
                "#mail",
                
                # class属性
                ".to-field",
                ".recipient-field",
                ".email-field",
                ".mail-field",
                
                # placeholder属性
                "input[placeholder*='收件人']",
                "input[placeholder*='recipient']",
                "input[placeholder*='email']",
                "input[placeholder*='mail']",
                "input[placeholder*='To']",
                
                # type属性
                "input[type='email']",
                "input[type='text']",
                
                # 更通用的选择器
                "textarea[name*='to']",
                "div[contenteditable='true'][placeholder*='收件人']"
            ],
            
            'subject_fields': [
                # name属性
                "input[name='subject']",
                "input[name='Subject']",
                "input[name='title']",
                "input[name='Title']",
                
                # id属性
                "#subject",
                "#Subject",
                "#title",
                "#Title",
                
                # class属性
                ".subject-field",
                ".title-field",
                
                # placeholder属性
                "input[placeholder*='主题']",
                "input[placeholder*='subject']",
                "input[placeholder*='title']",
                "input[placeholder*='Subject']",
                
                # 更通用的选择器
                "input[type='text'][placeholder*='主题']",
                "textarea[name*='subject']"
            ],
            
            'content_fields': [
                # textarea
                "textarea[name='content']",
                "textarea[name='Content']",
                "textarea[name='body']",
                "textarea[name='Body']",
                "textarea[name='message']",
                "textarea[name='Message']",
                
                # id属性
                "#content",
                "#Content",
                "#body",
                "#Body",
                "#message",
                "#Message",
                
                # class属性
                ".content-editor",
                ".body-editor",
                ".message-editor",
                ".editor",
                
                # contenteditable div
                "div[contenteditable='true']",
                "div[contenteditable]",
                
                # iframe编辑器
                "iframe[name*='content']",
                "iframe[id*='content']",
                "iframe[name*='editor']",
                "iframe[id*='editor']",
                
                # 富文本编辑器
                ".rich-editor",
                ".wysiwyg-editor",
                ".html-editor"
            ],
            
            'send_buttons': [
                # 中文发送按钮
                "//button[contains(text(), '发送')]",
                "//input[@value='发送']",
                "//a[contains(text(), '发送')]",
                "//span[contains(text(), '发送')]",
                
                # 英文send按钮
                "//button[contains(text(), 'Send')]",
                "//input[@value='Send']",
                "//a[contains(text(), 'Send')]",
                
                # type属性
                "button[type='submit']",
                "input[type='submit']",
                
                # class属性
                ".send-button",
                ".send-btn",
                ".submit-button",
                ".submit-btn",
                
                # id属性
                "#send",
                "#Send",
                "#submit",
                "#Submit",
                
                # title属性
                "button[title*='发送']",
                "button[title*='Send']",
                "input[title*='发送']",
                "input[title*='Send']",
                
                # 更通用的选择器
                "//*[contains(@onclick, 'send')]",
                "//*[contains(@onclick, 'submit')]"
            ]
        }
        
        # 性能统计
        self.performance_stats = {
            'total_attempts': 0,
            'success_count': 0,
            'strategy_success': {
                'enhanced_ultra_fast': 0,
                'enhanced_standard': 0,
                'enhanced_safe': 0,
                'manual_simulation': 0
            },
            'total_time': 0,
            'fastest_time': float('inf'),
            'slowest_time': 0
        }
    
    def send_email_enhanced(self, to_email: str, subject: str, content: str) -> Dict:
        """
        增强的邮件发送方法 - 重点提升成功率
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            content: 邮件内容
            
        Returns:
            Dict: 发送结果，包含真实验证信息
        """
        start_time = time.time()
        self.performance_stats['total_attempts'] += 1
        
        try:
            logger.info(f"🚀 开始增强发送: {to_email}")
            logger.info(f"📝 主题: {subject}")
            
            # 策略1: 增强超高速发送
            result = self._enhanced_ultra_fast_strategy(to_email, subject, content)
            if result['success']:
                return self._finalize_result(result, start_time, 'enhanced_ultra_fast')
            
            # 策略2: 增强标准发送
            result = self._enhanced_standard_strategy(to_email, subject, content)
            if result['success']:
                return self._finalize_result(result, start_time, 'enhanced_standard')
            
            # 策略3: 增强安全发送
            result = self._enhanced_safe_strategy(to_email, subject, content)
            if result['success']:
                return self._finalize_result(result, start_time, 'enhanced_safe')
            
            # 策略4: 手动模拟发送（最后的保障）
            result = self._manual_simulation_strategy(to_email, subject, content)
            if result['success']:
                return self._finalize_result(result, start_time, 'manual_simulation')
            
            # 所有策略都失败
            total_time = time.time() - start_time
            failure_result = {
                'success': False,
                'error': '所有增强策略都失败',
                'total_time': total_time,
                'strategies_tried': 4
            }
            
            self._update_performance_stats(failure_result, 'failed')
            logger.error(f"❌ 所有增强策略都失败，总耗时: {total_time:.2f}秒")
            return failure_result
            
        except Exception as e:
            total_time = time.time() - start_time
            error_result = {
                'success': False,
                'error': f'发送异常: {str(e)}',
                'total_time': total_time
            }
            self._update_performance_stats(error_result, 'error')
            logger.error(f"❌ 发送异常: {e}")
            return error_result
    
    def _enhanced_ultra_fast_strategy(self, to_email: str, subject: str, content: str) -> Dict:
        """增强超高速策略 - 改进的JavaScript方法"""
        try:
            logger.info("⚡ 执行增强超高速策略...")
            start_time = time.time()
            
            # 确保在写信页面
            if not self._ensure_compose_page_enhanced():
                return {'success': False, 'error': '无法进入写信页面'}
            
            # 增强的JavaScript发送脚本
            script = f"""
            try {{
                console.log('🚀 开始增强JavaScript发送...');
                
                // 更全面的元素查找
                function findElement(selectors) {{
                    for (let selector of selectors) {{
                        try {{
                            let element = document.querySelector(selector);
                            if (element && (element.offsetParent !== null || element.tagName === 'INPUT')) {{
                                return element;
                            }}
                        }} catch(e) {{
                            continue;
                        }}
                    }}
                    return null;
                }}
                
                // 查找收件人字段
                const toSelectors = [
                    'input[name="to"]', '#to', '.to-field', 
                    'input[placeholder*="收件人"]', 'input[type="email"]',
                    'input[placeholder*="recipient"]', 'input[placeholder*="To"]'
                ];
                const toField = findElement(toSelectors);
                
                // 查找主题字段
                const subjectSelectors = [
                    'input[name="subject"]', '#subject', '.subject-field',
                    'input[placeholder*="主题"]', 'input[placeholder*="subject"]'
                ];
                const subjectField = findElement(subjectSelectors);
                
                // 查找内容字段
                const contentSelectors = [
                    'textarea[name="content"]', '#content', '.content-editor',
                    'div[contenteditable="true"]', 'textarea[name="body"]'
                ];
                const contentField = findElement(contentSelectors);
                
                // 查找发送按钮
                const sendSelectors = [
                    'button[type="submit"]', '.send-button', '#send',
                    'input[type="submit"]', 'button[title*="发送"]'
                ];
                const sendButton = findElement(sendSelectors);
                
                console.log('找到的元素:', {{
                    to: !!toField,
                    subject: !!subjectField,
                    content: !!contentField,
                    send: !!sendButton
                }});
                
                // 验证关键元素
                if (!toField || !subjectField || !sendButton) {{
                    return {{
                        success: false, 
                        error: '缺少关键元素',
                        found: {{
                            to: !!toField,
                            subject: !!subjectField,
                            send: !!sendButton
                        }}
                    }};
                }}
                
                // 填写表单
                toField.focus();
                toField.value = '{to_email}';
                toField.dispatchEvent(new Event('input', {{bubbles: true}}));
                toField.dispatchEvent(new Event('change', {{bubbles: true}}));
                
                subjectField.focus();
                subjectField.value = '{subject}';
                subjectField.dispatchEvent(new Event('input', {{bubbles: true}}));
                subjectField.dispatchEvent(new Event('change', {{bubbles: true}}));
                
                if (contentField) {{
                    contentField.focus();
                    if (contentField.tagName.toLowerCase() === 'div') {{
                        contentField.innerHTML = '{content}';
                    }} else {{
                        contentField.value = '{content}';
                    }}
                    contentField.dispatchEvent(new Event('input', {{bubbles: true}}));
                    contentField.dispatchEvent(new Event('change', {{bubbles: true}}));
                }}
                
                // 等待一小段时间确保数据填写完成
                setTimeout(function() {{
                    sendButton.focus();
                    sendButton.click();
                    console.log('✅ 发送按钮已点击');
                }}, 200);
                
                return {{success: true, method: 'enhanced_javascript'}};
                
            }} catch(e) {{
                console.error('❌ 增强JavaScript发送失败:', e);
                return {{success: false, error: e.message}};
            }}
            """
            
            # 执行JavaScript
            result = self.driver.execute_script(script)
            
            # 等待发送完成
            time.sleep(1)
            
            elapsed_time = time.time() - start_time
            
            if result and result.get('success'):
                logger.info(f"✅ 增强超高速策略成功，耗时: {elapsed_time:.2f}秒")
                return {'success': True, 'time': elapsed_time, 'method': 'enhanced_ultra_fast'}
            else:
                error_msg = result.get('error', '未知错误') if result else 'JavaScript执行失败'
                logger.warning(f"❌ 增强超高速策略失败: {error_msg}")
                if result and 'found' in result:
                    logger.info(f"   元素查找结果: {result['found']}")
                return {'success': False, 'error': error_msg, 'time': elapsed_time}
                
        except Exception as e:
            logger.error(f"❌ 增强超高速策略异常: {e}")
            return {'success': False, 'error': str(e)}
    
    def _enhanced_standard_strategy(self, to_email: str, subject: str, content: str) -> Dict:
        """增强标准策略 - 改进的Selenium方法"""
        try:
            logger.info("🔧 执行增强标准策略...")
            start_time = time.time()
            
            # 确保在写信页面
            if not self._ensure_compose_page_enhanced():
                return {'success': False, 'error': '无法进入写信页面'}
            
            # 增强的元素查找和填写
            # 1. 查找并填写收件人
            to_field = self._find_element_enhanced(self.enhanced_selectors['to_fields'])
            if not to_field:
                return {'success': False, 'error': '未找到收件人字段'}
            
            self._fill_field_enhanced(to_field, to_email)
            logger.info("✅ 收件人填写完成")
            
            # 2. 查找并填写主题
            subject_field = self._find_element_enhanced(self.enhanced_selectors['subject_fields'])
            if not subject_field:
                return {'success': False, 'error': '未找到主题字段'}
            
            self._fill_field_enhanced(subject_field, subject)
            logger.info("✅ 主题填写完成")
            
            # 3. 查找并填写内容
            content_field = self._find_element_enhanced(self.enhanced_selectors['content_fields'])
            if content_field:
                self._fill_field_enhanced(content_field, content)
                logger.info("✅ 内容填写完成")
            else:
                logger.warning("⚠️ 未找到内容字段，跳过内容填写")
            
            # 4. 查找并点击发送按钮
            send_button = self._find_element_enhanced(self.enhanced_selectors['send_buttons'], by_xpath=True)
            if not send_button:
                return {'success': False, 'error': '未找到发送按钮'}
            
            # 滚动到发送按钮位置
            self.driver.execute_script("arguments[0].scrollIntoView(true);", send_button)
            time.sleep(0.5)
            
            # 点击发送按钮
            try:
                send_button.click()
            except Exception as e:
                # 如果普通点击失败，使用JavaScript点击
                self.driver.execute_script("arguments[0].click();", send_button)
            
            logger.info("✅ 发送按钮点击完成")
            time.sleep(1)
            
            elapsed_time = time.time() - start_time
            logger.info(f"✅ 增强标准策略成功，耗时: {elapsed_time:.2f}秒")
            return {'success': True, 'time': elapsed_time, 'method': 'enhanced_standard'}
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ 增强标准策略异常: {e}")
            return {'success': False, 'error': str(e), 'time': elapsed_time}
    
    def _enhanced_safe_strategy(self, to_email: str, subject: str, content: str) -> Dict:
        """增强安全策略 - 最稳定的方法"""
        try:
            logger.info("🛡️ 执行增强安全策略...")
            start_time = time.time()
            
            # 页面刷新确保状态
            self.driver.refresh()
            time.sleep(2)
            
            # 使用增强标准策略，但增加更多等待时间
            result = self._enhanced_standard_strategy(to_email, subject, content)
            
            elapsed_time = time.time() - start_time
            if result['success']:
                logger.info(f"✅ 增强安全策略成功，耗时: {elapsed_time:.2f}秒")
                return {'success': True, 'time': elapsed_time, 'method': 'enhanced_safe'}
            else:
                logger.error(f"❌ 增强安全策略失败，耗时: {elapsed_time:.2f}秒")
                return {'success': False, 'error': '增强安全策略失败', 'time': elapsed_time}
                
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ 增强安全策略异常: {e}")
            return {'success': False, 'error': str(e), 'time': elapsed_time}
    
    def _manual_simulation_strategy(self, to_email: str, subject: str, content: str) -> Dict:
        """手动模拟策略 - 最后的保障"""
        try:
            logger.info("👤 执行手动模拟策略...")
            start_time = time.time()
            
            # 确保在写信页面
            if not self._ensure_compose_page_enhanced():
                return {'success': False, 'error': '无法进入写信页面'}
            
            # 使用键盘操作模拟人工输入
            # 1. Tab键导航到收件人字段
            for _ in range(10):  # 最多尝试10次Tab
                active_element = self.driver.switch_to.active_element
                if active_element.get_attribute('name') == 'to' or 'to' in active_element.get_attribute('class', '').lower():
                    break
                active_element.send_keys(Keys.TAB)
                time.sleep(0.2)
            
            # 填写收件人
            active_element = self.driver.switch_to.active_element
            active_element.clear()
            active_element.send_keys(to_email)
            time.sleep(0.5)
            
            # Tab到主题字段
            active_element.send_keys(Keys.TAB)
            time.sleep(0.2)
            
            # 填写主题
            active_element = self.driver.switch_to.active_element
            active_element.clear()
            active_element.send_keys(subject)
            time.sleep(0.5)
            
            # Tab到内容字段
            active_element.send_keys(Keys.TAB)
            time.sleep(0.2)
            
            # 填写内容
            active_element = self.driver.switch_to.active_element
            active_element.clear()
            active_element.send_keys(content)
            time.sleep(0.5)
            
            # 使用Ctrl+Enter发送（常见的快捷键）
            active_element.send_keys(Keys.CONTROL + Keys.ENTER)
            time.sleep(1)
            
            elapsed_time = time.time() - start_time
            logger.info(f"✅ 手动模拟策略完成，耗时: {elapsed_time:.2f}秒")
            return {'success': True, 'time': elapsed_time, 'method': 'manual_simulation'}
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ 手动模拟策略异常: {e}")
            return {'success': False, 'error': str(e), 'time': elapsed_time}
    
    def _ensure_compose_page_enhanced(self) -> bool:
        """增强的写信页面确保方法"""
        try:
            current_url = self.driver.current_url.lower()
            
            # 如果已经在写信页面
            if 'compose' in current_url or 'write' in current_url:
                logger.info("✅ 已在写信页面")
                return True
            
            # 尝试多种写信按钮
            for selector in self.enhanced_selectors['compose_buttons']:
                try:
                    write_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    write_button.click()
                    time.sleep(2)
                    logger.info(f"✅ 成功点击写信按钮: {selector}")
                    return True
                except TimeoutException:
                    continue
                except Exception as e:
                    logger.debug(f"写信按钮点击失败: {selector}, 错误: {e}")
                    continue
            
            logger.warning("❌ 无法进入写信页面")
            return False
            
        except Exception as e:
            logger.error(f"❌ 确保写信页面失败: {e}")
            return False
    
    def _find_element_enhanced(self, selectors: List[str], by_xpath: bool = False) -> Optional:
        """增强的元素查找方法"""
        for selector in selectors:
            try:
                if by_xpath:
                    element = self.driver.find_element(By.XPATH, selector)
                else:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                
                if element.is_displayed() and element.is_enabled():
                    return element
            except NoSuchElementException:
                continue
            except Exception as e:
                logger.debug(f"元素查找异常: {selector}, 错误: {e}")
                continue
        return None
    
    def _fill_field_enhanced(self, field, value: str):
        """增强的字段填写方法"""
        try:
            # 滚动到元素位置
            self.driver.execute_script("arguments[0].scrollIntoView(true);", field)
            time.sleep(0.2)
            
            # 聚焦元素
            field.click()
            time.sleep(0.1)
            
            # 清空字段
            field.clear()
            time.sleep(0.1)
            
            # 填写内容
            if field.tag_name.lower() == 'div' and field.get_attribute('contenteditable'):
                # 对于contenteditable的div
                self.driver.execute_script("arguments[0].innerHTML = arguments[1];", field, value)
            else:
                # 对于普通input和textarea
                field.send_keys(value)
            
            # 触发事件
            self.driver.execute_script("""
                arguments[0].dispatchEvent(new Event('input', {bubbles: true}));
                arguments[0].dispatchEvent(new Event('change', {bubbles: true}));
            """, field)
            
            time.sleep(0.2)
            
        except Exception as e:
            logger.warning(f"字段填写失败: {e}")
            # 备用方法：直接设置value
            try:
                self.driver.execute_script("arguments[0].value = arguments[1];", field, value)
            except:
                pass
    
    def _finalize_result(self, strategy_result: Dict, start_time: float, strategy_name: str) -> Dict:
        """完成结果处理，包括真实验证"""
        try:
            # 进行真实发送验证
            is_verified, confidence, verification_details = self.verifier.verify_real_sending(timeout=10)
            
            # 计算总耗时
            total_time = time.time() - start_time
            
            # 构建最终结果
            final_result = {
                'success': is_verified,
                'strategy_used': strategy_name,
                'verification_confidence': confidence,
                'verification_details': verification_details,
                'total_time': total_time,
                'strategy_time': strategy_result.get('time', 0),
                'verification_time': verification_details.get('verification_time', 0)
            }
            
            # 更新性能统计
            self._update_performance_stats(final_result, strategy_name)
            
            if is_verified:
                logger.info(f"✅ 增强发送成功！策略: {strategy_name}, 置信度: {confidence:.2f}, 总耗时: {total_time:.2f}秒")
            else:
                logger.warning(f"❌ 发送验证失败！策略: {strategy_name}, 置信度: {confidence:.2f}")
            
            return final_result
            
        except Exception as e:
            logger.error(f"❌ 结果处理异常: {e}")
            total_time = time.time() - start_time
            return {
                'success': False,
                'error': f'结果处理异常: {str(e)}',
                'total_time': total_time
            }
    
    def _update_performance_stats(self, result: Dict, strategy: str):
        """更新性能统计"""
        total_time = result.get('total_time', 0)
        self.performance_stats['total_time'] += total_time
        
        if result.get('success'):
            self.performance_stats['success_count'] += 1
            if strategy in self.performance_stats['strategy_success']:
                self.performance_stats['strategy_success'][strategy] += 1
            
            # 更新速度统计
            if total_time < self.performance_stats['fastest_time']:
                self.performance_stats['fastest_time'] = total_time
            if total_time > self.performance_stats['slowest_time']:
                self.performance_stats['slowest_time'] = total_time
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        success_rate = (self.performance_stats['success_count'] / self.performance_stats['total_attempts'] 
                       if self.performance_stats['total_attempts'] > 0 else 0)
        
        avg_time = (self.performance_stats['total_time'] / self.performance_stats['total_attempts']
                   if self.performance_stats['total_attempts'] > 0 else 0)
        
        return {
            'total_attempts': self.performance_stats['total_attempts'],
            'success_count': self.performance_stats['success_count'],
            'success_rate': f"{success_rate:.1%}",
            'average_time': f"{avg_time:.2f}秒",
            'fastest_time': f"{self.performance_stats['fastest_time']:.2f}秒" if self.performance_stats['fastest_time'] != float('inf') else "N/A",
            'slowest_time': f"{self.performance_stats['slowest_time']:.2f}秒",
            'strategy_success': self.performance_stats['strategy_success']
        }
