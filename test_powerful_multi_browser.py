#!/usr/bin/env python3
"""
强大的多浏览器发送系统测试脚本
测试真正的多账号并发发送功能
"""

import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger import setup_logger
from src.models.account import Account
from src.core.integrated_email_sender import IntegratedEmailSender, IntegratedSenderConfig

logger = setup_logger("INFO")

def create_test_accounts():
    """创建测试账号"""
    test_accounts = [
        Account(email="<EMAIL>", password="password1"),
        Account(email="<EMAIL>", password="password2"),
        Account(email="<EMAIL>", password="password3"),
        Account(email="<EMAIL>", password="password4"),
        Account(email="<EMAIL>", password="password5"),
        Account(email="<EMAIL>", password="password6"),
    ]
    return test_accounts

def test_powerful_multi_browser_system():
    """测试强大的多浏览器系统"""
    logger.info("🚀 开始测试强大的多浏览器发送系统...")
    
    try:
        # 1. 创建测试账号
        accounts = create_test_accounts()
        logger.info(f"📋 创建了 {len(accounts)} 个测试账号")
        
        # 2. 创建配置
        config = IntegratedSenderConfig(
            max_browsers=3,  # 3个浏览器
            emails_per_account=3,  # 每个账号发送3封邮件
            send_interval=1.0,  # 1秒间隔
            switch_cooldown=2.0,  # 2秒切换冷却
            enable_load_balancing=True,  # 启用负载均衡
            enable_auto_retry=True,  # 启用自动重试
            minimize_browsers=True  # 最小化浏览器
        )
        logger.info(f"⚙️ 创建配置: {config.max_browsers}个浏览器, 每账号{config.emails_per_account}封邮件")
        
        # 3. 创建集成邮件发送器
        sender = IntegratedEmailSender(config)
        
        # 4. 设置状态回调
        sender.add_status_callback(lambda event_type, data: logger.info(f"📊 状态变化: {event_type}"))
        
        # 5. 初始化系统
        logger.info("🔧 初始化集成发送系统...")
        if not sender.initialize(accounts):
            logger.error("❌ 系统初始化失败")
            return False
        
        logger.info("✅ 系统初始化成功")
        
        # 6. 启动发送
        logger.info("🚀 启动发送系统...")
        if not sender.start_sending():
            logger.error("❌ 启动发送失败")
            return False
        
        logger.info("✅ 发送系统启动成功")
        
        # 7. 添加测试任务
        test_emails = [
            ("<EMAIL>", "测试邮件1", "这是第一封测试邮件的内容"),
            ("<EMAIL>", "测试邮件2", "这是第二封测试邮件的内容"),
            ("<EMAIL>", "测试邮件3", "这是第三封测试邮件的内容"),
            ("<EMAIL>", "测试邮件4", "这是第四封测试邮件的内容"),
            ("<EMAIL>", "测试邮件5", "这是第五封测试邮件的内容"),
            ("<EMAIL>", "测试邮件6", "这是第六封测试邮件的内容"),
            ("<EMAIL>", "测试邮件7", "这是第七封测试邮件的内容"),
            ("<EMAIL>", "测试邮件8", "这是第八封测试邮件的内容"),
            ("<EMAIL>", "测试邮件9", "这是第九封测试邮件的内容"),
            ("<EMAIL>", "测试邮件10", "这是第十封测试邮件的内容"),
        ]
        
        logger.info(f"📝 添加 {len(test_emails)} 个测试任务...")
        task_ids = sender.add_batch_tasks(test_emails)
        logger.info(f"✅ 成功添加 {len(task_ids)} 个任务")
        
        # 8. 监控发送过程
        logger.info("👀 开始监控发送过程...")
        monitor_duration = 120  # 监控120秒
        start_time = time.time()
        
        last_stats_time = 0
        stats_interval = 10  # 每10秒显示一次详细统计
        
        while time.time() - start_time < monitor_duration:
            try:
                current_time = time.time()
                
                # 获取统计信息
                stats = sender.get_comprehensive_stats()
                
                # 每10秒显示详细统计
                if current_time - last_stats_time >= stats_interval:
                    logger.info("=" * 60)
                    logger.info("📊 详细统计报告:")
                    
                    # 系统状态
                    system_status = stats.get('system_status', {})
                    logger.info(f"  系统运行: {system_status.get('is_running', False)}")
                    logger.info(f"  运行时间: {system_status.get('runtime_seconds', 0):.1f}秒")
                    
                    # 发送统计
                    sending_stats = stats.get('sending_stats', {})
                    logger.info(f"  总发送: {sending_stats.get('total_sent', 0)}")
                    logger.info(f"  总失败: {sending_stats.get('total_failed', 0)}")
                    logger.info(f"  成功率: {sending_stats.get('success_rate', 0):.1f}%")
                    
                    # 分发器统计
                    distributor_stats = stats.get('distributor_stats', {})
                    task_stats = distributor_stats.get('task_stats', {})
                    logger.info(f"  总任务: {task_stats.get('total_tasks', 0)}")
                    logger.info(f"  已完成: {task_stats.get('completed_tasks', 0)}")
                    logger.info(f"  发送中: {task_stats.get('sending_tasks', 0)}")
                    logger.info(f"  待分配: {task_stats.get('pending_tasks', 0)}")
                    
                    # 浏览器统计
                    browser_stats = distributor_stats.get('browser_stats', {})
                    logger.info(f"  活跃浏览器: {browser_stats.get('active_browsers', 0)}/{browser_stats.get('total_browsers', 0)}")
                    
                    # 账号统计
                    account_stats = distributor_stats.get('account_stats', {})
                    logger.info(f"  账号切换: {account_stats.get('total_switches', 0)} (成功率: {account_stats.get('switch_success_rate', 0):.1f}%)")
                    
                    # 浏览器详情
                    browser_details = distributor_stats.get('browser_details', {})
                    for browser_id, details in browser_details.items():
                        current_account = details.get('current_account', '无')
                        status = details.get('status', 'unknown')
                        total_completed = details.get('total_tasks_completed', 0)
                        queue_size = details.get('current_queue_size', 0)
                        
                        status_icon = {
                            'idle': '🟢',
                            'busy': '🟡',
                            'error': '🔴',
                            'switching': '🔄'
                        }.get(status, '❓')
                        
                        logger.info(f"  {status_icon} {browser_id}: {current_account} (完成:{total_completed}, 队列:{queue_size})")
                    
                    logger.info("=" * 60)
                    last_stats_time = current_time
                
                # 检查是否完成所有任务
                completed = task_stats.get('completed_tasks', 0)
                total = task_stats.get('total_tasks', 0)
                if completed >= total and total > 0:
                    logger.info("🎉 所有任务已完成！")
                    break
                
                time.sleep(2)  # 每2秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 监控过程异常: {e}")
                time.sleep(1)
        
        # 9. 获取最终统计
        logger.info("📊 获取最终统计信息...")
        final_stats = sender.get_comprehensive_stats()
        
        logger.info("🏁 最终统计报告:")
        logger.info("=" * 60)
        
        # 系统统计
        system_status = final_stats.get('system_status', {})
        logger.info(f"总运行时间: {system_status.get('runtime_seconds', 0):.1f}秒")
        
        # 发送统计
        sending_stats = final_stats.get('sending_stats', {})
        logger.info(f"总发送: {sending_stats.get('total_sent', 0)}")
        logger.info(f"总失败: {sending_stats.get('total_failed', 0)}")
        logger.info(f"成功率: {sending_stats.get('success_rate', 0):.1f}%")
        
        # 任务统计
        distributor_stats = final_stats.get('distributor_stats', {})
        task_stats = distributor_stats.get('task_stats', {})
        logger.info(f"任务完成率: {(task_stats.get('completed_tasks', 0) / max(task_stats.get('total_tasks', 1), 1)) * 100:.1f}%")
        
        # 账号切换统计
        account_stats = distributor_stats.get('account_stats', {})
        logger.info(f"账号切换次数: {account_stats.get('total_switches', 0)}")
        logger.info(f"切换成功率: {account_stats.get('switch_success_rate', 0):.1f}%")
        
        # 性能指标
        emails_per_minute = task_stats.get('emails_per_minute', 0)
        logger.info(f"发送速度: {emails_per_minute:.1f} 邮件/分钟")
        
        logger.info("=" * 60)
        
        # 10. 停止发送
        logger.info("🛑 停止发送系统...")
        sender.stop_sending()
        
        # 11. 清理资源
        logger.info("🧹 清理资源...")
        sender.cleanup()
        
        logger.info("✅ 测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试过程异常: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_concurrent_performance():
    """测试并发性能"""
    logger.info("🚀 开始并发性能测试...")
    
    try:
        accounts = create_test_accounts()
        
        # 配置更多浏览器和任务
        config = IntegratedSenderConfig(
            max_browsers=5,  # 5个浏览器
            emails_per_account=2,  # 每个账号发送2封邮件
            send_interval=0.5,  # 0.5秒间隔
            switch_cooldown=1.0,  # 1秒切换冷却
            enable_load_balancing=True,
            enable_auto_retry=True
        )
        
        sender = IntegratedEmailSender(config)
        
        if not sender.initialize(accounts):
            logger.error("❌ 性能测试初始化失败")
            return False
        
        if not sender.start_sending():
            logger.error("❌ 性能测试启动失败")
            return False
        
        # 添加更多任务
        test_emails = [(f"test{i}@example.com", f"性能测试邮件{i}", f"这是第{i}封性能测试邮件") for i in range(1, 21)]
        task_ids = sender.add_batch_tasks(test_emails)
        
        logger.info(f"📝 性能测试: 添加了 {len(task_ids)} 个任务")
        
        # 监控性能
        start_time = time.time()
        while time.time() - start_time < 60:  # 监控60秒
            stats = sender.get_comprehensive_stats()
            task_stats = stats.get('distributor_stats', {}).get('task_stats', {})
            
            completed = task_stats.get('completed_tasks', 0)
            total = task_stats.get('total_tasks', 0)
            
            if completed >= total and total > 0:
                break
            
            time.sleep(1)
        
        # 性能结果
        final_stats = sender.get_comprehensive_stats()
        runtime = final_stats.get('system_status', {}).get('runtime_seconds', 0)
        completed_tasks = final_stats.get('distributor_stats', {}).get('task_stats', {}).get('completed_tasks', 0)
        
        if runtime > 0:
            performance = (completed_tasks / runtime) * 60
            logger.info(f"🏆 性能测试结果: {performance:.1f} 邮件/分钟")
        
        sender.stop_sending()
        sender.cleanup()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 性能测试异常: {e}")
        return False

def main():
    """主函数"""
    logger.info("🧪 开始强大的多浏览器发送系统测试...")
    
    # 测试1: 基本功能测试
    logger.info("=" * 50)
    logger.info("测试1: 基本功能测试")
    logger.info("=" * 50)
    
    success1 = test_powerful_multi_browser_system()
    
    # 等待一段时间
    time.sleep(5)
    
    # 测试2: 并发性能测试
    logger.info("=" * 50)
    logger.info("测试2: 并发性能测试")
    logger.info("=" * 50)
    
    success2 = test_concurrent_performance()
    
    # 总结
    logger.info("=" * 50)
    logger.info("测试总结")
    logger.info("=" * 50)
    
    logger.info(f"基本功能测试: {'✅ 通过' if success1 else '❌ 失败'}")
    logger.info(f"并发性能测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        logger.info("🎉 所有测试通过！强大的多浏览器发送系统工作正常")
        return 0
    else:
        logger.error("❌ 部分测试失败，需要检查和修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
