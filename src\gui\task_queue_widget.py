#!/usr/bin/env python3
"""
任务队列UI组件 - 重新架构版本
提供完整的任务队列管理界面
"""

import sys
import os
from typing import List, Dict, Any, Optional
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.task_queue_manager import TaskQueueManager, EmailTask, TaskStatus
from src.utils.logger import setup_logger

logger = setup_logger("INFO")

class TaskQueueWidget(QWidget):
    """任务队列管理界面"""
    
    # 信号
    task_selected = pyqtSignal(str)  # 任务被选中
    task_action_requested = pyqtSignal(str, str)  # 任务操作请求 (task_id, action)
    
    def __init__(self, task_manager: TaskQueueManager, parent=None):
        super().__init__(parent)
        self.task_manager = task_manager
        self.selected_task_ids = set()
        
        # 设置回调
        self.task_manager.on_task_status_changed = self.on_task_status_changed
        self.task_manager.on_stats_updated = self.on_stats_updated
        
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题和统计信息
        self.create_header(layout)
        
        # 工具栏
        self.create_toolbar(layout)
        
        # 任务表格
        self.create_task_table(layout)
        
        # 底部状态栏
        self.create_status_bar(layout)
        
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                gridline-color: #e9ecef;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
    
    def create_header(self, layout):
        """创建标题和统计信息"""
        header_layout = QHBoxLayout()
        
        # 标题
        title_label = QLabel("📋 任务队列管理")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin: 10px 0;
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # 统计信息
        self.stats_label = QLabel()
        self.stats_label.setStyleSheet("""
            font-size: 12px;
            color: #6c757d;
            background-color: white;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        """)
        header_layout.addWidget(self.stats_label)
        
        layout.addLayout(header_layout)
    
    def create_toolbar(self, layout):
        """创建工具栏"""
        toolbar_layout = QHBoxLayout()
        
        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.refresh_tasks)
        toolbar_layout.addWidget(self.refresh_btn)
        
        # 清理已完成任务
        self.clear_completed_btn = QPushButton("🧹 清理已完成")
        self.clear_completed_btn.clicked.connect(self.clear_completed_tasks)
        toolbar_layout.addWidget(self.clear_completed_btn)
        
        # 删除选中任务
        self.delete_selected_btn = QPushButton("🗑️ 删除选中")
        self.delete_selected_btn.clicked.connect(self.delete_selected_tasks)
        self.delete_selected_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_selected_btn)
        
        toolbar_layout.addStretch()
        
        # 状态筛选
        filter_label = QLabel("状态筛选:")
        toolbar_layout.addWidget(filter_label)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "全部", "等待中", "处理中", "已完成", "失败", "已取消"
        ])
        self.status_filter.currentTextChanged.connect(self.filter_tasks)
        toolbar_layout.addWidget(self.status_filter)
        
        layout.addLayout(toolbar_layout)
    
    def create_task_table(self, layout):
        """创建任务表格"""
        self.task_table = QTableWidget()
        self.task_table.setColumnCount(8)
        self.task_table.setHorizontalHeaderLabels([
            "选择", "任务ID", "收件人", "主题", "状态", "创建时间", "完成时间", "错误信息"
        ])
        
        # 设置列宽
        header = self.task_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 选择
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 任务ID
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # 收件人
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # 主题
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 状态
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 创建时间
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # 完成时间
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # 错误信息
        
        self.task_table.setColumnWidth(0, 50)
        
        # 设置表格属性
        self.task_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.task_table.setAlternatingRowColors(True)
        self.task_table.setSortingEnabled(True)
        
        # 连接信号
        self.task_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.task_table.cellDoubleClicked.connect(self.on_cell_double_clicked)
        
        layout.addWidget(self.task_table)
    
    def create_status_bar(self, layout):
        """创建状态栏"""
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            color: #28a745;
            font-weight: bold;
        """)
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_layout.addWidget(self.progress_bar)
        
        layout.addLayout(status_layout)
    
    def setup_timer(self):
        """设置定时器"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_tasks)
        self.refresh_timer.start(2000)  # 每2秒刷新一次
    
    def refresh_tasks(self):
        """刷新任务列表"""
        try:
            tasks = self.task_manager.get_all_tasks()
            self.update_task_table(tasks)
            self.update_stats_display()
        except Exception as e:
            logger.error(f"刷新任务列表失败: {e}")
    
    def update_task_table(self, tasks: List[EmailTask]):
        """更新任务表格"""
        # 获取当前筛选状态
        filter_status = self.status_filter.currentText()
        
        # 筛选任务
        if filter_status != "全部":
            status_map = {
                "等待中": TaskStatus.PENDING,
                "处理中": TaskStatus.PROCESSING,
                "已完成": TaskStatus.COMPLETED,
                "失败": TaskStatus.FAILED,
                "已取消": TaskStatus.CANCELLED
            }
            if filter_status in status_map:
                tasks = [task for task in tasks if task.status == status_map[filter_status]]
        
        # 更新表格
        self.task_table.setRowCount(len(tasks))
        
        for row, task in enumerate(tasks):
            # 选择框
            checkbox = QCheckBox()
            checkbox.setChecked(task.task_id in self.selected_task_ids)
            checkbox.stateChanged.connect(lambda state, tid=task.task_id: self.on_checkbox_changed(tid, state))
            self.task_table.setCellWidget(row, 0, checkbox)
            
            # 任务ID
            self.task_table.setItem(row, 1, QTableWidgetItem(task.task_id[-8:]))  # 显示后8位
            
            # 收件人
            self.task_table.setItem(row, 2, QTableWidgetItem(task.to_email))
            
            # 主题
            subject_item = QTableWidgetItem(task.subject[:50] + "..." if len(task.subject) > 50 else task.subject)
            subject_item.setToolTip(task.subject)
            self.task_table.setItem(row, 3, subject_item)
            
            # 状态
            status_item = QTableWidgetItem(self.get_status_text(task.status))
            status_item.setBackground(self.get_status_color(task.status))
            self.task_table.setItem(row, 4, status_item)
            
            # 创建时间
            create_time = task.create_time.strftime("%H:%M:%S")
            self.task_table.setItem(row, 5, QTableWidgetItem(create_time))
            
            # 完成时间
            complete_time = task.complete_time.strftime("%H:%M:%S") if task.complete_time else ""
            self.task_table.setItem(row, 6, QTableWidgetItem(complete_time))
            
            # 错误信息
            error_msg = task.error_message or ""
            error_item = QTableWidgetItem(error_msg[:30] + "..." if len(error_msg) > 30 else error_msg)
            if error_msg:
                error_item.setToolTip(error_msg)
            self.task_table.setItem(row, 7, error_item)
    
    def get_status_text(self, status: TaskStatus) -> str:
        """获取状态文本"""
        status_map = {
            TaskStatus.PENDING: "⏳ 等待中",
            TaskStatus.PROCESSING: "🔄 处理中",
            TaskStatus.COMPLETED: "✅ 已完成",
            TaskStatus.FAILED: "❌ 失败",
            TaskStatus.CANCELLED: "🚫 已取消"
        }
        return status_map.get(status, "未知")
    
    def get_status_color(self, status: TaskStatus) -> QColor:
        """获取状态颜色"""
        color_map = {
            TaskStatus.PENDING: QColor("#fff3cd"),      # 黄色
            TaskStatus.PROCESSING: QColor("#cce5ff"),   # 蓝色
            TaskStatus.COMPLETED: QColor("#d4edda"),    # 绿色
            TaskStatus.FAILED: QColor("#f8d7da"),       # 红色
            TaskStatus.CANCELLED: QColor("#e2e3e5")     # 灰色
        }
        return color_map.get(status, QColor("#ffffff"))
    
    def update_stats_display(self):
        """更新统计显示"""
        stats = self.task_manager.get_stats()
        stats_text = (
            f"总计: {stats['total_tasks']} | "
            f"等待: {stats['pending_tasks']} | "
            f"处理中: {stats['processing_tasks']} | "
            f"已完成: {stats['completed_tasks']} | "
            f"失败: {stats['failed_tasks']}"
        )
        self.stats_label.setText(stats_text)
    
    def on_checkbox_changed(self, task_id: str, state: int):
        """复选框状态改变"""
        if state == Qt.Checked:
            self.selected_task_ids.add(task_id)
        else:
            self.selected_task_ids.discard(task_id)
        
        self.delete_selected_btn.setEnabled(len(self.selected_task_ids) > 0)
    
    def on_selection_changed(self):
        """选择改变"""
        selected_rows = set()
        for item in self.task_table.selectedItems():
            selected_rows.add(item.row())
        
        if selected_rows:
            row = list(selected_rows)[0]
            task_id_item = self.task_table.item(row, 1)
            if task_id_item:
                # 发射任务选中信号
                self.task_selected.emit(task_id_item.text())
    
    def on_cell_double_clicked(self, row: int, column: int):
        """单元格双击"""
        task_id_item = self.task_table.item(row, 1)
        if task_id_item:
            # 显示任务详情
            self.show_task_details(task_id_item.text())
    
    def filter_tasks(self):
        """筛选任务"""
        self.refresh_tasks()
    
    def clear_completed_tasks(self):
        """清理已完成任务"""
        reply = QMessageBox.question(
            self, "确认", "确定要清理所有已完成的任务吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.task_manager.clear_completed_tasks()
            self.refresh_tasks()
            self.status_label.setText("已清理完成任务")
    
    def delete_selected_tasks(self):
        """删除选中任务"""
        if not self.selected_task_ids:
            return
        
        reply = QMessageBox.question(
            self, "确认", f"确定要删除选中的 {len(self.selected_task_ids)} 个任务吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            for task_id in list(self.selected_task_ids):
                self.task_manager.remove_task(task_id)
            
            self.selected_task_ids.clear()
            self.refresh_tasks()
            self.status_label.setText("已删除选中任务")
    
    def show_task_details(self, task_id: str):
        """显示任务详情"""
        # 查找完整的任务ID
        tasks = self.task_manager.get_all_tasks()
        full_task = None
        for task in tasks:
            if task.task_id.endswith(task_id):
                full_task = task
                break
        
        if not full_task:
            QMessageBox.warning(self, "错误", "找不到指定的任务")
            return
        
        # 创建详情对话框
        dialog = TaskDetailDialog(full_task, self)
        dialog.exec_()
    
    def on_task_status_changed(self, task: EmailTask):
        """任务状态改变回调"""
        # 在主线程中更新UI
        QTimer.singleShot(0, self.refresh_tasks)
    
    def on_stats_updated(self, stats: Dict[str, int]):
        """统计更新回调"""
        # 在主线程中更新UI
        QTimer.singleShot(0, self.update_stats_display)

class TaskDetailDialog(QDialog):
    """任务详情对话框"""
    
    def __init__(self, task: EmailTask, parent=None):
        super().__init__(parent)
        self.task = task
        self.setWindowTitle(f"任务详情 - {task.task_id}")
        self.setModal(True)
        self.resize(600, 400)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 创建表单
        form_layout = QFormLayout()
        
        # 任务ID
        form_layout.addRow("任务ID:", QLabel(self.task.task_id))
        
        # 收件人
        form_layout.addRow("收件人:", QLabel(self.task.to_email))
        
        # 主题
        subject_edit = QTextEdit()
        subject_edit.setPlainText(self.task.subject)
        subject_edit.setMaximumHeight(60)
        subject_edit.setReadOnly(True)
        form_layout.addRow("主题:", subject_edit)
        
        # 内容
        content_edit = QTextEdit()
        content_edit.setPlainText(self.task.content)
        content_edit.setReadOnly(True)
        form_layout.addRow("内容:", content_edit)
        
        # 状态
        status_label = QLabel(f"{self.task.status.value} ({self.task.retry_count}/{self.task.max_retries} 重试)")
        form_layout.addRow("状态:", status_label)
        
        # 时间信息
        form_layout.addRow("创建时间:", QLabel(self.task.create_time.strftime("%Y-%m-%d %H:%M:%S")))
        
        if self.task.start_time:
            form_layout.addRow("开始时间:", QLabel(self.task.start_time.strftime("%Y-%m-%d %H:%M:%S")))
        
        if self.task.complete_time:
            form_layout.addRow("完成时间:", QLabel(self.task.complete_time.strftime("%Y-%m-%d %H:%M:%S")))
        
        # 错误信息
        if self.task.error_message:
            error_edit = QTextEdit()
            error_edit.setPlainText(self.task.error_message)
            error_edit.setMaximumHeight(80)
            error_edit.setReadOnly(True)
            form_layout.addRow("错误信息:", error_edit)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
