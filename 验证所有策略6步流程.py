#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证所有发送策略6步流程
检查所有发送策略是否都正确实现了6步完整流程，包含发送成功检查和账号频繁限制处理
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_all_strategies_6_steps():
    """检查所有策略的6步流程实现"""
    print("🔍 检查所有发送策略的6步流程实现")
    print("=" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        from src.core.sina_ultra_fast_sender_final import SinaUltraFastSenderFinal
        import inspect
        
        # 获取所有策略方法
        first_strategy = inspect.getsource(UnifiedEmailSender._send_ultra_fast)
        first_execute = inspect.getsource(UnifiedEmailSender._execute_6_steps_logic)
        first_check = inspect.getsource(UnifiedEmailSender._check_send_success_fast_copy)
        second_strategy = inspect.getsource(UnifiedEmailSender._send_standard)
        third_strategy = inspect.getsource(UnifiedEmailSender._send_safe)
        core_strategy = inspect.getsource(SinaUltraFastSenderFinal._check_send_success_fast)
        
        strategies = [
            ("第一步策略", first_strategy + first_execute + first_check),
            ("第二步策略", second_strategy),
            ("第三步策略", third_strategy),
            ("核心发送器", core_strategy),
        ]
        
        # 检查6步流程特性
        six_steps_features = [
            "6步完整流程",
            "6. 检查是否发送成功",
            "7. 每封邮件开始前都需要重新点击写信按钮",
            "_check_send_success_fast_copy",
            "第6步：检查发送结果",
            "账号频繁限制检测",
            "发送成功确认",
            "_mark_account_for_cooling",
        ]
        
        print("📋 各策略6步流程实现检查:")
        
        all_passed = True
        for strategy_name, strategy_code in strategies:
            print(f"\n🎯 {strategy_name}:")
            strategy_passed = 0
            
            for feature in six_steps_features:
                has_feature = feature in strategy_code
                status = "✅" if has_feature else "❌"
                print(f"  {status} {feature}")
                if has_feature:
                    strategy_passed += 1
            
            strategy_score = strategy_passed / len(six_steps_features)
            print(f"  📊 完整性: {strategy_passed}/{len(six_steps_features)} ({strategy_score:.1%})")
            
            if strategy_score < 0.5:  # 至少要有50%的特性
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_frequency_limit_handling():
    """检查频繁限制处理"""
    print("\n🔍 检查账号频繁限制处理")
    print("=" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        from src.core.sina_ultra_fast_sender_final import SinaUltraFastSenderFinal
        import inspect
        
        # 检查频繁限制处理方法
        unified_check = inspect.getsource(UnifiedEmailSender._check_send_success_fast_copy)
        core_check = inspect.getsource(SinaUltraFastSenderFinal._check_send_success_fast)
        unified_mark = inspect.getsource(UnifiedEmailSender._mark_account_for_cooling)
        core_mark = inspect.getsource(SinaUltraFastSenderFinal._mark_account_for_cooling)
        
        # 检查频繁限制特性
        frequency_features = [
            # 检测关键词
            ("发信过于频繁", "检测关键词: 发信过于频繁"),
            ("请1小时后再试", "检测关键词: 请1小时后再试"),
            ("发送频率过高", "检测关键词: 发送频率过高"),
            ("操作过于频繁", "检测关键词: 操作过于频繁"),
            ("m0.mail.sina.com.cn 显示", "检测关键词: 域名显示"),
            
            # 处理逻辑
            ("账号发送过于频繁，需要冷却处理", "处理逻辑: 冷却处理"),
            ("将此账号冷却60分钟，立即切换其他账号", "处理建议: 切换账号"),
            ("🧊 账号冷却标记", "冷却标记: 账号冷却"),
            ("⏰ 冷却时间", "冷却时间: 时间记录"),
            ("60 * 60", "冷却时长: 60分钟"),
        ]
        
        print("📋 频繁限制处理特性:")
        
        all_codes = unified_check + core_check + unified_mark + core_mark
        passed = 0
        
        for keyword, desc in frequency_features:
            has_feature = keyword in all_codes
            status = "✅" if has_feature else "❌"
            print(f"  {status} {desc}")
            if has_feature:
                passed += 1
        
        print(f"\n📊 频繁限制处理完整性: {passed}/{len(frequency_features)}")
        
        return passed == len(frequency_features)
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_success_detection():
    """检查成功检测"""
    print("\n🔍 检查发送成功检测")
    print("=" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        from src.core.sina_ultra_fast_sender_final import SinaUltraFastSenderFinal
        import inspect
        
        # 检查成功检测方法
        unified_check = inspect.getsource(UnifiedEmailSender._check_send_success_fast_copy)
        core_check = inspect.getsource(SinaUltraFastSenderFinal._check_send_success_fast)
        
        # 检查成功检测特性
        success_features = [
            # 成功关键词
            ("您的邮件已发送", "成功关键词: 您的邮件已发送"),
            ("此邮件发送成功，并已保存到", "成功关键词: 发送成功并保存"),
            ("邮件发送成功", "成功关键词: 邮件发送成功"),
            ("发送成功", "成功关键词: 发送成功"),
            
            # 成功处理
            ("第6步成功：发送成功确认", "成功日志: 第6步成功"),
            ("return True", "返回值: True表示成功"),
            
            # 错误检测
            ("发送失败", "错误关键词: 发送失败"),
            ("网络错误", "错误关键词: 网络错误"),
            ("第6步失败：发送错误", "错误日志: 第6步失败"),
        ]
        
        print("📋 成功检测特性:")
        
        all_codes = unified_check + core_check
        passed = 0
        
        for keyword, desc in success_features:
            has_feature = keyword in all_codes
            status = "✅" if has_feature else "❌"
            print(f"  {status} {desc}")
            if has_feature:
                passed += 1
        
        print(f"\n📊 成功检测完整性: {passed}/{len(success_features)}")
        
        return passed >= len(success_features) * 0.8  # 至少80%的特性
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_strategies_upgrade():
    """分析策略升级"""
    print("\n🔍 分析策略升级")
    print("=" * 60)
    
    print("📋 所有策略升级为6步流程:")
    
    strategies_info = [
        {
            "name": "第一步策略 (ultra_fast)",
            "description": "6步完整流程 - 超快发送模式",
            "features": [
                "完整复制multi_browser_sender_flow_test.py的完美流程",
                "每封邮件重新点击写信按钮",
                "执行完整的6步逻辑",
                "智能发送结果检查",
                "账号频繁限制处理",
                "成功计数更新"
            ]
        },
        {
            "name": "第二步策略 (standard)",
            "description": "6步完整流程 - 标准发送模式",
            "features": [
                "标准发送模式的6步流程",
                "每封邮件重新点击写信按钮",
                "使用元素操作发送",
                "智能发送结果检查",
                "账号频繁限制处理",
                "成功计数更新"
            ]
        },
        {
            "name": "第三步策略 (safe)",
            "description": "6步完整流程 - 安全发送模式",
            "features": [
                "安全发送模式的6步流程",
                "每封邮件重新点击写信按钮",
                "额外等待确保页面稳定",
                "智能发送结果检查",
                "账号频繁限制处理",
                "成功计数更新"
            ]
        },
        {
            "name": "核心发送器 (SinaUltraFastSenderFinal)",
            "description": "增强的发送结果检查",
            "features": [
                "第6步：检查发送结果",
                "优先检查账号频繁限制",
                "检查发送成功消息",
                "URL变化检查",
                "界面元素检查",
                "账号冷却标记"
            ]
        }
    ]
    
    for strategy in strategies_info:
        print(f"\n🎯 {strategy['name']}")
        print(f"   📝 {strategy['description']}")
        for feature in strategy['features']:
            print(f"   ✅ {feature}")

def predict_upgrade_results():
    """预测升级结果"""
    print("\n🔍 预测升级结果")
    print("=" * 60)
    
    print("📋 升级前的问题:")
    print("  ❌ 只有第一步策略有完整的第6步检查")
    print("  ❌ 第二步和第三步策略缺少账号频繁限制处理")
    print("  ❌ 核心发送器缺少账号频繁限制检测")
    print("  ❌ 系统无法统一处理账号限制问题")
    
    print("\n📋 升级后的预期:")
    print("  ✅ 所有策略都具备完整的6步流程")
    print("  ✅ 统一的账号频繁限制处理")
    print("  ✅ 智能的账号冷却和切换")
    print("  ✅ 提高多账号发送系统的稳定性")
    
    print("\n📋 预期的执行效果:")
    print("  第一步策略:")
    print("    ✅ 保持原有的超快发送速度")
    print("    ✅ 完整的6步流程和账号管理")
    
    print("\n  第二步策略:")
    print("    ✅ 标准发送模式 + 6步流程")
    print("    ✅ 智能账号频繁限制处理")
    print("    ✅ 自动切换到第三步策略或切换账号")
    
    print("\n  第三步策略:")
    print("    ✅ 安全发送模式 + 6步流程")
    print("    ✅ 最后的账号频繁限制检查")
    print("    ✅ 确保账号状态的最终确认")
    
    print("\n  核心发送器:")
    print("    ✅ 增强的发送结果检查")
    print("    ✅ 完整的账号频繁限制检测")
    print("    ✅ 智能的账号冷却标记")

def main():
    """主函数"""
    print("🎯 验证所有发送策略6步流程")
    print("目标：确认所有发送策略都正确实现了6步完整流程")
    print("范围：第一步策略、第二步策略、第三步策略、核心发送器")
    print("=" * 80)
    
    # 检查所有策略的6步流程
    strategies_ok = check_all_strategies_6_steps()
    
    # 检查频繁限制处理
    frequency_ok = check_frequency_limit_handling()
    
    # 检查成功检测
    success_ok = check_success_detection()
    
    # 分析策略升级
    analyze_strategies_upgrade()
    
    # 预测升级结果
    predict_upgrade_results()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 所有策略6步流程验证结果")
    print("=" * 80)
    
    if strategies_ok and frequency_ok and success_ok:
        print("🎉 所有发送策略6步流程实现成功！")
        print("✅ 所有策略都具备6步完整流程")
        print("✅ 统一的频繁限制处理")
        print("✅ 完整的成功检测")
        print("✅ 智能的账号管理")
        
        print("\n🎯 升级的关键价值:")
        print("  1. 🔄 统一的6步流程 - 所有策略都具备相同的完整流程")
        print("  2. 🚨 统一的账号管理 - 所有策略都能处理账号频繁限制")
        print("  3. 🧊 智能账号冷却 - 自动标记和切换被限制的账号")
        print("  4. 📊 完整状态反馈 - 详细的发送结果检查和日志")
        print("  5. 🛡️ 系统稳定性 - 提高多账号发送系统的整体稳定性")
        
        print("\n🚀 预期效果:")
        print("现在整个发送系统具备:")
        print("  ✅ 统一的6步完整流程")
        print("  ✅ 智能的账号频繁限制处理")
        print("  ✅ 自动的账号冷却和切换")
        print("  ✅ 高度的系统稳定性和可靠性")
        
    else:
        print("❌ 部分策略6步流程实现不完整")
        if not strategies_ok:
            print("❌ 策略基础实现检查失败")
        if not frequency_ok:
            print("❌ 频繁限制处理检查失败")
        if not success_ok:
            print("❌ 成功检测检查失败")
    
    return 0 if (strategies_ok and frequency_ok and success_ok) else 1

if __name__ == "__main__":
    sys.exit(main())
