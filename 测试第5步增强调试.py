#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试第5步增强调试功能
验证新增的详细调试信息能否帮助定位发送按钮问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_enhanced_debugging():
    """检查增强的调试功能"""
    print("🔍 检查第5步增强调试功能")
    print("=" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        step5_source = inspect.getsource(UnifiedEmailSender._step5_click_send)
        
        # 检查增强调试特性
        debug_features = [
            ("页面所有按钮" in step5_source, "页面按钮全面检查"),
            ("querySelectorAll('input')" in step5_source, "检查所有input元素"),
            ("querySelectorAll('button')" in step5_source, "检查所有button元素"),
            ("input.type === 'submit'" in step5_source, "过滤submit类型"),
            ("input.type === 'button'" in step5_source, "过滤button类型"),
            ("offsetParent !== null" in step5_source, "可见性检查"),
            ("searchLog" in step5_source, "搜索日志记录"),
            ("方法1找到" in step5_source, "方法1调试信息"),
            ("方法2找到" in step5_source, "方法2调试信息"),
            ("方法3找到" in step5_source, "方法3调试信息"),
            ("方法4找到" in step5_source, "方法4调试信息"),
            ("最终找到发送按钮" in step5_source, "最终结果日志"),
            ("所有方法都未找到" in step5_source, "失败情况日志")
        ]
        
        print("📋 增强调试特性:")
        passed = 0
        for check, desc in debug_features:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            if check:
                passed += 1
        
        print(f"\n📊 调试功能完整性: {passed}/{len(debug_features)}")
        
        return passed == len(debug_features)
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_debugging_strategy():
    """分析调试策略"""
    print("\n🔍 分析增强调试策略")
    print("=" * 60)
    
    print("📋 调试策略层次:")
    print("  1. 🔍 页面元素全面扫描")
    print("     - 扫描所有input元素（type=submit/button）")
    print("     - 扫描所有button元素")
    print("     - 记录每个元素的详细属性")
    print("     - 检查可见性状态")
    
    print("\n  2. 🎯 分步骤按钮查找")
    print("     - 方法1: input[type='submit'][value='发送']")
    print("     - 方法2: input[value='发送']")
    print("     - 方法3: 遍历button查找包含'发送'文本")
    print("     - 方法4: 任何input[type='submit']")
    
    print("\n  3. 📝 详细日志记录")
    print("     - 每个方法的查找结果")
    print("     - 找到元素但不可见的情况")
    print("     - 最终选择的按钮详细信息")
    print("     - 所有方法失败的完整记录")
    
    print("\n  4. 🎯 问题定位能力")
    print("     - 确定页面是否有发送按钮")
    print("     - 确定按钮是否可见")
    print("     - 确定选择器是否正确")
    print("     - 确定点击操作是否成功")

def predict_debugging_results():
    """预测调试结果"""
    print("\n🔍 预测可能的调试结果")
    print("=" * 60)
    
    print("📋 可能的调试场景:")
    
    print("\n  场景1: 找到发送按钮但不可见")
    print("    📋 页面所有按钮: [{'type': 'input', 'inputType': 'submit', 'value': '发送', 'visible': False}]")
    print("    🔍 发送按钮搜索日志: ['方法1找到: input[type=\"submit\"][value=\"发送\"]', '方法1失败: 按钮不可见']")
    print("    💡 解决方案: 需要等待页面加载完成或滚动到按钮位置")
    
    print("\n  场景2: 发送按钮文本不是'发送'")
    print("    📋 页面所有按钮: [{'type': 'input', 'inputType': 'submit', 'value': 'Send', 'visible': True}]")
    print("    🔍 发送按钮搜索日志: ['方法1失败: 未找到input[type=\"submit\"][value=\"发送\"]', '方法4成功: 任何提交按钮可见']")
    print("    💡 解决方案: 按钮文本可能是英文或其他文字")
    
    print("\n  场景3: 发送按钮是button元素")
    print("    📋 页面所有按钮: [{'type': 'button', 'text': '发送邮件', 'visible': True}]")
    print("    🔍 发送按钮搜索日志: ['方法3找到: button包含\"发送\"文本', '方法3成功: 按钮可见']")
    print("    💡 解决方案: 成功找到button类型的发送按钮")
    
    print("\n  场景4: 页面没有发送按钮")
    print("    📋 页面所有按钮: [{'type': 'input', 'inputType': 'button', 'value': '取消', 'visible': True}]")
    print("    🔍 发送按钮搜索日志: ['所有方法都未找到可用的发送按钮']")
    print("    💡 解决方案: 页面可能还没有加载完成，或者需要先完成其他步骤")

def main():
    """主函数"""
    print("🎯 测试第5步增强调试功能")
    print("目标：通过详细调试信息定位发送按钮查找失败的原因")
    print("策略：全面扫描页面按钮 + 分步骤查找 + 详细日志记录")
    print("=" * 80)
    
    # 检查增强调试功能
    debug_ok = check_enhanced_debugging()
    
    # 分析调试策略
    analyze_debugging_strategy()
    
    # 预测调试结果
    predict_debugging_results()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 增强调试功能评估")
    print("=" * 80)
    
    if debug_ok:
        print("🎉 第5步增强调试功能完整！")
        print("✅ 页面按钮全面扫描功能已添加")
        print("✅ 分步骤查找逻辑已实现")
        print("✅ 详细日志记录已完善")
        print("✅ 问题定位能力已增强")
        
        print("\n🎯 调试优势:")
        print("  1. 🔍 全面性 - 扫描页面所有相关按钮")
        print("  2. 📝 详细性 - 记录每个查找步骤的结果")
        print("  3. 🎯 精确性 - 区分找到但不可见vs完全找不到")
        print("  4. 💡 指导性 - 提供问题解决方向")
        
        print("\n🚀 下一步:")
        print("请重新运行多浏览器发送测试，观察第5步的详细调试日志")
        print("根据日志信息确定发送按钮查找失败的具体原因")
        
        print("\n📋 关注的日志信息:")
        print("  📋 页面所有按钮: [...] - 查看页面实际有哪些按钮")
        print("  🔍 发送按钮搜索日志: [...] - 查看每个方法的查找结果")
        print("  ✅/❌ 最终结果 - 确定是否成功找到并点击按钮")
        
    else:
        print("❌ 增强调试功能不完整")
        print("需要检查代码实现")
    
    return 0 if debug_ok else 1

if __name__ == "__main__":
    sys.exit(main())
