#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新浪邮箱自动化程序 - 主程序入口
功能强大的Windows桌面应用程序，实现新浪邮箱的批量自动化操作

主要功能:
1. 批量导入新浪邮箱账号和密码
2. 为每个账号配置独立的代理IP
3. 自动在浏览器中登录新浪邮箱
4. 按模板自动发送邮件
5. 实时监控文件夹中的txt文件，提取QQ号码并转换为邮箱
6. 智能轮换发送账号，实现自动化邮件发送

作者: AI Assistant
版本: 1.0.0
创建时间: 2025-07-31
"""

import sys
import os
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PyQt5.QtWidgets import QApplication, QMessageBox
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QIcon
    
    from src.utils.logger import setup_logger
    from src.utils.config_manager import ConfigManager
    from src.gui.main_window import MainWindow
    
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所有必要的依赖包:")
    print("pip install -r requirements.txt")
    sys.exit(1)


def setup_application():
    """设置应用程序基本配置"""
    # 设置高DPI支持
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # 创建应用程序实例
    app = QApplication(sys.argv)
    app.setApplicationName("新浪邮箱自动化程序")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("AI Assistant")
    
    # 设置应用程序图标
    icon_path = project_root / "resources" / "icon.ico"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    return app


def check_environment():
    """检查运行环境"""
    # 检查Python版本
    if sys.version_info < (3, 7):
        QMessageBox.critical(
            None, 
            "版本错误", 
            "此程序需要Python 3.7或更高版本\n"
            f"当前版本: {sys.version}"
        )
        return False
    
    # 检查必要的目录
    required_dirs = ["config", "data", "logs", "resources"]
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
    
    return True


def main():
    """主函数 - 增强错误处理版本"""
    logger = None
    app = None

    try:
        # 检查运行环境
        if not check_environment():
            return 1

        # 设置日志系统
        logger = setup_logger()
        logger.info("=" * 50)
        logger.info("新浪邮箱自动化程序启动")
        logger.info("=" * 50)

        # 加载配置
        config_manager = ConfigManager()
        config = config_manager.load_config()
        logger.info("配置文件加载完成")

        # 创建应用程序
        app = setup_application()
        logger.info("应用程序初始化完成")

        # 设置全局异常处理器
        def handle_exception(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                # 允许Ctrl+C正常退出
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return

            error_msg = f"程序运行时异常: {exc_value}\n\n详细错误信息:\n{''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))}"
            if logger:
                logger.error(error_msg)
            else:
                print(error_msg)

            # 显示错误对话框但不退出程序
            try:
                if app:
                    QMessageBox.critical(None, "运行时错误",
                                       f"程序遇到错误但将继续运行:\n\n{exc_value}\n\n"
                                       f"详细信息已记录到日志文件。\n"
                                       f"如果问题持续，请重启程序。")
            except:
                pass

        # 设置异常处理器
        sys.excepthook = handle_exception

        # 创建主窗口
        main_window = MainWindow(config)
        main_window.show()
        logger.info("主窗口创建完成")

        # 运行应用程序
        logger.info("应用程序开始运行")
        exit_code = app.exec_()

        logger.info(f"应用程序正常退出，退出码: {exit_code}")
        return exit_code

    except Exception as e:
        error_msg = f"程序启动失败: {str(e)}\n\n详细错误信息:\n{traceback.format_exc()}"

        if logger:
            logger.error(error_msg)
        else:
            print(error_msg)

        # 尝试显示错误对话框
        try:
            if app is None:
                app = QApplication(sys.argv)
            QMessageBox.critical(None, "启动错误",
                               f"程序启动失败:\n\n{str(e)}\n\n"
                               f"请检查:\n"
                               f"• 是否安装了所有依赖包\n"
                               f"• 配置文件是否正确\n"
                               f"• 系统权限是否足够")
        except:
            pass

        return 1


if __name__ == "__main__":
    sys.exit(main())
