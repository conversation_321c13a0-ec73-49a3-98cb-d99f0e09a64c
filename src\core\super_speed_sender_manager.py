#!/usr/bin/env python3
"""
超级发送流程管理器 - 集成所有成功经验
基于多浏览器发送邮件经验，实现超级发送流程4-5秒或更快
集成：超极速Cookie登录、超极速写信按钮、超极速发送器、快速重置机制
"""

import time
import threading
import queue
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from src.utils.logger import setup_logger
from src.models.account import Account
from src.models.browser_instance import BrowserInstance
from src.core.multi_browser_manager import MultiBrowserManager, SendingConfig
from src.core.ultra_speed_cookie_manager import UltraSpeedCookieManager, UltraSpeedComposeManager
from src.core.sina_ultra_fast_sender_final import SinaUltraFastSenderFinal

logger = setup_logger("INFO")

@dataclass
class SuperSpeedTask:
    """超级发送任务"""
    task_id: str
    to_email: str
    subject: str
    content: str
    priority: int = 1
    created_time: float = 0
    status: str = "pending"
    retry_count: int = 0
    max_retries: int = 1

@dataclass
class SuperSpeedStats:
    """超级发送统计"""
    total_tasks: int = 0
    sent_success: int = 0
    sent_failed: int = 0
    pending_tasks: int = 0
    start_time: float = 0
    avg_send_time: float = 0
    fastest_send_time: float = 999
    slowest_send_time: float = 0
    emails_per_minute: float = 0

class SuperSpeedSenderManager:
    """超级发送流程管理器 - 集成所有成功经验"""
    
    def __init__(self, config: SendingConfig):
        # 超级发送优化配置
        self.config = config
        self.config.send_interval = min(self.config.send_interval, 0.5)  # 最大0.5秒间隔
        
        # 核心组件
        self.browser_manager = MultiBrowserManager(config)
        self.ultra_cookie_manager: Optional[UltraSpeedCookieManager] = None
        
        # 超级发送器缓存
        self.super_senders: Dict[str, SinaUltraFastSenderFinal] = {}
        self.compose_managers: Dict[str, UltraSpeedComposeManager] = {}
        
        # 统计和状态
        self.stats = SuperSpeedStats()
        self.is_running = False
        self.is_stopping = False  # 停止标志
        self.send_lock = threading.Lock()
        self.last_send_times: Dict[str, float] = {}
        self.active_threads = []  # 活动线程列表

        # 多浏览器并发任务队列
        self.task_queue = queue.Queue()
        self.browser_workers = {}  # 浏览器工作线程
        self.max_concurrent_browsers = 5  # 最大并发浏览器数量
        
        logger.info("⚡ 超级发送流程管理器初始化完成")
    
    def initialize(self, accounts: List[Account]) -> bool:
        """超级发送流程初始化"""
        try:
            logger.info("🚀 超级发送流程初始化...")
            
            # 1. 初始化浏览器管理器
            logger.info(f"🔧 开始初始化浏览器管理器，配置的浏览器数量: {self.browser_manager.config.max_browsers}")

            if not self.browser_manager.initialize_browsers():
                logger.error("❌ 浏览器初始化失败")
                return False

            logger.info(f"✅ 浏览器管理器初始化成功，实际创建的浏览器数量: {len(self.browser_manager.browsers)}")
            
            # 2. 设置账号队列
            self.browser_manager.set_account_queue(accounts)
            
            # 3. 初始化超极速Cookie管理器
            if hasattr(self.browser_manager, 'ultra_cookie_manager'):
                self.ultra_cookie_manager = self.browser_manager.ultra_cookie_manager
            else:
                from src.core.ultra_speed_cookie_manager import UltraSpeedCookieManager
                self.ultra_cookie_manager = UltraSpeedCookieManager(self.browser_manager.cookie_manager)
            
            # 4. 预加载所有账号的Cookie
            account_emails = [account.email for account in accounts]
            self.ultra_cookie_manager.preload_cookies(account_emails)
            logger.info(f"🔥 预加载了 {len(account_emails)} 个账号的Cookie")
            
            # 5. 为每个浏览器加载初始账号并预热
            self._super_speed_preheat_browsers(accounts)

            # 6. 启动多浏览器并发工作线程
            self._start_concurrent_browser_workers()

            # 标记为运行状态
            self.is_running = True
            self.stats.start_time = time.time()
            logger.info("✅ 超级发送流程初始化完成，多浏览器并发已启动")
            return True
            
        except Exception as e:
            logger.error(f"❌ 超级发送流程初始化失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

    def _start_concurrent_browser_workers(self):
        """启动多浏览器并发工作线程"""
        try:
            logger.info("🚀 启动多浏览器并发工作线程...")

            # 获取所有可用浏览器
            available_browsers = list(self.browser_manager.browsers.values())
            logger.info(f"🔍 检查浏览器实例: 总数={len(available_browsers)}, 最大并发={self.max_concurrent_browsers}")

            if not available_browsers:
                logger.error("❌ 没有可用的浏览器实例！无法启动工作线程")
                logger.error("🔍 浏览器管理器状态:")
                logger.error(f"  - 浏览器字典: {list(self.browser_manager.browsers.keys())}")
                logger.error(f"  - 配置的最大浏览器数: {self.browser_manager.config.max_browsers}")
                raise Exception("没有可用的浏览器实例，无法启动并发工作线程")

            concurrent_count = min(len(available_browsers), self.max_concurrent_browsers)

            logger.info(f"📊 启动 {concurrent_count} 个并发浏览器工作线程")

            # 为每个浏览器启动工作线程
            for i, browser_instance in enumerate(available_browsers[:concurrent_count]):
                worker_thread = threading.Thread(
                    target=self._browser_worker_thread,
                    args=(browser_instance,),
                    daemon=True,
                    name=f"BrowserWorker-{browser_instance.browser_id}"
                )

                self.browser_workers[browser_instance.browser_id] = worker_thread
                worker_thread.start()

                logger.info(f"✅ 启动浏览器工作线程: {browser_instance.browser_id}")

            logger.info(f"🎉 多浏览器并发工作线程启动完成，共 {len(self.browser_workers)} 个线程")

        except Exception as e:
            logger.error(f"❌ 启动多浏览器并发工作线程失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            raise

    def _browser_worker_thread(self, browser_instance):
        """浏览器工作线程 - 持续从队列获取任务并发送（增强版）"""
        worker_name = f"Worker-{browser_instance.browser_id}"
        logger.info(f"🔧 {worker_name} 启动")

        consecutive_failures = 0
        max_consecutive_failures = 3

        while self.is_running and not self.is_stopping:
            try:
                # 从任务队列获取任务（超时1秒）
                try:
                    task = self.task_queue.get(timeout=1.0)
                except queue.Empty:
                    continue

                # 检查浏览器会话是否有效
                if not self._check_browser_session(browser_instance):
                    logger.warning(f"⚠️ {worker_name} 浏览器会话失效，尝试恢复...")
                    if self._recover_browser_session(browser_instance):
                        logger.info(f"✅ {worker_name} 浏览器会话恢复成功")
                        consecutive_failures = 0
                    else:
                        logger.error(f"❌ {worker_name} 浏览器会话恢复失败")
                        consecutive_failures += 1

                        # 将任务放回队列
                        self.task_queue.put(task)

                        if consecutive_failures >= max_consecutive_failures:
                            logger.error(f"❌ {worker_name} 连续失败 {consecutive_failures} 次，暂停工作")
                            time.sleep(10)  # 暂停10秒
                            consecutive_failures = 0
                        else:
                            time.sleep(2)  # 短暂等待
                        continue

                # 检查浏览器状态
                if browser_instance.status != "ready":
                    # 浏览器不可用，将任务放回队列
                    self.task_queue.put(task)
                    time.sleep(0.5)
                    continue

                # 检查发送间隔
                current_time = time.time()
                last_send_time = self.last_send_times.get(browser_instance.browser_id, 0)
                if current_time - last_send_time < self.config.send_interval:
                    # 还没到发送时间，将任务放回队列
                    self.task_queue.put(task)
                    time.sleep(0.1)
                    continue

                # 执行发送任务
                logger.info(f"🚀 {worker_name} 开始处理任务: {task.task_id} -> {task.to_email}")

                start_time = time.time()
                success = self.send_email_super_speed(browser_instance, task)
                elapsed = time.time() - start_time

                if success:
                    logger.info(f"✅ {worker_name} 任务完成: {task.task_id} ({elapsed:.2f}s)")
                    consecutive_failures = 0  # 重置失败计数
                    self.stats.sent_success += 1
                else:
                    logger.error(f"❌ {worker_name} 任务失败: {task.task_id}")
                    consecutive_failures += 1
                    self.stats.sent_failed += 1

                # 更新统计
                self.stats.pending_tasks = max(0, self.stats.pending_tasks - 1)

                # 标记任务完成
                self.task_queue.task_done()

            except Exception as e:
                logger.error(f"❌ {worker_name} 处理任务异常: {e}")
                consecutive_failures += 1

                # 确保任务被标记为完成
                try:
                    self.task_queue.task_done()
                except:
                    pass

        logger.info(f"🛑 {worker_name} 结束")

    def _check_browser_session(self, browser_instance) -> bool:
        """检查浏览器会话是否有效"""
        try:
            if not browser_instance.driver:
                return False

            # 尝试获取当前URL来检查会话是否有效
            current_url = browser_instance.driver.current_url

            # 检查是否是有效的新浪邮箱页面
            if "mail.sina.com" in current_url or "sina.com" in current_url:
                return True
            else:
                logger.warning(f"⚠️ 浏览器 {browser_instance.browser_id} 不在新浪邮箱页面: {current_url}")
                return False

        except Exception as e:
            logger.error(f"❌ 检查浏览器会话失败 {browser_instance.browser_id}: {e}")
            return False

    def _recover_browser_session(self, browser_instance) -> bool:
        """恢复浏览器会话"""
        try:
            logger.info(f"🔄 开始恢复浏览器会话: {browser_instance.browser_id}")

            # 1. 尝试重新加载当前账号
            if browser_instance.current_account:
                logger.info(f"🔐 重新登录账号: {browser_instance.current_account.email}")

                if self.browser_manager.load_account_cookies(browser_instance, browser_instance.current_account):
                    logger.info(f"✅ 账号重新登录成功: {browser_instance.current_account.email}")

                    # 2. 重新创建发送器
                    browser_id = browser_instance.browser_id
                    if browser_id in self.super_senders:
                        del self.super_senders[browser_id]

                    # 重新预热发送器
                    self._create_and_preheat_sender(browser_instance)

                    return True
                else:
                    logger.error(f"❌ 账号重新登录失败: {browser_instance.current_account.email}")

            # 3. 如果当前账号失败，尝试切换到新账号
            logger.info(f"🔄 尝试切换到新账号: {browser_instance.browser_id}")
            if self.browser_manager.switch_account_if_needed(browser_instance, force_switch=True):
                logger.info(f"✅ 成功切换到新账号: {browser_instance.browser_id}")

                # 重新预热发送器
                self._create_and_preheat_sender(browser_instance)
                return True

            logger.error(f"❌ 浏览器会话恢复失败: {browser_instance.browser_id}")
            return False

        except Exception as e:
            logger.error(f"❌ 恢复浏览器会话异常 {browser_instance.browser_id}: {e}")
            return False

    def _super_speed_preheat_browsers(self, accounts):
        """超级发送流程预热浏览器"""
        try:
            logger.info("🔥 超级发送流程预热浏览器...")
            
            for browser_id, browser_instance in self.browser_manager.browsers.items():
                # 为每个浏览器加载初始账号
                account = self.browser_manager.get_next_account()
                if account:
                    # 使用超极速Cookie登录
                    if self.browser_manager.load_account_cookies(browser_instance, account):
                        logger.info(f"⚡ 浏览器 {browser_id} 超极速登录成功: {account.email}")
                        
                        # 预创建和预热发送器
                        self._create_and_preheat_sender(browser_instance)
                    else:
                        logger.warning(f"⚠️ 浏览器 {browser_id} 超极速登录失败: {account.email}")
                else:
                    logger.warning(f"⚠️ 没有账号为浏览器 {browser_id} 预热")
            
            logger.info("🔥 超级发送流程预热完成")
            
        except Exception as e:
            logger.error(f"❌ 超级发送流程预热失败: {e}")
    
    def _create_and_preheat_sender(self, browser_instance: BrowserInstance):
        """创建和预热发送器"""
        try:
            browser_id = browser_instance.browser_id
            
            # 创建超极速发送器
            sender = SinaUltraFastSenderFinal(browser_instance.driver)
            
            # 创建超极速写信按钮管理器
            compose_manager = UltraSpeedComposeManager(browser_instance.driver)
            
            # 预热：准备写邮件界面
            if sender.prepare_compose_page():
                self.super_senders[browser_id] = sender
                self.compose_managers[browser_id] = compose_manager
                browser_instance.status = "ready"
                logger.info(f"⚡ 发送器预热成功: {browser_id}")
            else:
                logger.warning(f"⚠️ 发送器预热失败: {browser_id}")
                
        except Exception as e:
            logger.error(f"❌ 发送器预热异常: {e}")
    
    def send_email_super_speed(self, browser_instance: BrowserInstance, task: SuperSpeedTask) -> bool:
        """超级发送流程 - 集成所有成功经验"""
        start_time = time.time()
        
        try:
            logger.info(f"⚡ 超级发送流程: {task.to_email}")
            
            browser_id = browser_instance.browser_id
            
            # 获取预热的发送器
            sender = self.super_senders.get(browser_id)
            if not sender:
                logger.warning(f"⚠️ 未找到预热发送器，创建新的: {browser_id}")
                sender = SinaUltraFastSenderFinal(browser_instance.driver)
                
                # 快速准备
                if not sender.prepare_compose_page():
                    logger.error(f"❌ 发送器准备失败: {browser_id}")
                    return False
                
                self.super_senders[browser_id] = sender
            
            # 设置浏览器状态
            browser_instance.status = "busy"
            
            # 超级发送流程 - 使用第一步策略（5步逻辑复刻）
            from src.core.unified_email_sender import UnifiedEmailSender, SendingStrategy

            # 创建统一发送器，使用第一步策略
            unified_sender = UnifiedEmailSender(browser_instance.driver, SendingStrategy.ULTRA_FAST)

            # 使用第一步策略发送（严格按照5步顺序）
            result = unified_sender.send_email(
                task.to_email,
                task.subject,
                task.content
            )

            success = result.success
            
            if success:
                # 更新统计
                browser_instance.sent_count += 1
                browser_instance.last_activity = time.time()
                browser_instance.status = "ready"

                self.last_send_times[browser_id] = time.time()

                elapsed = time.time() - start_time

                # 更新超级发送统计
                self._update_super_speed_stats(elapsed)

                logger.info(f"⚡ 超级发送成功: {task.task_id} ({elapsed:.2f}秒)")

                # 检查是否需要切换账号
                current_sent_count = browser_instance.sent_count
                emails_per_account = self.browser_manager.config.emails_per_account
                account_switched = False

                logger.info(f"📊 账号状态检查: {browser_id} - 已发送: {current_sent_count}, 限制: {emails_per_account}")

                # 检查是否达到账号切换条件
                if (browser_instance.current_account and
                    current_sent_count >= emails_per_account):
                    logger.info(f"🔄 达到账号切换条件，尝试切换账号: {browser_id}")

                    # 尝试切换账号
                    if self.browser_manager.switch_account_if_needed(browser_instance):
                        # 检查是否真的切换了账号（发送计数被重置）
                        if browser_instance.sent_count == 0:
                            account_switched = True
                            logger.info(f"⚡ 账号切换成功，重新准备发送器: {browser_id}")
                            # 移除旧的发送器，下次会创建新的
                            if browser_id in self.super_senders:
                                del self.super_senders[browser_id]
                        else:
                            logger.info(f"⚡ 账号切换检查完成，继续使用当前账号: {browser_id}")
                    else:
                        logger.warning(f"⚠️ 账号切换失败: {browser_id}")
                else:
                    logger.info(f"📊 未达到账号切换条件: {browser_id} ({current_sent_count}/{emails_per_account})")

                # 如果没有切换账号，使用快速重置机制（同一账号连续发送）
                if not account_switched:
                    logger.info(f"⚡ 同一账号连续发送，执行快速重置: {browser_id}")
                    if hasattr(sender, 'quick_reset_for_continuous_sending'):
                        logger.info(f"🔄 开始执行快速重置: {browser_id}")
                        reset_success = sender.quick_reset_for_continuous_sending()
                        if reset_success:
                            logger.info(f"✅ 快速重置成功: {browser_id}")
                        else:
                            logger.warning(f"⚠️ 快速重置失败: {browser_id}")
                    else:
                        logger.warning(f"⚠️ 发送器不支持快速重置: {browser_id}")
                else:
                    logger.info(f"🔄 账号已切换，跳过快速重置: {browser_id}")

                return True
            else:
                browser_instance.status = "ready"
                elapsed = time.time() - start_time
                logger.error(f"❌ 超级发送失败: {task.task_id} ({elapsed:.2f}秒)")
                return False
                
        except Exception as e:
            browser_instance.status = "error"
            elapsed = time.time() - start_time
            logger.error(f"❌ 超级发送异常: {task.task_id} ({elapsed:.2f}秒) - {e}")
            return False
    
    def _update_super_speed_stats(self, elapsed_time: float):
        """更新超级发送统计"""
        self.stats.sent_success += 1
        
        # 更新平均发送时间
        if self.stats.sent_success > 0:
            self.stats.avg_send_time = (
                (self.stats.avg_send_time * (self.stats.sent_success - 1) + elapsed_time) / 
                self.stats.sent_success
            )
        else:
            self.stats.avg_send_time = elapsed_time
        
        # 更新最快和最慢时间
        if elapsed_time < self.stats.fastest_send_time:
            self.stats.fastest_send_time = elapsed_time
        
        if elapsed_time > self.stats.slowest_send_time:
            self.stats.slowest_send_time = elapsed_time
        
        # 更新发送速率
        if self.stats.start_time > 0:
            total_elapsed = time.time() - self.stats.start_time
            if total_elapsed > 0:
                self.stats.emails_per_minute = (self.stats.sent_success * 60) / total_elapsed
    
    def get_next_super_speed_browser(self) -> Optional[BrowserInstance]:
        """获取下一个超级发送可用浏览器"""
        current_time = time.time()
        
        for browser_instance in self.browser_manager.browsers.values():
            if browser_instance.status != "ready":
                continue
            
            # 超级发送间隔检查
            last_send_time = self.last_send_times.get(browser_instance.browser_id, 0)
            if current_time - last_send_time < self.config.send_interval:
                continue
            
            return browser_instance
        
        return None
    
    def add_super_speed_task(self, to_email: str, subject: str, content: str) -> str:
        """添加超级发送任务到队列，由多浏览器并发处理"""
        task_id = f"super_{int(time.time() * 1000)}_{self.stats.total_tasks}"

        task = SuperSpeedTask(
            task_id=task_id,
            to_email=to_email,
            subject=subject,
            content=content,
            created_time=time.time()
        )

        self.stats.total_tasks += 1
        self.stats.pending_tasks += 1

        logger.info(f"⚡ 添加超级发送任务到队列: {task_id} -> {to_email}")

        # 添加到任务队列，由多个浏览器工作线程并发处理
        self.task_queue.put(task)

        return task_id

    def get_concurrent_stats(self) -> dict:
        """获取并发统计信息"""
        return {
            'active_workers': len([w for w in self.browser_workers.values() if w.is_alive()]),
            'total_workers': len(self.browser_workers),
            'queue_size': self.task_queue.qsize(),
            'max_concurrent': self.max_concurrent_browsers
        }


    def start_sending(self):
        """启动发送流程 - 确保工作线程正常运行"""
        try:
            logger.info("🚀 启动超级发送流程...")
            
            # 设置运行状态
            self.is_running = True
            self.is_stopping = False
            
            # 检查工作线程状态
            active_workers = len([w for w in self.browser_workers.values() if w.is_alive()])
            total_browsers = len(self.browser_manager.browsers) if self.browser_manager else 0
            
            logger.info(f"📊 工作线程状态: {active_workers}/{total_browsers} 活跃")
            
            # 如果工作线程不足，强制启动
            if active_workers < total_browsers:
                logger.warning("⚠️ 工作线程不足，强制启动...")
                self.force_start_workers()
            
            # 再次检查
            active_workers = len([w for w in self.browser_workers.values() if w.is_alive()])
            logger.info(f"✅ 超级发送流程启动完成，{active_workers} 个工作线程运行中")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动发送流程失败: {e}")
            return False

    def force_start_workers(self):
        """强制启动工作线程 - 紧急修复"""
        try:
            logger.info("🚨 强制启动工作线程...")

            # 检查浏览器状态
            if not hasattr(self, 'browser_manager') or not self.browser_manager:
                logger.error("❌ 浏览器管理器不存在")
                return False

            browsers = getattr(self.browser_manager, 'browsers', {})
            logger.info(f"🔍 检查到 {len(browsers)} 个浏览器实例")

            if not browsers:
                logger.error("❌ 没有浏览器实例，无法启动工作线程")
                return False

            # 强制启动工作线程
            for browser_id, browser_instance in browsers.items():
                if browser_id not in self.browser_workers:
                    logger.info(f"🚀 强制启动浏览器工作线程: {browser_id}")

                    worker_thread = threading.Thread(
                        target=self._browser_worker_thread,
                        args=(browser_instance,),
                        daemon=True,
                        name=f"ForceWorker-{browser_id}"
                    )

                    self.browser_workers[browser_id] = worker_thread
                    worker_thread.start()

                    logger.info(f"✅ 工作线程启动成功: {browser_id}")

            logger.info(f"🎉 强制启动完成，共 {len(self.browser_workers)} 个工作线程")
            return True

        except Exception as e:
            logger.error(f"❌ 强制启动工作线程失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def stop_sending(self):
        """停止超级发送流程和所有并发工作线程"""
        logger.info("🛑 开始停止超级发送流程...")

        # 设置停止标志
        self.is_stopping = True
        self.is_running = False

        # 停止所有浏览器工作线程
        logger.info(f"⏳ 停止 {len(self.browser_workers)} 个浏览器工作线程...")

        for browser_id, worker_thread in self.browser_workers.items():
            if worker_thread.is_alive():
                logger.info(f"🛑 停止浏览器工作线程: {browser_id}")
                worker_thread.join(timeout=3)  # 等待3秒
                if worker_thread.is_alive():
                    logger.warning(f"⚠️ 浏览器工作线程 {browser_id} 未能在3秒内完成")

        # 清理工作线程
        self.browser_workers.clear()

        # 清空任务队列
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
                self.task_queue.task_done()
            except queue.Empty:
                break

        # 等待剩余的活动线程完成
        if self.active_threads:
            logger.info(f"⏳ 等待 {len(self.active_threads)} 个剩余活动线程完成...")

            for thread in self.active_threads[:]:  # 创建副本避免修改时出错
                if thread.is_alive():
                    thread.join(timeout=2)  # 每个线程最多等待2秒
                    if thread.is_alive():
                        logger.warning(f"⚠️ 线程 {thread.name} 未能在2秒内完成")

            # 清理线程列表
            self.active_threads.clear()

        logger.info("✅ 超级发送流程和所有并发工作线程已停止")

    def get_super_speed_stats(self) -> SuperSpeedStats:
        """获取超级发送统计"""
        return self.stats
    
    def cleanup(self):
        """清理超级发送资源"""
        # 清理发送器缓存
        for sender in self.super_senders.values():
            try:
                if hasattr(sender, 'cleanup'):
                    sender.cleanup()
            except:
                pass
        
        self.super_senders.clear()
        self.compose_managers.clear()
        
        # 清理Cookie管理器缓存
        if self.ultra_cookie_manager:
            try:
                self.ultra_cookie_manager.clear_cache()
            except:
                pass
        
        # 清理浏览器管理器
        if self.browser_manager:
            try:
                self.browser_manager.cleanup()
            except:
                pass
        
        logger.info("🧹 超级发送流程资源已清理")

    def get_worker_status(self) -> dict:
        """获取工作线程状态"""
        try:
            status = {
                'total_workers': len(self.browser_workers),
                'active_workers': 0,
                'inactive_workers': 0,
                'worker_details': {}
            }

            for browser_id, worker_thread in self.browser_workers.items():
                is_alive = worker_thread.is_alive()
                if is_alive:
                    status['active_workers'] += 1
                else:
                    status['inactive_workers'] += 1

                status['worker_details'][browser_id] = {
                    'thread_name': worker_thread.name,
                    'is_alive': is_alive,
                    'daemon': worker_thread.daemon
                }

            return status

        except Exception as e:
            logger.error(f"❌ 获取工作线程状态失败: {e}")
            return {'error': str(e)}

    def monitor_and_recover(self):
        """监控和恢复工作线程"""
        try:
            status = self.get_worker_status()

            if status.get('error'):
                logger.error(f"❌ 工作线程状态检查失败: {status['error']}")
                return False

            logger.info(f"📊 工作线程状态: {status['active_workers']}/{status['total_workers']} 活跃")

            # 如果有不活跃的工作线程，尝试重启
            if status['inactive_workers'] > 0:
                logger.warning(f"⚠️ 发现 {status['inactive_workers']} 个不活跃工作线程，尝试重启...")

                # 重启不活跃的工作线程
                for browser_id, details in status['worker_details'].items():
                    if not details['is_alive']:
                        logger.info(f"🔄 重启工作线程: {browser_id}")

                        # 获取浏览器实例
                        browser_instance = self.browser_manager.browsers.get(browser_id)
                        if browser_instance:
                            # 创建新的工作线程
                            worker_thread = threading.Thread(
                                target=self._browser_worker_thread,
                                args=(browser_instance,),
                                daemon=True,
                                name=f"RecoveredWorker-{browser_id}"
                            )

                            self.browser_workers[browser_id] = worker_thread
                            worker_thread.start()

                            logger.info(f"✅ 工作线程重启成功: {browser_id}")
                        else:
                            logger.error(f"❌ 找不到浏览器实例: {browser_id}")

            return True

        except Exception as e:
            logger.error(f"❌ 监控和恢复失败: {e}")
            return False
