#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第二阶段成功率优化测试
目标：成功率从0%提升到90%以上
"""

import sys
import os
import time
import tempfile
import sqlite3
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import get_logger
from src.core.enhanced_success_rate_sender import EnhancedSuccessRateSender
from src.core.cookie_manager import CookieManager

logger = get_logger("SuccessRateOptimizationPhase2Test")

class SuccessRateOptimizationPhase2Test:
    """第二阶段成功率优化测试类"""
    
    def __init__(self):
        """初始化测试"""
        self.test_name = "第二阶段成功率优化测试"
        self.browser_drivers = []
        self.test_accounts = [
            {'email': '<EMAIL>', 'name': '账号1'},
            {'email': '<EMAIL>', 'name': '账号2'}
        ]
        self.test_recipients = []
        self.test_emails_per_account = 3  # 每个账号发送3封邮件测试成功率
        self.success_rate_target = 0.9  # 目标：90%成功率
        
        logger.info(f"🧪 {self.test_name} 初始化完成")
        logger.info(f"🎯 成功率优化目标: {self.success_rate_target:.0%}以上")
    
    def load_test_recipients(self):
        """加载测试收件人"""
        try:
            logger.info("📧 加载测试收件人...")
            
            # 连接数据库
            db_path = "data/sina_email_automation.db"
            if not os.path.exists(db_path):
                raise Exception(f"数据库文件不存在: {db_path}")
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询前6个收件人（每个账号3个）
            cursor.execute("""
                SELECT email, name FROM recipient_data 
                WHERE email IS NOT NULL AND email != ''
                LIMIT 6
            """)
            
            rows = cursor.fetchall()
            conn.close()
            
            if not rows:
                raise Exception("没有找到收件人数据")
            
            self.test_recipients = []
            for email, name in rows:
                self.test_recipients.append({
                    'email': email,
                    'name': name or email.split('@')[0]
                })
            
            logger.info(f"✅ 加载了 {len(self.test_recipients)} 个测试收件人")
            for i, recipient in enumerate(self.test_recipients):
                logger.info(f"  收件人{i+1}: {recipient['email']}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载收件人数据失败: {e}")
            return False
    
    def create_stable_browsers(self):
        """创建稳定的浏览器 - 优化稳定性而非速度"""
        try:
            logger.info("🌐 创建稳定浏览器...")
            
            for i, account in enumerate(self.test_accounts):
                logger.info(f"🌐 创建稳定浏览器 {i+1}/{len(self.test_accounts)} - {account['email']}")
                
                # 配置Chrome选项 - 优化稳定性
                chrome_options = Options()
                
                # 基础稳定性参数
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                
                # 稳定性优化参数（减少了一些可能导致不稳定的参数）
                stability_options = [
                    '--disable-background-downloads',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-client-side-phishing-detection',
                    '--disable-sync',
                    '--disable-translate',
                    '--disable-logging',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-background-networking',
                    '--disable-background-mode',
                    '--disable-default-apps',
                    '--disable-hang-monitor',
                    '--disable-prompt-on-repost',
                    '--disable-features=TranslateUI',
                    '--disable-ipc-flooding-protection',
                    '--disable-component-extensions-with-background-pages',
                    '--disable-domain-reliability',
                    '--disable-breakpad',
                    '--disable-component-update',
                    '--disable-print-preview',
                    '--disable-speech-api',
                    '--no-default-browser-check',
                    '--no-pings',
                    '--disable-field-trial-config',
                    '--disable-back-forward-cache',
                    '--force-color-profile=srgb',
                    '--metrics-recording-only',
                    '--disable-dev-tools',
                    '--disable-audio-output',
                    '--disable-notifications'
                ]
                
                for option in stability_options:
                    chrome_options.add_argument(option)
                
                # 设置用户数据目录
                temp_dir = tempfile.gettempdir()
                user_data_dir = os.path.join(temp_dir, f"test_success_chrome_{i}_{int(time.time())}")
                chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
                
                # 设置窗口位置
                window_x = i * 450
                window_y = i * 100
                chrome_options.add_argument(f'--window-position={window_x},{window_y}')
                chrome_options.add_argument('--window-size=1300,900')
                
                # 启用性能日志
                chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
                
                # 创建浏览器
                browser_start_time = time.time()
                driver = webdriver.Chrome(options=chrome_options)
                browser_startup_time = time.time() - browser_start_time
                
                driver.set_page_load_timeout(30)  # 增加页面加载超时
                driver.implicitly_wait(5)         # 适中的隐式等待
                
                # 设置反检测
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                driver.execute_script(f"document.title = '成功率优化测试 - {account['name']}';")
                
                # 导航到新浪邮箱
                page_load_start = time.time()
                driver.get("https://mail.sina.com.cn")
                page_load_time = time.time() - page_load_start
                
                # 应用Cookie
                cookie_start_time = time.time()
                success = self._apply_cookies_and_login(driver, account, i+1)
                cookie_time = time.time() - cookie_start_time
                
                if success:
                    logger.info(f"🔑 浏览器 {i+1} Cookie登录成功: {account['email']}")
                else:
                    logger.warning(f"⚠️ 浏览器 {i+1} Cookie登录失败: {account['email']}")
                
                # 记录启动性能
                logger.info(f"⚡ 浏览器 {i+1} 启动统计:")
                logger.info(f"   浏览器启动: {browser_startup_time:.2f}秒")
                logger.info(f"   页面加载: {page_load_time:.2f}秒")
                logger.info(f"   Cookie应用: {cookie_time:.2f}秒")
                logger.info(f"   总启动时间: {browser_startup_time + page_load_time + cookie_time:.2f}秒")
                
                self.browser_drivers.append({
                    'driver': driver,
                    'account': account,
                    'browser_num': i+1,
                    'startup_time': browser_startup_time + page_load_time + cookie_time
                })
                
                logger.info(f"✅ 稳定浏览器 {i+1} 创建成功")
                time.sleep(1)
            
            logger.info(f"✅ 创建了 {len(self.browser_drivers)} 个稳定浏览器")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建稳定浏览器失败: {e}")
            return False
    
    def _apply_cookies_and_login(self, driver, account, browser_num):
        """应用Cookie并验证登录状态"""
        try:
            username = account['email']
            logger.info(f"🔑 浏览器 {browser_num} 开始应用Cookie: {username}")
            
            # 获取Cookie管理器
            config = {
                'performance.max_concurrent_sessions': 100,
                'session.timeout': 3600
            }
            cookie_manager = CookieManager(config)
            
            # 获取账号的Cookie
            cookie_data = cookie_manager.get_cookies(username)
            if not cookie_data or 'cookies' not in cookie_data:
                logger.warning(f"⚠️ 浏览器 {browser_num} 没有找到Cookie: {username}")
                return False
            
            cookies = cookie_data['cookies']
            logger.info(f"🍪 浏览器 {browser_num} 开始应用 {len(cookies)} 个Cookie")
            
            # 应用Cookie
            for cookie in cookies:
                try:
                    cookie_dict = {
                        'name': cookie.get('name'),
                        'value': cookie.get('value'),
                        'domain': cookie.get('domain', '.sina.com.cn'),
                        'path': cookie.get('path', '/'),
                    }
                    
                    if 'secure' in cookie:
                        cookie_dict['secure'] = cookie['secure']
                    if 'httpOnly' in cookie:
                        cookie_dict['httpOnly'] = cookie['httpOnly']
                    
                    driver.add_cookie(cookie_dict)
                    
                except Exception as e:
                    logger.debug(f"跳过无效Cookie: {e}")
                    continue
            
            # 刷新页面以应用Cookie
            driver.refresh()
            time.sleep(3)  # 增加等待时间确保稳定性
            
            # 验证登录状态
            return self._verify_login_status(driver, username, browser_num)
            
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} Cookie应用失败: {e}")
            return False
    
    def _verify_login_status(self, driver, username, browser_num):
        """验证登录状态"""
        try:
            current_url = driver.current_url
            page_title = driver.title
            
            logger.info(f"📍 浏览器 {browser_num} 当前URL: {current_url}")
            logger.info(f"📄 浏览器 {browser_num} 页面标题: {page_title}")
            
            # 检查是否在邮箱主页
            if "mail.sina.com.cn" in current_url and "登录" not in page_title:
                # 查找写信按钮或其他登录标识
                try:
                    write_buttons = driver.find_elements("xpath", "//a[contains(text(), '写信') or contains(@title, '写信')]")
                    if write_buttons:
                        logger.info(f"✅ 浏览器 {browser_num} 登录验证成功: 找到写信按钮")
                        return True
                    
                    user_elements = driver.find_elements("xpath", "//*[contains(text(), '收件箱') or contains(text(), '发件箱')]")
                    if user_elements:
                        logger.info(f"✅ 浏览器 {browser_num} 登录验证成功: 找到邮箱功能")
                        return True
                except Exception as e:
                    logger.debug(f"查找登录标识时出错: {e}")
            
            logger.warning(f"⚠️ 浏览器 {browser_num} 可能未登录")
            return False
            
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} 登录状态验证失败: {e}")
            return False
    
    def test_success_rate_optimization(self):
        """测试成功率优化效果"""
        try:
            logger.info("🚀 开始第二阶段成功率优化测试...")
            
            total_tests = 0
            total_success = 0
            success_results = []
            
            # 为每个浏览器分配收件人
            for browser_info in self.browser_drivers:
                driver = browser_info['driver']
                account = browser_info['account']
                browser_num = browser_info['browser_num']
                
                logger.info("=" * 60)
                logger.info(f"🌐 浏览器 {browser_num} ({account['email']}) 开始成功率优化测试")
                logger.info("=" * 60)
                
                # 创建增强成功率发送器
                enhanced_sender = EnhancedSuccessRateSender(driver)
                
                # 计算该浏览器要发送的邮件
                start_idx = (browser_num - 1) * self.test_emails_per_account
                end_idx = start_idx + self.test_emails_per_account
                recipients_for_browser = self.test_recipients[start_idx:end_idx]
                
                logger.info(f"📧 浏览器 {browser_num} 将发送 {len(recipients_for_browser)} 封邮件")
                
                # 发送每封邮件
                for email_idx, recipient in enumerate(recipients_for_browser):
                    email_num = email_idx + 1
                    total_tests += 1
                    
                    logger.info(f"📮 浏览器 {browser_num} 开始发送第 {email_num} 封邮件到: {recipient['email']}")
                    
                    # 构建邮件内容
                    subject = f"🎯 成功率优化测试邮件 {email_num} - 来自{account['email']}"
                    content = f"""
                    <div style="font-family: Arial, sans-serif;">
                        <h3>🎯 第二阶段成功率优化测试</h3>
                        <p>尊敬的 {recipient['name']}，</p>
                        <p>这是一封来自成功率优化系统的测试邮件。</p>
                        <ul>
                            <li>发送账号: {account['email']}</li>
                            <li>邮件编号: {email_num}</li>
                            <li>发送时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</li>
                            <li>浏览器编号: {browser_num}</li>
                            <li>优化目标: 90%以上成功率</li>
                        </ul>
                        <p><strong>如果您收到此邮件，说明我们的成功率优化系统运行正常！</strong></p>
                        <p style="color: #666; font-size: 12px;">此邮件由成功率优化测试系统发送，请勿回复。</p>
                    </div>
                    """
                    
                    # 使用增强发送器发送邮件
                    result = enhanced_sender.send_email_enhanced(
                        recipient['email'], 
                        subject, 
                        content
                    )
                    
                    # 记录成功率结果
                    success_result = {
                        'browser_num': browser_num,
                        'email_num': email_num,
                        'account': account['email'],
                        'recipient': recipient['email'],
                        'result': result,
                        'success': result.get('success', False)
                    }
                    success_results.append(success_result)
                    
                    if result.get('success'):
                        total_success += 1
                        total_time = result.get('total_time', 0)
                        strategy = result.get('strategy_used', 'unknown')
                        confidence = result.get('verification_confidence', 0)
                        
                        logger.info(f"✅ 浏览器 {browser_num} 第 {email_num} 封邮件发送成功！")
                        logger.info(f"   策略: {strategy}")
                        logger.info(f"   置信度: {confidence:.2f}")
                        logger.info(f"   总耗时: {total_time:.2f}秒")
                        
                        # 显示验证详情
                        verification_summary = enhanced_sender.verifier.get_verification_summary(
                            result.get('verification_details', {})
                        )
                        logger.info(f"   验证详情: {verification_summary}")
                        
                    else:
                        error = result.get('error', '未知错误')
                        total_time = result.get('total_time', 0)
                        logger.error(f"❌ 浏览器 {browser_num} 第 {email_num} 封邮件发送失败: {error}")
                        logger.error(f"   总耗时: {total_time:.2f}秒")
                    
                    # 邮件间隔
                    time.sleep(2)
                
                # 显示该浏览器的性能统计
                performance_report = enhanced_sender.get_performance_report()
                logger.info(f"📊 浏览器 {browser_num} 性能报告:")
                for key, value in performance_report.items():
                    logger.info(f"   {key}: {value}")
                
                logger.info(f"🎉 浏览器 {browser_num} ({account['email']}) 成功率测试完成")
            
            # 显示总体成功率优化结果
            self._analyze_success_rate_optimization_results(success_results, total_tests, total_success)
            
            logger.info("🎉 第二阶段成功率优化测试完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 成功率优化测试失败: {e}")
            return False
    
    def _analyze_success_rate_optimization_results(self, results, total_tests, total_success):
        """分析成功率优化结果"""
        try:
            logger.info("=" * 60)
            logger.info("📊 第二阶段成功率优化测试总结")
            logger.info("=" * 60)
            
            # 基本统计
            success_rate = (total_success / total_tests) * 100 if total_tests > 0 else 0
            logger.info(f"📧 总测试邮件: {total_tests} 封")
            logger.info(f"✅ 发送成功: {total_success} 封")
            logger.info(f"📈 成功率: {success_rate:.1f}%")
            
            # 成功率分析
            successful_results = [r for r in results if r['success']]
            if successful_results:
                # 策略分析
                strategy_stats = {}
                confidence_levels = []
                time_stats = []
                
                for result_data in successful_results:
                    result = result_data['result']
                    
                    # 统计策略使用情况
                    strategy = result.get('strategy_used', 'unknown')
                    if strategy not in strategy_stats:
                        strategy_stats[strategy] = {'count': 0, 'total_time': 0}
                    strategy_stats[strategy]['count'] += 1
                    strategy_stats[strategy]['total_time'] += result.get('total_time', 0)
                    
                    # 收集置信度和时间数据
                    confidence_levels.append(result.get('verification_confidence', 0))
                    time_stats.append(result.get('total_time', 0))
                
                # 显示策略统计
                logger.info(f"📊 成功策略统计:")
                for strategy, stats in strategy_stats.items():
                    avg_time = stats['total_time'] / stats['count'] if stats['count'] > 0 else 0
                    logger.info(f"   {strategy}: {stats['count']}次, 平均耗时: {avg_time:.2f}秒")
                
                # 显示置信度统计
                if confidence_levels:
                    avg_confidence = sum(confidence_levels) / len(confidence_levels)
                    min_confidence = min(confidence_levels)
                    max_confidence = max(confidence_levels)
                    logger.info(f"📊 验证置信度统计:")
                    logger.info(f"   平均置信度: {avg_confidence:.2f}")
                    logger.info(f"   最低置信度: {min_confidence:.2f}")
                    logger.info(f"   最高置信度: {max_confidence:.2f}")
                
                # 显示时间统计
                if time_stats:
                    avg_time = sum(time_stats) / len(time_stats)
                    min_time = min(time_stats)
                    max_time = max(time_stats)
                    logger.info(f"📊 发送时间统计:")
                    logger.info(f"   平均发送时间: {avg_time:.2f}秒")
                    logger.info(f"   最快发送时间: {min_time:.2f}秒")
                    logger.info(f"   最慢发送时间: {max_time:.2f}秒")
            
            # 成功率目标达成情况
            target_achievement = success_rate >= (self.success_rate_target * 100)
            if target_achievement:
                logger.info(f"🎯 成功率优化目标达成！实际: {success_rate:.1f}%, 目标: {self.success_rate_target:.0%}")
            else:
                logger.warning(f"⚠️ 成功率优化目标未达成。实际: {success_rate:.1f}%, 目标: {self.success_rate_target:.0%}")
            
            # 改进分析
            baseline_success_rate = 0  # 之前的基准成功率
            improvement = success_rate - baseline_success_rate
            logger.info(f"📈 成功率改进: +{improvement:.1f}% (从{baseline_success_rate}%提升到{success_rate:.1f}%)")
            
            # 失败分析
            failed_results = [r for r in results if not r['success']]
            if failed_results:
                logger.info(f"❌ 失败邮件分析:")
                error_stats = {}
                for result_data in failed_results:
                    error = result_data['result'].get('error', '未知错误')
                    if error not in error_stats:
                        error_stats[error] = 0
                    error_stats[error] += 1
                
                for error, count in error_stats.items():
                    logger.info(f"   {error}: {count}次")
            
        except Exception as e:
            logger.error(f"❌ 分析成功率优化结果失败: {e}")
    
    def run_complete_test(self):
        """运行完整的成功率优化测试"""
        try:
            logger.info(f"🧪 开始 {self.test_name}")
            
            # 1. 加载测试数据
            logger.info("=" * 50)
            logger.info("步骤1: 加载测试数据")
            logger.info("=" * 50)
            
            if not self.load_test_recipients():
                return False
            
            # 2. 创建稳定浏览器
            logger.info("=" * 50)
            logger.info("步骤2: 创建稳定浏览器")
            logger.info("=" * 50)
            
            if not self.create_stable_browsers():
                return False
            
            # 3. 等待用户确认
            logger.info("=" * 50)
            logger.info("测试信息确认")
            logger.info("=" * 50)
            
            logger.info(f"🎯 将测试第二阶段成功率优化")
            logger.info(f"📈 成功率目标: {self.success_rate_target:.0%}以上")
            logger.info(f"🌐 使用 {len(self.browser_drivers)} 个稳定浏览器")
            logger.info(f"📮 每个浏览器发送 {self.test_emails_per_account} 封邮件")
            logger.info(f"👥 总共发送 {len(self.browser_drivers) * self.test_emails_per_account} 封邮件")
            
            logger.info("⏰ 测试将在3秒后开始...")
            time.sleep(3)
            
            # 4. 执行成功率优化测试
            logger.info("=" * 50)
            logger.info("步骤3: 执行成功率优化测试")
            logger.info("=" * 50)
            
            success = self.test_success_rate_optimization()
            
            if success:
                logger.info("🎉 第二阶段成功率优化测试成功！")
            else:
                logger.error("❌ 第二阶段成功率优化测试失败！")
            
            # 5. 保持浏览器打开供观察
            logger.info("⏰ 浏览器将保持打开20秒供观察...")
            time.sleep(20)
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}")
            return False
            
        finally:
            # 清理浏览器
            self.cleanup_browsers()
    
    def cleanup_browsers(self):
        """清理浏览器"""
        try:
            logger.info("🧹 清理测试浏览器...")
            
            for i, browser_info in enumerate(self.browser_drivers):
                try:
                    browser_info['driver'].quit()
                    logger.info(f"✅ 浏览器 {i+1} 已关闭")
                except Exception as e:
                    logger.warning(f"⚠️ 关闭浏览器 {i+1} 失败: {e}")
            
            self.browser_drivers.clear()
            logger.info("✅ 浏览器清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理浏览器失败: {e}")

def main():
    """主函数"""
    try:
        logger.info("🧪 启动第二阶段成功率优化测试程序")
        
        # 创建测试实例
        test = SuccessRateOptimizationPhase2Test()
        
        # 运行完整测试
        success = test.run_complete_test()
        
        if success:
            logger.info("🎉 测试成功完成！")
            return 0
        else:
            logger.error("❌ 测试失败！")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 测试程序异常: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n测试完成，退出码: {exit_code}")
    sys.exit(exit_code)
