#!/usr/bin/env python3
"""
并发发送器GUI控件 - 多浏览器并发发送的可视化界面
"""

import time
from typing import List, Dict, Any, Optional
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QGroupBox, QLabel, QPushButton, QTextEdit, QTableWidget,
    QTableWidgetItem, QProgressBar, QSpinBox, QCheckBox,
    QTabWidget, QSplitter, QFrame, QScrollArea, QComboBox,
    QMessageBox, QHeaderView
)
from PyQt5.QtCore import QTimer, Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette

from src.utils.logger import setup_logger
from src.models.account import Account
from src.core.integrated_concurrent_sender import IntegratedConcurrentSender, IntegratedSendingConfig

logger = setup_logger("INFO")

class BrowserStatusWidget(QFrame):
    """单个浏览器状态显示控件"""
    
    def __init__(self, browser_id: str):
        super().__init__()
        self.browser_id = browser_id
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setFrameStyle(QFrame.Box)
        self.setLineWidth(1)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(5)
        
        # 浏览器ID标题
        title_label = QLabel(f"🌐 {self.browser_id}")
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 状态信息
        self.status_label = QLabel("状态: 未知")
        self.account_label = QLabel("账号: 无")
        self.sent_label = QLabel("已发送: 0")
        self.queue_label = QLabel("队列: 0")
        
        layout.addWidget(self.status_label)
        layout.addWidget(self.account_label)
        layout.addWidget(self.sent_label)
        layout.addWidget(self.queue_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximum(100)
        layout.addWidget(self.progress_bar)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        self.switch_btn = QPushButton("切换账号")
        self.switch_btn.setMaximumHeight(25)
        button_layout.addWidget(self.switch_btn)
        layout.addLayout(button_layout)
    
    def update_status(self, status_data: Dict[str, Any]):
        """更新状态显示"""
        # 更新状态
        is_working = status_data.get('is_working', False)
        status_text = "🟢 工作中" if is_working else "🔴 停止"
        self.status_label.setText(f"状态: {status_text}")
        
        # 更新账号
        current_account = status_data.get('current_account', '无')
        self.account_label.setText(f"账号: {current_account}")
        
        # 更新发送数量
        total_sent = status_data.get('total_sent', 0)
        current_sent = status_data.get('current_account_sent', 0)
        self.sent_label.setText(f"已发送: {current_sent}/{total_sent}")
        
        # 更新队列
        queue_size = status_data.get('queue_size', 0)
        self.queue_label.setText(f"队列: {queue_size}")
        
        # 更新进度条
        needs_switch = status_data.get('needs_account_switch', False)
        if needs_switch:
            self.progress_bar.setValue(100)
            self.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #ff6b6b; }")
        else:
            # 根据发送数量计算进度
            max_emails = 5  # 假设每个账号最多发送5封
            progress = min((current_sent / max_emails) * 100, 100) if max_emails > 0 else 0
            self.progress_bar.setValue(int(progress))
            self.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #4ecdc4; }")

class ConcurrentSenderWidget(QWidget):
    """并发发送器主控件"""
    
    # 信号定义
    status_updated = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 核心组件
        self.concurrent_sender: Optional[IntegratedConcurrentSender] = None
        self.accounts: List[Account] = []
        
        # UI组件
        self.browser_widgets: Dict[str, BrowserStatusWidget] = {}
        
        # 定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        
        self.setup_ui()
        logger.info("🎨 并发发送器GUI初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 控制面板标签页
        control_tab = self.create_control_tab()
        tab_widget.addTab(control_tab, "🎛️ 控制面板")
        
        # 浏览器监控标签页
        monitor_tab = self.create_monitor_tab()
        tab_widget.addTab(monitor_tab, "🖥️ 浏览器监控")
        
        # 统计信息标签页
        stats_tab = self.create_stats_tab()
        tab_widget.addTab(stats_tab, "📊 统计信息")
        
        # 任务管理标签页
        task_tab = self.create_task_tab()
        tab_widget.addTab(task_tab, "📋 任务管理")
        
        layout.addWidget(tab_widget)
        
        # 底部状态栏
        self.create_status_bar(layout)
    
    def create_control_tab(self) -> QWidget:
        """创建控制面板标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 配置组
        config_group = QGroupBox("⚙️ 发送配置")
        config_layout = QGridLayout(config_group)
        
        # 浏览器数量
        config_layout.addWidget(QLabel("浏览器数量:"), 0, 0)
        self.browser_count_spin = QSpinBox()
        self.browser_count_spin.setRange(1, 10)
        self.browser_count_spin.setValue(3)
        config_layout.addWidget(self.browser_count_spin, 0, 1)
        
        # 每账号邮件数
        config_layout.addWidget(QLabel("每账号邮件数:"), 0, 2)
        self.emails_per_account_spin = QSpinBox()
        self.emails_per_account_spin.setRange(1, 50)
        self.emails_per_account_spin.setValue(5)
        config_layout.addWidget(self.emails_per_account_spin, 0, 3)
        
        # 发送间隔
        config_layout.addWidget(QLabel("发送间隔(秒):"), 1, 0)
        self.send_interval_spin = QSpinBox()
        self.send_interval_spin.setRange(1, 60)
        self.send_interval_spin.setValue(2)
        config_layout.addWidget(self.send_interval_spin, 1, 1)
        
        # 自动切换账号
        self.auto_switch_check = QCheckBox("自动切换账号")
        self.auto_switch_check.setChecked(True)
        config_layout.addWidget(self.auto_switch_check, 1, 2)
        
        layout.addWidget(config_group)
        
        # 控制按钮组
        control_group = QGroupBox("🎮 系统控制")
        control_layout = QHBoxLayout(control_group)
        
        self.init_btn = QPushButton("🔧 初始化系统")
        self.start_btn = QPushButton("🚀 启动发送")
        self.stop_btn = QPushButton("🛑 停止发送")
        self.reset_btn = QPushButton("🔄 重置系统")
        
        self.init_btn.clicked.connect(self.initialize_system)
        self.start_btn.clicked.connect(self.start_sending)
        self.stop_btn.clicked.connect(self.stop_sending)
        self.reset_btn.clicked.connect(self.reset_system)
        
        # 初始状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)
        
        control_layout.addWidget(self.init_btn)
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.reset_btn)
        
        layout.addWidget(control_group)
        
        # 快速任务添加组
        task_group = QGroupBox("📝 快速添加任务")
        task_layout = QGridLayout(task_group)
        
        task_layout.addWidget(QLabel("收件人:"), 0, 0)
        self.quick_to_edit = QTextEdit()
        self.quick_to_edit.setMaximumHeight(60)
        self.quick_to_edit.setPlaceholderText("输入邮箱地址，每行一个")
        task_layout.addWidget(self.quick_to_edit, 0, 1)
        
        task_layout.addWidget(QLabel("主题:"), 1, 0)
        self.quick_subject_edit = QTextEdit()
        self.quick_subject_edit.setMaximumHeight(30)
        task_layout.addWidget(self.quick_subject_edit, 1, 1)
        
        task_layout.addWidget(QLabel("内容:"), 2, 0)
        self.quick_content_edit = QTextEdit()
        self.quick_content_edit.setMaximumHeight(80)
        task_layout.addWidget(self.quick_content_edit, 2, 1)
        
        self.add_task_btn = QPushButton("➕ 添加任务")
        self.add_task_btn.clicked.connect(self.add_quick_tasks)
        self.add_task_btn.setEnabled(False)
        task_layout.addWidget(self.add_task_btn, 3, 1)
        
        layout.addWidget(task_group)
        
        return tab
    
    def create_monitor_tab(self) -> QWidget:
        """创建浏览器监控标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 浏览器状态概览
        overview_group = QGroupBox("📊 系统概览")
        overview_layout = QHBoxLayout(overview_group)
        
        self.total_browsers_label = QLabel("总浏览器: 0")
        self.active_browsers_label = QLabel("活跃浏览器: 0")
        self.total_accounts_label = QLabel("总账号: 0")
        self.system_status_label = QLabel("系统状态: 未初始化")
        
        overview_layout.addWidget(self.total_browsers_label)
        overview_layout.addWidget(self.active_browsers_label)
        overview_layout.addWidget(self.total_accounts_label)
        overview_layout.addWidget(self.system_status_label)
        overview_layout.addStretch()
        
        layout.addWidget(overview_group)
        
        # 浏览器状态显示区域
        browser_group = QGroupBox("🖥️ 浏览器状态")
        browser_layout = QVBoxLayout(browser_group)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.browser_grid_layout = QGridLayout(scroll_widget)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        
        browser_layout.addWidget(scroll_area)
        layout.addWidget(browser_group)
        
        return tab
    
    def create_stats_tab(self) -> QWidget:
        """创建统计信息标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 发送统计
        sending_group = QGroupBox("📈 发送统计")
        sending_layout = QGridLayout(sending_group)
        
        self.total_tasks_label = QLabel("总任务: 0")
        self.completed_tasks_label = QLabel("已完成: 0")
        self.failed_tasks_label = QLabel("失败: 0")
        self.emails_per_minute_label = QLabel("每分钟: 0")
        
        sending_layout.addWidget(self.total_tasks_label, 0, 0)
        sending_layout.addWidget(self.completed_tasks_label, 0, 1)
        sending_layout.addWidget(self.failed_tasks_label, 1, 0)
        sending_layout.addWidget(self.emails_per_minute_label, 1, 1)
        
        layout.addWidget(sending_group)
        
        # 账号切换统计
        switch_group = QGroupBox("🔄 账号切换统计")
        switch_layout = QGridLayout(switch_group)
        
        self.total_switches_label = QLabel("总切换: 0")
        self.successful_switches_label = QLabel("成功: 0")
        self.failed_switches_label = QLabel("失败: 0")
        self.switch_success_rate_label = QLabel("成功率: 0%")
        
        switch_layout.addWidget(self.total_switches_label, 0, 0)
        switch_layout.addWidget(self.successful_switches_label, 0, 1)
        switch_layout.addWidget(self.failed_switches_label, 1, 0)
        switch_layout.addWidget(self.switch_success_rate_label, 1, 1)
        
        layout.addWidget(switch_group)
        
        # 账号详情表格
        account_group = QGroupBox("👤 账号详情")
        account_layout = QVBoxLayout(account_group)
        
        self.account_table = QTableWidget()
        self.account_table.setColumnCount(5)
        self.account_table.setHorizontalHeaderLabels([
            "账号", "浏览器", "已发送", "状态", "最后发送时间"
        ])
        self.account_table.horizontalHeader().setStretchLastSection(True)
        
        account_layout.addWidget(self.account_table)
        layout.addWidget(account_group)
        
        return tab
    
    def create_task_tab(self) -> QWidget:
        """创建任务管理标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 任务队列状态
        queue_group = QGroupBox("📋 任务队列状态")
        queue_layout = QHBoxLayout(queue_group)
        
        self.global_queue_label = QLabel("全局队列: 0")
        self.total_queue_label = QLabel("总队列: 0")
        
        queue_layout.addWidget(self.global_queue_label)
        queue_layout.addWidget(self.total_queue_label)
        queue_layout.addStretch()
        
        layout.addWidget(queue_group)
        
        # 批量任务控制
        batch_group = QGroupBox("📦 批量任务控制")
        batch_layout = QHBoxLayout(batch_group)
        
        self.clear_queue_btn = QPushButton("🗑️ 清空队列")
        self.pause_tasks_btn = QPushButton("⏸️ 暂停任务")
        self.resume_tasks_btn = QPushButton("▶️ 恢复任务")
        
        batch_layout.addWidget(self.clear_queue_btn)
        batch_layout.addWidget(self.pause_tasks_btn)
        batch_layout.addWidget(self.resume_tasks_btn)
        batch_layout.addStretch()
        
        layout.addWidget(batch_group)
        
        return tab
    
    def create_status_bar(self, layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_layout = QHBoxLayout(status_frame)
        
        self.runtime_label = QLabel("运行时间: 00:00:00")
        self.performance_label = QLabel("性能: 0 邮件/分钟")
        self.health_label = QLabel("系统健康: 🔴")
        
        status_layout.addWidget(self.runtime_label)
        status_layout.addWidget(self.performance_label)
        status_layout.addWidget(self.health_label)
        status_layout.addStretch()
        
        layout.addWidget(status_frame)

    def set_accounts(self, accounts: List[Account]):
        """设置账号列表"""
        self.accounts = accounts
        logger.info(f"📋 设置账号列表，数量: {len(accounts)}")

    def initialize_system(self):
        """初始化系统"""
        try:
            if not self.accounts:
                QMessageBox.warning(self, "警告", "请先设置账号列表！")
                return

            logger.info("🔧 开始初始化并发发送系统...")

            # 创建配置
            config = IntegratedSendingConfig(
                max_browsers=self.browser_count_spin.value(),
                emails_per_account=self.emails_per_account_spin.value(),
                send_interval=self.send_interval_spin.value(),
                auto_switch_enabled=self.auto_switch_check.isChecked()
            )

            # 创建并发发送器
            self.concurrent_sender = IntegratedConcurrentSender(config)

            # 初始化
            if self.concurrent_sender.initialize(self.accounts):
                logger.info("✅ 系统初始化成功")

                # 更新UI状态
                self.init_btn.setEnabled(False)
                self.start_btn.setEnabled(True)
                self.add_task_btn.setEnabled(True)

                # 创建浏览器状态控件
                self.create_browser_widgets()

                # 启动更新定时器
                self.update_timer.start(1000)  # 每秒更新

                QMessageBox.information(self, "成功", "系统初始化成功！")
            else:
                logger.error("❌ 系统初始化失败")
                QMessageBox.critical(self, "错误", "系统初始化失败！")

        except Exception as e:
            logger.error(f"❌ 初始化系统异常: {e}")
            QMessageBox.critical(self, "错误", f"初始化失败: {str(e)}")

    def start_sending(self):
        """启动发送"""
        try:
            if not self.concurrent_sender:
                QMessageBox.warning(self, "警告", "请先初始化系统！")
                return

            if self.concurrent_sender.start_sending():
                logger.info("🚀 发送系统启动成功")

                # 更新UI状态
                self.start_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)

                QMessageBox.information(self, "成功", "发送系统启动成功！")
            else:
                QMessageBox.critical(self, "错误", "发送系统启动失败！")

        except Exception as e:
            logger.error(f"❌ 启动发送异常: {e}")
            QMessageBox.critical(self, "错误", f"启动失败: {str(e)}")

    def stop_sending(self):
        """停止发送"""
        try:
            if self.concurrent_sender:
                self.concurrent_sender.stop_sending()
                logger.info("🛑 发送系统已停止")

                # 更新UI状态
                self.start_btn.setEnabled(True)
                self.stop_btn.setEnabled(False)

                QMessageBox.information(self, "成功", "发送系统已停止！")

        except Exception as e:
            logger.error(f"❌ 停止发送异常: {e}")
            QMessageBox.critical(self, "错误", f"停止失败: {str(e)}")

    def reset_system(self):
        """重置系统"""
        try:
            # 停止定时器
            self.update_timer.stop()

            # 清理并发发送器
            if self.concurrent_sender:
                self.concurrent_sender.cleanup()
                self.concurrent_sender = None

            # 清理浏览器控件
            self.clear_browser_widgets()

            # 重置UI状态
            self.init_btn.setEnabled(True)
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)
            self.add_task_btn.setEnabled(False)

            # 重置显示
            self.reset_display()

            logger.info("🔄 系统重置完成")
            QMessageBox.information(self, "成功", "系统重置完成！")

        except Exception as e:
            logger.error(f"❌ 重置系统异常: {e}")
            QMessageBox.critical(self, "错误", f"重置失败: {str(e)}")

    def add_quick_tasks(self):
        """添加快速任务"""
        try:
            if not self.concurrent_sender or not self.concurrent_sender.is_running:
                QMessageBox.warning(self, "警告", "请先启动发送系统！")
                return

            # 获取输入内容
            to_emails = self.quick_to_edit.toPlainText().strip().split('\n')
            subject = self.quick_subject_edit.toPlainText().strip()
            content = self.quick_content_edit.toPlainText().strip()

            if not to_emails or not subject or not content:
                QMessageBox.warning(self, "警告", "请填写完整的邮件信息！")
                return

            # 过滤空行
            to_emails = [email.strip() for email in to_emails if email.strip()]

            if not to_emails:
                QMessageBox.warning(self, "警告", "请输入有效的邮箱地址！")
                return

            # 添加任务
            email_list = [(email, subject, content) for email in to_emails]
            task_ids = self.concurrent_sender.add_batch_tasks(email_list)

            logger.info(f"📝 添加了 {len(task_ids)} 个任务")
            QMessageBox.information(self, "成功", f"成功添加 {len(task_ids)} 个邮件任务！")

            # 清空输入框
            self.quick_to_edit.clear()
            self.quick_subject_edit.clear()
            self.quick_content_edit.clear()

        except Exception as e:
            logger.error(f"❌ 添加任务异常: {e}")
            QMessageBox.critical(self, "错误", f"添加任务失败: {str(e)}")

    def create_browser_widgets(self):
        """创建浏览器状态控件"""
        if not self.concurrent_sender:
            return

        # 清理现有控件
        self.clear_browser_widgets()

        # 获取浏览器状态
        browser_status = self.concurrent_sender.get_browser_status()

        # 创建新控件
        row = 0
        col = 0
        max_cols = 3  # 每行最多3个浏览器

        for browser_id in browser_status.keys():
            widget = BrowserStatusWidget(browser_id)

            # 连接切换按钮
            widget.switch_btn.clicked.connect(
                lambda checked=False, bid=browser_id: self.force_switch_account(bid)
            )

            self.browser_widgets[browser_id] = widget
            self.browser_grid_layout.addWidget(widget, row, col)

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        logger.info(f"🎨 创建了 {len(self.browser_widgets)} 个浏览器状态控件")

    def clear_browser_widgets(self):
        """清理浏览器控件"""
        for widget in self.browser_widgets.values():
            widget.deleteLater()
        self.browser_widgets.clear()

    def force_switch_account(self, browser_id: str):
        """强制切换账号"""
        try:
            if self.concurrent_sender:
                success = self.concurrent_sender.force_account_switch(browser_id)
                if success:
                    QMessageBox.information(self, "成功", f"浏览器 {browser_id} 账号切换成功！")
                else:
                    QMessageBox.warning(self, "失败", f"浏览器 {browser_id} 账号切换失败！")
        except Exception as e:
            logger.error(f"❌ 强制切换账号异常: {e}")
            QMessageBox.critical(self, "错误", f"切换失败: {str(e)}")

    def update_display(self):
        """更新显示"""
        try:
            if not self.concurrent_sender:
                return

            # 获取综合统计
            stats = self.concurrent_sender.get_comprehensive_stats()

            # 更新系统概览
            self.update_system_overview(stats)

            # 更新浏览器状态
            self.update_browser_status(stats)

            # 更新统计信息
            self.update_statistics(stats)

            # 更新状态栏
            self.update_status_bar(stats)

        except Exception as e:
            logger.error(f"❌ 更新显示异常: {e}")

    def update_system_overview(self, stats: Dict[str, Any]):
        """更新系统概览"""
        sending_stats = stats.get('sending_stats', {})
        system_status = stats.get('system_status', {})

        self.total_browsers_label.setText(f"总浏览器: {sending_stats.get('total_browsers', 0)}")
        self.active_browsers_label.setText(f"活跃浏览器: {sending_stats.get('active_browsers', 0)}")
        self.total_accounts_label.setText(f"总账号: {sending_stats.get('total_accounts', 0)}")

        is_running = system_status.get('is_running', False)
        status_text = "🟢 运行中" if is_running else "🔴 已停止"
        self.system_status_label.setText(f"系统状态: {status_text}")

    def update_browser_status(self, stats: Dict[str, Any]):
        """更新浏览器状态"""
        browser_details = stats.get('browser_details', {})

        for browser_id, widget in self.browser_widgets.items():
            if browser_id in browser_details:
                widget.update_status(browser_details[browser_id])

    def update_statistics(self, stats: Dict[str, Any]):
        """更新统计信息"""
        sending_stats = stats.get('sending_stats', {})
        account_monitoring = stats.get('account_monitoring', {})

        # 发送统计
        self.total_tasks_label.setText(f"总任务: {sending_stats.get('total_tasks', 0)}")
        self.completed_tasks_label.setText(f"已完成: {sending_stats.get('completed_tasks', 0)}")
        self.failed_tasks_label.setText(f"失败: {sending_stats.get('failed_tasks', 0)}")
        self.emails_per_minute_label.setText(f"每分钟: {sending_stats.get('emails_per_minute', 0):.1f}")

        # 账号切换统计
        self.total_switches_label.setText(f"总切换: {account_monitoring.get('total_switches', 0)}")
        self.successful_switches_label.setText(f"成功: {account_monitoring.get('successful_switches', 0)}")
        self.failed_switches_label.setText(f"失败: {account_monitoring.get('failed_switches', 0)}")
        self.switch_success_rate_label.setText(f"成功率: {account_monitoring.get('switch_success_rate', 0):.1f}%")

        # 更新账号详情表格
        self.update_account_table(stats.get('account_details', {}))

        # 更新任务队列状态
        self.global_queue_label.setText(f"全局队列: {sending_stats.get('global_queue_size', 0)}")

    def update_account_table(self, account_details: Dict[str, Any]):
        """更新账号详情表格"""
        self.account_table.setRowCount(len(account_details))

        for row, (email, details) in enumerate(account_details.items()):
            self.account_table.setItem(row, 0, QTableWidgetItem(email))
            self.account_table.setItem(row, 1, QTableWidgetItem(details.get('browser_id', '无')))
            self.account_table.setItem(row, 2, QTableWidgetItem(str(details.get('sent_count', 0))))

            is_active = details.get('is_active', False)
            status_text = "🟢 活跃" if is_active else "🔴 待机"
            self.account_table.setItem(row, 3, QTableWidgetItem(status_text))

            # 最后发送时间（简化显示）
            self.account_table.setItem(row, 4, QTableWidgetItem("--"))

    def update_status_bar(self, stats: Dict[str, Any]):
        """更新状态栏"""
        system_status = stats.get('system_status', {})
        sending_stats = stats.get('sending_stats', {})

        # 运行时间
        runtime = system_status.get('runtime_seconds', 0)
        hours = int(runtime // 3600)
        minutes = int((runtime % 3600) // 60)
        seconds = int(runtime % 60)
        self.runtime_label.setText(f"运行时间: {hours:02d}:{minutes:02d}:{seconds:02d}")

        # 性能指标
        emails_per_minute = sending_stats.get('emails_per_minute', 0)
        self.performance_label.setText(f"性能: {emails_per_minute:.1f} 邮件/分钟")

        # 系统健康
        is_healthy = self.concurrent_sender.is_system_healthy() if self.concurrent_sender else False
        health_text = "🟢 健康" if is_healthy else "🔴 异常"
        self.health_label.setText(f"系统健康: {health_text}")

    def reset_display(self):
        """重置显示"""
        # 重置概览
        self.total_browsers_label.setText("总浏览器: 0")
        self.active_browsers_label.setText("活跃浏览器: 0")
        self.total_accounts_label.setText("总账号: 0")
        self.system_status_label.setText("系统状态: 未初始化")

        # 重置统计
        self.total_tasks_label.setText("总任务: 0")
        self.completed_tasks_label.setText("已完成: 0")
        self.failed_tasks_label.setText("失败: 0")
        self.emails_per_minute_label.setText("每分钟: 0")

        self.total_switches_label.setText("总切换: 0")
        self.successful_switches_label.setText("成功: 0")
        self.failed_switches_label.setText("失败: 0")
        self.switch_success_rate_label.setText("成功率: 0%")

        # 清空表格
        self.account_table.setRowCount(0)

        # 重置状态栏
        self.runtime_label.setText("运行时间: 00:00:00")
        self.performance_label.setText("性能: 0 邮件/分钟")
        self.health_label.setText("系统健康: 🔴")

    def closeEvent(self, event):
        """关闭事件"""
        try:
            # 停止定时器
            self.update_timer.stop()

            # 清理资源
            if self.concurrent_sender:
                self.concurrent_sender.cleanup()

            event.accept()

        except Exception as e:
            logger.error(f"❌ 关闭事件异常: {e}")
            event.accept()
