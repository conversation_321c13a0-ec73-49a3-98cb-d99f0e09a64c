#!/usr/bin/env python3
"""
智能账号监控器 - 监控每个账号的发送数量并自动切换
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import defaultdict

from src.utils.logger import setup_logger
from src.models.account import Account

logger = setup_logger("INFO")

@dataclass
class AccountStatus:
    """账号状态"""
    email: str
    sent_count: int = 0
    last_send_time: float = 0
    is_active: bool = True
    browser_id: Optional[str] = None
    switch_count: int = 0  # 被切换的次数
    total_send_time: float = 0  # 总发送时间
    avg_send_time: float = 0  # 平均发送时间
    last_switch_time: float = 0  # 最后切换时间
    
    def update_send_stats(self, send_time: float):
        """更新发送统计"""
        self.sent_count += 1
        self.last_send_time = time.time()
        self.total_send_time += send_time
        self.avg_send_time = self.total_send_time / self.sent_count

@dataclass
class BrowserAccountBinding:
    """浏览器账号绑定"""
    browser_id: str
    current_account: Optional[str] = None
    assigned_accounts: List[str] = field(default_factory=list)
    account_index: int = 0
    last_switch_time: float = 0
    switch_count: int = 0

@dataclass
class MonitorConfig:
    """监控配置"""
    emails_per_account: int = 5  # 每个账号最大发送数量
    monitor_interval: float = 1.0  # 监控间隔（秒）
    auto_switch_enabled: bool = True  # 是否启用自动切换
    switch_cooldown: float = 2.0  # 切换冷却时间（秒）
    max_retries: int = 3  # 切换失败最大重试次数

class SmartAccountMonitor:
    """智能账号监控器"""
    
    def __init__(self, config: MonitorConfig):
        self.config = config
        
        # 账号状态管理
        self.account_status: Dict[str, AccountStatus] = {}
        self.browser_bindings: Dict[str, BrowserAccountBinding] = {}
        
        # 监控线程
        self.monitor_thread: Optional[threading.Thread] = None
        self.is_monitoring = False
        self.monitor_lock = threading.Lock()
        
        # 回调函数
        self.switch_callbacks: List[Callable] = []
        self.alert_callbacks: List[Callable] = []
        
        # 统计信息
        self.total_switches = 0
        self.successful_switches = 0
        self.failed_switches = 0
        self.start_time = 0
        
        logger.info("🔍 智能账号监控器初始化完成")
    
    def initialize_accounts(self, accounts: List[Account], browser_ids: List[str]):
        """初始化账号监控"""
        logger.info(f"🔧 初始化账号监控，账号数量: {len(accounts)}, 浏览器数量: {len(browser_ids)}")
        
        # 初始化账号状态
        for account in accounts:
            self.account_status[account.email] = AccountStatus(email=account.email)
        
        # 初始化浏览器绑定
        self._initialize_browser_bindings(accounts, browser_ids)
        
        logger.info("✅ 账号监控初始化完成")
    
    def _initialize_browser_bindings(self, accounts: List[Account], browser_ids: List[str]):
        """初始化浏览器账号绑定"""
        if not browser_ids:
            logger.error("❌ 没有可用的浏览器ID")
            return
        
        # 平均分配账号到浏览器
        accounts_per_browser = len(accounts) // len(browser_ids)
        remaining_accounts = len(accounts) % len(browser_ids)
        
        account_index = 0
        for i, browser_id in enumerate(browser_ids):
            binding = BrowserAccountBinding(browser_id=browser_id)
            
            # 计算这个浏览器应该分配的账号数量
            accounts_for_this_browser = accounts_per_browser
            if i < remaining_accounts:
                accounts_for_this_browser += 1
            
            # 分配账号
            for j in range(accounts_for_this_browser):
                if account_index < len(accounts):
                    account_email = accounts[account_index].email
                    binding.assigned_accounts.append(account_email)
                    
                    # 设置账号的浏览器绑定
                    if account_email in self.account_status:
                        self.account_status[account_email].browser_id = browser_id
                    
                    account_index += 1
            
            # 设置第一个账号为当前账号
            if binding.assigned_accounts:
                binding.current_account = binding.assigned_accounts[0]
                binding.account_index = 0
            
            self.browser_bindings[browser_id] = binding
            
            logger.info(f"📋 浏览器 {browser_id} 分配账号: {binding.assigned_accounts}")
    
    def start_monitoring(self):
        """启动监控"""
        if self.is_monitoring:
            logger.warning("⚠️ 账号监控已在运行")
            return
        
        logger.info("🚀 启动智能账号监控...")
        
        self.is_monitoring = True
        self.start_time = time.time()
        
        self.monitor_thread = threading.Thread(
            target=self._monitor_worker,
            name="AccountMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        logger.info("✅ 智能账号监控启动完成")
    
    def stop_monitoring(self):
        """停止监控"""
        logger.info("🛑 停止智能账号监控...")
        
        self.is_monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        logger.info("✅ 智能账号监控已停止")
    
    def _monitor_worker(self):
        """监控工作线程"""
        logger.info("🔧 账号监控工作线程开始")
        
        while self.is_monitoring:
            try:
                with self.monitor_lock:
                    self._check_account_limits()
                
                time.sleep(self.config.monitor_interval)
                
            except Exception as e:
                logger.error(f"❌ 账号监控异常: {e}")
                time.sleep(1)
        
        logger.info("🛑 账号监控工作线程停止")
    
    def _check_account_limits(self):
        """检查账号发送限制"""
        for browser_id, binding in self.browser_bindings.items():
            if not binding.current_account:
                continue
            
            current_account_status = self.account_status.get(binding.current_account)
            if not current_account_status:
                continue
            
            # 检查是否达到发送限制
            if current_account_status.sent_count >= self.config.emails_per_account:
                logger.info(f"🔄 浏览器 {browser_id} 账号 {binding.current_account} 达到发送限制 ({current_account_status.sent_count}/{self.config.emails_per_account})")
                
                if self.config.auto_switch_enabled:
                    self._trigger_account_switch(browser_id)
    
    def _trigger_account_switch(self, browser_id: str):
        """触发账号切换"""
        try:
            # 检查切换冷却时间
            binding = self.browser_bindings.get(browser_id)
            if not binding:
                return
            
            current_time = time.time()
            if current_time - binding.last_switch_time < self.config.switch_cooldown:
                logger.debug(f"🕐 浏览器 {browser_id} 切换冷却中...")
                return
            
            # 找到下一个可用账号
            next_account = self._find_next_available_account(browser_id)
            if not next_account:
                logger.warning(f"⚠️ 浏览器 {browser_id} 没有更多可用账号")
                self._trigger_alert("no_available_accounts", browser_id)
                return
            
            # 执行切换
            old_account = binding.current_account
            success = self._execute_account_switch(browser_id, next_account)
            
            if success:
                # 更新绑定信息
                binding.current_account = next_account
                binding.account_index = binding.assigned_accounts.index(next_account)
                binding.last_switch_time = current_time
                binding.switch_count += 1
                
                # 更新账号状态
                if old_account and old_account in self.account_status:
                    self.account_status[old_account].switch_count += 1
                    self.account_status[old_account].last_switch_time = current_time
                    self.account_status[old_account].is_active = False
                
                if next_account in self.account_status:
                    self.account_status[next_account].is_active = True
                
                # 更新统计
                self.total_switches += 1
                self.successful_switches += 1
                
                logger.info(f"✅ 浏览器 {browser_id} 账号切换成功: {old_account} -> {next_account}")
                self._trigger_switch_callback(browser_id, old_account, next_account, True)
                
            else:
                self.failed_switches += 1
                logger.error(f"❌ 浏览器 {browser_id} 账号切换失败: {old_account} -> {next_account}")
                self._trigger_switch_callback(browser_id, old_account, next_account, False)
                
        except Exception as e:
            logger.error(f"❌ 触发账号切换异常: {e}")
    
    def _find_next_available_account(self, browser_id: str) -> Optional[str]:
        """找到下一个可用账号"""
        binding = self.browser_bindings.get(browser_id)
        if not binding or not binding.assigned_accounts:
            return None
        
        # 从当前账号的下一个开始查找
        start_index = (binding.account_index + 1) % len(binding.assigned_accounts)
        
        for i in range(len(binding.assigned_accounts)):
            index = (start_index + i) % len(binding.assigned_accounts)
            account_email = binding.assigned_accounts[index]
            
            account_status = self.account_status.get(account_email)
            if account_status and account_status.sent_count < self.config.emails_per_account:
                return account_email
        
        return None
    
    def _execute_account_switch(self, browser_id: str, new_account: str) -> bool:
        """执行账号切换（通过回调函数）"""
        # 这里通过回调函数来执行实际的切换操作
        for callback in self.switch_callbacks:
            try:
                result = callback(browser_id, new_account)
                if result:
                    return True
            except Exception as e:
                logger.error(f"❌ 执行切换回调失败: {e}")
        
        return False
    
    def _trigger_switch_callback(self, browser_id: str, old_account: Optional[str], new_account: str, success: bool):
        """触发切换回调"""
        for callback in self.switch_callbacks:
            try:
                callback(browser_id, old_account, new_account, success)
            except Exception as e:
                logger.error(f"❌ 切换回调异常: {e}")
    
    def _trigger_alert(self, alert_type: str, browser_id: str, **kwargs):
        """触发警报"""
        for callback in self.alert_callbacks:
            try:
                callback(alert_type, browser_id, **kwargs)
            except Exception as e:
                logger.error(f"❌ 警报回调异常: {e}")
    
    def record_email_sent(self, browser_id: str, account_email: str, send_time: float = 0):
        """记录邮件发送"""
        with self.monitor_lock:
            if account_email in self.account_status:
                self.account_status[account_email].update_send_stats(send_time)
                logger.debug(f"📊 记录发送: {account_email} (总计: {self.account_status[account_email].sent_count})")
    
    def add_switch_callback(self, callback: Callable):
        """添加切换回调函数"""
        self.switch_callbacks.append(callback)
    
    def add_alert_callback(self, callback: Callable):
        """添加警报回调函数"""
        self.alert_callbacks.append(callback)
    
    def get_monitor_stats(self) -> Dict[str, Any]:
        """获取监控统计"""
        runtime = time.time() - self.start_time if self.start_time > 0 else 0
        
        return {
            'total_switches': self.total_switches,
            'successful_switches': self.successful_switches,
            'failed_switches': self.failed_switches,
            'switch_success_rate': self.successful_switches / max(self.total_switches, 1) * 100,
            'runtime_seconds': runtime,
            'account_status': {email: {
                'sent_count': status.sent_count,
                'is_active': status.is_active,
                'browser_id': status.browser_id,
                'switch_count': status.switch_count,
                'avg_send_time': status.avg_send_time
            } for email, status in self.account_status.items()},
            'browser_bindings': {browser_id: {
                'current_account': binding.current_account,
                'assigned_accounts': binding.assigned_accounts,
                'switch_count': binding.switch_count
            } for browser_id, binding in self.browser_bindings.items()}
        }
    
    def force_switch_account(self, browser_id: str) -> bool:
        """强制切换指定浏览器的账号"""
        logger.info(f"🔄 强制切换浏览器 {browser_id} 的账号")
        self._trigger_account_switch(browser_id)
        return True
    
    def get_current_account(self, browser_id: str) -> Optional[str]:
        """获取指定浏览器的当前账号"""
        binding = self.browser_bindings.get(browser_id)
        return binding.current_account if binding else None
    
    def get_account_sent_count(self, account_email: str) -> int:
        """获取指定账号的发送数量"""
        status = self.account_status.get(account_email)
        return status.sent_count if status else 0
