#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试发送20封邮件的功能模块
从启动发送开始完整测试整个流程
"""

import sys
import os
import time
import json
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import get_logger
from src.core.email_sending_manager import EmailSendingManager, SendingConfig, SendingMode
from src.core.unified_email_sender import SendingStrategy
from src.core.smart_task_queue import TaskPriority
# from src.models.account import AccountManager  # 暂时不使用

logger = get_logger("EmailSendingTest")

class EmailSendingTest:
    """邮件发送测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        self.test_name = "发送20封邮件测试"
        self.email_count = 20
        self.browser_drivers = []
        self.accounts = []
        self.task_sending_manager = None
        self.test_results = {
            'total_emails': 0,
            'sent_success': 0,
            'sent_failed': 0,
            'start_time': None,
            'end_time': None,
            'duration': 0,
            'errors': []
        }
        
        logger.info(f"🧪 {self.test_name} 初始化完成")
    
    def setup_test_environment(self):
        """设置测试环境"""
        try:
            logger.info("🔧 设置测试环境...")
            
            # 1. 加载账号信息
            self.load_test_accounts()
            
            # 2. 创建测试浏览器
            self.create_test_browsers()
            
            # 3. 初始化发送管理器
            self.init_sending_manager()
            
            # 4. 准备测试邮件数据
            self.prepare_test_emails()
            
            logger.info("✅ 测试环境设置完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试环境设置失败: {e}")
            return False
    
    def load_test_accounts(self):
        """加载测试账号"""
        try:
            logger.info("📧 加载测试账号...")

            # 创建测试账号对象（简化版）
            from src.models.account import Account

            test_account_data = [
                {
                    'email': '<EMAIL>',
                    'password': 'test_password_1',
                    'status': 'active'
                },
                {
                    'email': '<EMAIL>',
                    'password': 'test_password_2',
                    'status': 'active'
                }
            ]

            self.accounts = []
            for data in test_account_data:
                account = Account()
                account.email = data['email']
                account.password = data['password']
                account.status = data['status']
                self.accounts.append(account)

            if not self.accounts:
                raise Exception("没有找到可用的测试账号")

            logger.info(f"✅ 加载了 {len(self.accounts)} 个测试账号:")
            for i, account in enumerate(self.accounts):
                logger.info(f"  账号{i+1}: {account.email}")

        except Exception as e:
            logger.error(f"❌ 加载测试账号失败: {e}")
            raise
    
    def create_test_browsers(self):
        """创建测试浏览器"""
        try:
            logger.info("🌐 创建测试浏览器...")
            
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            browser_count = min(len(self.accounts), 2)  # 最多2个浏览器
            
            for i in range(browser_count):
                logger.info(f"🌐 创建浏览器 {i+1}/{browser_count}")
                
                # 配置Chrome选项
                chrome_options = Options()
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                
                # 设置用户数据目录
                import tempfile
                temp_dir = tempfile.gettempdir()
                user_data_dir = os.path.join(temp_dir, f"test_chrome_profile_{i}_{int(time.time())}")
                chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
                
                # 设置窗口位置
                window_x = i * 420
                window_y = i * 350
                chrome_options.add_argument(f'--window-position={window_x},{window_y}')
                chrome_options.add_argument('--window-size=1200,800')
                
                # 创建浏览器
                driver = webdriver.Chrome(options=chrome_options)
                driver.set_page_load_timeout(30)
                driver.implicitly_wait(10)
                
                # 设置反检测
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                
                # 设置标题
                account_info = f" - {self.accounts[i].email}" if i < len(self.accounts) else f" - 测试浏览器{i+1}"
                driver.execute_script(f"document.title = '新浪邮箱测试{account_info}';")
                
                # 导航到新浪邮箱
                driver.get("https://mail.sina.com.cn")
                time.sleep(3)

                # 尝试应用Cookie登录
                if i < len(self.accounts):
                    account = self.accounts[i]
                    success = self._apply_cookies_and_login(driver, account, i+1)
                    if success:
                        logger.info(f"🔑 浏览器 {i+1} Cookie登录成功: {account.email}")
                    else:
                        logger.warning(f"⚠️ 浏览器 {i+1} Cookie登录失败: {account.email}")

                self.browser_drivers.append(driver)
                logger.info(f"✅ 浏览器 {i+1} 创建成功")
            
            logger.info(f"✅ 创建了 {len(self.browser_drivers)} 个测试浏览器")
            
        except Exception as e:
            logger.error(f"❌ 创建测试浏览器失败: {e}")
            raise

    def _apply_cookies_and_login(self, driver, account, browser_num):
        """应用Cookie并验证登录状态"""
        try:
            username = account.email
            logger.info(f"🔑 浏览器 {browser_num} 开始应用Cookie: {username}")

            # 获取Cookie管理器
            from src.core.cookie_manager import CookieManager

            # 创建配置
            config = {
                'performance.max_concurrent_sessions': 100,
                'session.timeout': 3600
            }
            cookie_manager = CookieManager(config)

            # 获取账号的Cookie
            cookie_data = cookie_manager.get_cookies(username)
            if not cookie_data or 'cookies' not in cookie_data:
                logger.warning(f"⚠️ 浏览器 {browser_num} 没有找到Cookie: {username}")
                return False

            cookies = cookie_data['cookies']

            # 确保在正确的域名下
            current_url = driver.current_url
            if "sina.com" not in current_url:
                driver.get("https://mail.sina.com.cn")
                time.sleep(2)

            # 应用Cookie
            logger.info(f"🍪 浏览器 {browser_num} 开始应用 {len(cookies)} 个Cookie")
            for cookie in cookies:
                try:
                    cookie_dict = {
                        'name': cookie.get('name'),
                        'value': cookie.get('value'),
                        'domain': cookie.get('domain', '.sina.com.cn'),
                        'path': cookie.get('path', '/'),
                    }

                    if 'secure' in cookie:
                        cookie_dict['secure'] = cookie['secure']
                    if 'httpOnly' in cookie:
                        cookie_dict['httpOnly'] = cookie['httpOnly']

                    driver.add_cookie(cookie_dict)

                except Exception as e:
                    logger.debug(f"跳过无效Cookie: {e}")
                    continue

            # 刷新页面以应用Cookie
            driver.refresh()
            time.sleep(3)

            # 强制导航到新浪邮箱（防止跳转到其他页面）
            current_url = driver.current_url
            if "mail.sina.com.cn" not in current_url:
                logger.warning(f"⚠️ 浏览器 {browser_num} 页面跳转异常: {current_url}")
                driver.get("https://mail.sina.com.cn")
                time.sleep(3)

            # 验证登录状态
            return self._verify_login_status(driver, username, browser_num)

        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} Cookie应用失败: {e}")
            return False

    def _verify_login_status(self, driver, username, browser_num):
        """验证登录状态"""
        try:
            logger.info(f"🔍 浏览器 {browser_num} 验证登录状态: {username}")

            # 检查页面标题和URL
            current_url = driver.current_url
            page_title = driver.title

            logger.info(f"📍 浏览器 {browser_num} 当前URL: {current_url}")
            logger.info(f"📄 浏览器 {browser_num} 页面标题: {page_title}")

            # 检查是否在邮箱主页
            if "mail.sina.com.cn" in current_url and "登录" not in page_title:
                # 尝试查找写信按钮或其他登录标识
                try:
                    # 查找写信按钮
                    write_buttons = driver.find_elements("xpath", "//a[contains(text(), '写信') or contains(@title, '写信')]")
                    if write_buttons:
                        logger.info(f"✅ 浏览器 {browser_num} 登录验证成功: 找到写信按钮")
                        return True

                    # 查找其他登录标识
                    user_elements = driver.find_elements("xpath", "//*[contains(text(), '收件箱') or contains(text(), '发件箱')]")
                    if user_elements:
                        logger.info(f"✅ 浏览器 {browser_num} 登录验证成功: 找到邮箱功能")
                        return True

                except Exception as e:
                    logger.debug(f"查找登录标识时出错: {e}")

            logger.warning(f"⚠️ 浏览器 {browser_num} 可能未登录，需要手动登录")
            return False

        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} 登录状态验证失败: {e}")
            return False

    def init_sending_manager(self):
        """初始化发送管理器"""
        try:
            logger.info("📧 初始化发送管理器...")
            
            # 创建发送配置
            config = SendingConfig(
                mode=SendingMode.MANUAL,
                strategy=SendingStrategy.STANDARD,
                concurrent_workers=len(self.browser_drivers),
                send_interval=2.0
            )
            
            # 创建发送管理器
            self.task_sending_manager = EmailSendingManager(config)
            
            # 创建发送器工厂
            self.current_driver_index = 0
            
            def create_test_sender():
                """创建测试发送器"""
                if not self.browser_drivers:
                    return None
                
                driver = self.browser_drivers[self.current_driver_index]
                account = self.accounts[self.current_driver_index] if self.current_driver_index < len(self.accounts) else None
                
                from src.core.unified_email_sender import UnifiedEmailSender
                sender = UnifiedEmailSender(driver, SendingStrategy.STANDARD)
                
                if account:
                    sender.account_info = account
                    logger.info(f"🔗 测试发送器绑定账号: {account.email}")
                
                self.current_driver_index = (self.current_driver_index + 1) % len(self.browser_drivers)
                return sender
            
            # 设置发送器工厂
            self.task_sending_manager.set_sender_factory(create_test_sender)
            
            logger.info("✅ 发送管理器初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 发送管理器初始化失败: {e}")
            raise
    
    def prepare_test_emails(self):
        """准备测试邮件数据"""
        try:
            logger.info(f"📝 准备 {self.email_count} 封测试邮件...")
            
            # 测试收件人列表
            test_recipients = [
                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"
            ]
            
            # 测试邮件模板
            test_template = {
                'subject': '🧪 测试邮件 - {index}',
                'content': '''
                <h2>这是一封测试邮件</h2>
                <p>邮件编号：{index}</p>
                <p>发送时间：{timestamp}</p>
                <p>测试内容：这是多浏览器邮件发送系统的功能测试。</p>
                <p>如果您收到此邮件，说明系统运行正常。</p>
                <hr>
                <small>此邮件由自动化测试系统发送</small>
                '''
            }
            
            # 创建任务数据
            tasks_data = []
            for i in range(self.email_count):
                recipient = test_recipients[i % len(test_recipients)]
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                
                task = {
                    'to_email': recipient,
                    'subject': test_template['subject'].format(index=i+1),
                    'content': test_template['content'].format(index=i+1, timestamp=timestamp),
                    'content_type': 'text/html',
                    'task_type': 'test_email'
                }
                tasks_data.append(task)
            
            # 添加任务到发送管理器
            batch_id = self.task_sending_manager.add_batch_tasks(
                tasks_data=tasks_data,
                batch_name=f"测试批次_{int(time.time())}",
                priority=TaskPriority.NORMAL
            )
            
            self.test_results['total_emails'] = len(tasks_data)
            logger.info(f"✅ 准备了 {len(tasks_data)} 封测试邮件，批次ID: {batch_id}")
            
        except Exception as e:
            logger.error(f"❌ 准备测试邮件失败: {e}")
            raise
    
    def run_sending_test(self):
        """运行发送测试"""
        try:
            logger.info("🚀 开始发送测试...")
            
            # 记录开始时间
            self.test_results['start_time'] = time.time()
            
            # 启动发送器
            success = self.task_sending_manager.start_sending()
            if not success:
                raise Exception("发送器启动失败")
            
            logger.info("✅ 发送器已启动，开始发送邮件...")
            
            # 监控发送进度
            self.monitor_sending_progress()
            
            # 记录结束时间
            self.test_results['end_time'] = time.time()
            self.test_results['duration'] = self.test_results['end_time'] - self.test_results['start_time']
            
            logger.info("✅ 发送测试完成")
            
        except Exception as e:
            logger.error(f"❌ 发送测试失败: {e}")
            self.test_results['errors'].append(str(e))
            raise
    
    def monitor_sending_progress(self):
        """监控发送进度"""
        try:
            logger.info("📊 开始监控发送进度...")
            
            max_wait_time = 300  # 最多等待5分钟
            check_interval = 5   # 每5秒检查一次
            elapsed_time = 0
            
            while elapsed_time < max_wait_time:
                # 获取发送状态
                status = self.task_sending_manager.get_sending_status()
                
                if status:
                    queue_stats = status.get('queue_stats', {})
                    total_tasks = queue_stats.get('total_tasks', 0)
                    completed_tasks = queue_stats.get('completed_tasks', 0)
                    failed_tasks = queue_stats.get('failed_tasks', 0)
                    
                    # 更新测试结果
                    self.test_results['sent_success'] = completed_tasks
                    self.test_results['sent_failed'] = failed_tasks
                    
                    logger.info(f"📊 发送进度: {completed_tasks + failed_tasks}/{total_tasks} "
                              f"(成功: {completed_tasks}, 失败: {failed_tasks})")
                    
                    # 检查是否完成
                    if completed_tasks + failed_tasks >= total_tasks:
                        logger.info("✅ 所有邮件发送完成")
                        break
                
                time.sleep(check_interval)
                elapsed_time += check_interval
            
            if elapsed_time >= max_wait_time:
                logger.warning("⚠️ 发送监控超时")
                
        except Exception as e:
            logger.error(f"❌ 监控发送进度失败: {e}")
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        try:
            logger.info("🧹 清理测试环境...")
            
            # 停止发送器
            if self.task_sending_manager:
                self.task_sending_manager.stop_sending()
            
            # 关闭浏览器
            for i, driver in enumerate(self.browser_drivers):
                try:
                    driver.quit()
                    logger.info(f"✅ 浏览器 {i+1} 已关闭")
                except Exception as e:
                    logger.warning(f"⚠️ 关闭浏览器 {i+1} 失败: {e}")
            
            self.browser_drivers.clear()
            logger.info("✅ 测试环境清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理测试环境失败: {e}")
    
    def generate_test_report(self):
        """生成测试报告"""
        try:
            logger.info("📋 生成测试报告...")
            
            # 计算成功率
            total_sent = self.test_results['sent_success'] + self.test_results['sent_failed']
            success_rate = (self.test_results['sent_success'] / total_sent * 100) if total_sent > 0 else 0
            
            # 生成报告
            report = f"""
=== {self.test_name} 报告 ===

📊 测试统计:
- 计划发送: {self.test_results['total_emails']} 封
- 实际发送: {total_sent} 封
- 发送成功: {self.test_results['sent_success']} 封
- 发送失败: {self.test_results['sent_failed']} 封
- 成功率: {success_rate:.1f}%

⏱️ 时间统计:
- 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.test_results['start_time']))}
- 结束时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.test_results['end_time']))}
- 总耗时: {self.test_results['duration']:.1f} 秒
- 平均速度: {total_sent / self.test_results['duration']:.2f} 封/秒

🌐 环境信息:
- 测试账号: {len(self.accounts)} 个
- 测试浏览器: {len(self.browser_drivers)} 个

❌ 错误信息:
{chr(10).join(self.test_results['errors']) if self.test_results['errors'] else '无错误'}

=== 测试结论 ===
{'✅ 测试通过' if success_rate >= 80 else '❌ 测试失败'}
"""
            
            # 保存报告到文件
            report_file = f"test_report_email_sending_{int(time.time())}.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"📋 测试报告已保存: {report_file}")
            print(report)
            
            return success_rate >= 80
            
        except Exception as e:
            logger.error(f"❌ 生成测试报告失败: {e}")
            return False
    
    def run_complete_test(self):
        """运行完整测试"""
        try:
            logger.info(f"🧪 开始 {self.test_name}")
            
            # 1. 设置测试环境
            if not self.setup_test_environment():
                return False
            
            # 2. 运行发送测试
            self.run_sending_test()
            
            # 3. 生成测试报告
            test_passed = self.generate_test_report()
            
            return test_passed
            
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}")
            self.test_results['errors'].append(str(e))
            return False
            
        finally:
            # 4. 清理测试环境
            self.cleanup_test_environment()

def main():
    """主函数"""
    try:
        logger.info("🧪 启动邮件发送测试程序")
        
        # 创建测试实例
        test = EmailSendingTest()
        
        # 运行完整测试
        success = test.run_complete_test()
        
        if success:
            logger.info("🎉 测试成功完成！")
            return 0
        else:
            logger.error("❌ 测试失败！")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 测试程序异常: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
