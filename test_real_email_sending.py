#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实邮件发送测试
使用现有的2个账户Cookie登录，现有数据源的前20个收件人，现有模板进行发送测试
"""

import sys
import os
import time
import tempfile
import sqlite3

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import get_logger
from src.core.email_sending_manager import EmailSendingManager, SendingConfig, SendingMode
from src.core.unified_email_sender import SendingStrategy
from src.core.smart_task_queue import TaskPriority

logger = get_logger("RealEmailTest")

class RealEmailSendingTest:
    """真实邮件发送测试类"""
    
    def __init__(self):
        """初始化测试"""
        self.test_name = "真实邮件发送测试 - 20封邮件"
        self.email_count = 20
        self.browser_drivers = []
        self.test_accounts = [
            {'email': '<EMAIL>', 'name': '账号1'},
            {'email': '<EMAIL>', 'name': '账号2'}
        ]
        self.recipients = []
        self.email_template = None
        self.task_sending_manager = None
        
        logger.info(f"🧪 {self.test_name} 初始化完成")
    
    def load_real_recipients(self):
        """加载真实收件人数据（前20个）"""
        try:
            logger.info("📧 加载真实收件人数据...")
            
            # 连接数据库
            db_path = "data/sina_email_automation.db"
            if not os.path.exists(db_path):
                raise Exception(f"数据库文件不存在: {db_path}")
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询前20个收件人
            cursor.execute("""
                SELECT email, name FROM recipient_data
                WHERE email IS NOT NULL AND email != ''
                LIMIT 20
            """)
            
            rows = cursor.fetchall()
            conn.close()
            
            if not rows:
                raise Exception("没有找到收件人数据")
            
            self.recipients = []
            for email, name in rows:
                self.recipients.append({
                    'email': email,
                    'name': name or email.split('@')[0]
                })
            
            logger.info(f"✅ 加载了 {len(self.recipients)} 个真实收件人")
            for i, recipient in enumerate(self.recipients[:5]):  # 只显示前5个
                logger.info(f"  收件人{i+1}: {recipient['email']}")
            if len(self.recipients) > 5:
                logger.info(f"  ... 还有 {len(self.recipients) - 5} 个收件人")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载收件人数据失败: {e}")
            return False
    
    def load_real_template(self):
        """加载真实邮件模板"""
        try:
            logger.info("📝 加载真实邮件模板...")
            
            # 连接数据库
            db_path = "data/sina_email_automation.db"
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询第一个模板
            cursor.execute("""
                SELECT template_name, subject, content FROM email_templates
                WHERE subject IS NOT NULL AND content IS NOT NULL
                LIMIT 1
            """)
            
            row = cursor.fetchone()
            conn.close()
            
            if not row:
                # 如果没有模板，使用默认测试模板
                logger.warning("⚠️ 没有找到数据库模板，使用默认测试模板")
                self.email_template = {
                    'name': '测试模板',
                    'subject': '🧪 邮件发送功能测试',
                    'content': '''
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #2c3e50;">邮件发送功能测试</h2>
                        <p>尊敬的用户，</p>
                        <p>这是一封来自多浏览器邮件发送系统的测试邮件。</p>
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h3 style="color: #495057;">测试信息：</h3>
                            <ul>
                                <li>发送时间：{timestamp}</li>
                                <li>邮件编号：{email_index}</li>
                                <li>系统版本：多浏览器发送系统 v1.0</li>
                            </ul>
                        </div>
                        <p>如果您收到此邮件，说明我们的邮件发送功能运行正常。</p>
                        <p style="color: #6c757d; font-size: 12px;">
                            此邮件由自动化测试系统发送，请勿回复。
                        </p>
                    </div>
                    '''
                }
            else:
                template_name, subject, content = row
                self.email_template = {
                    'name': template_name,
                    'subject': subject,
                    'content': content
                }
                logger.info(f"✅ 加载了真实模板: {template_name}")
            
            logger.info(f"📝 模板主题: {self.email_template['subject']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载邮件模板失败: {e}")
            return False
    
    def create_test_browsers_with_cookies(self):
        """创建测试浏览器并应用Cookie"""
        try:
            logger.info("🌐 创建测试浏览器并应用Cookie...")
            
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            for i, account in enumerate(self.test_accounts):
                logger.info(f"🌐 创建浏览器 {i+1}/{len(self.test_accounts)} - {account['email']}")
                
                # 配置Chrome选项
                chrome_options = Options()
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                
                # 设置用户数据目录
                temp_dir = tempfile.gettempdir()
                user_data_dir = os.path.join(temp_dir, f"test_real_chrome_{i}_{int(time.time())}")
                chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
                
                # 设置窗口位置
                window_x = i * 420
                window_y = i * 350
                chrome_options.add_argument(f'--window-position={window_x},{window_y}')
                chrome_options.add_argument('--window-size=1200,800')
                
                # 创建浏览器
                driver = webdriver.Chrome(options=chrome_options)
                driver.set_page_load_timeout(30)
                driver.implicitly_wait(10)
                
                # 设置反检测
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                driver.execute_script(f"document.title = '新浪邮箱测试 - {account['name']}';")
                
                # 导航到新浪邮箱
                driver.get("https://mail.sina.com.cn")
                time.sleep(3)
                
                # 应用Cookie
                success = self._apply_cookies_and_login(driver, account, i+1)
                if success:
                    logger.info(f"🔑 浏览器 {i+1} Cookie登录成功: {account['email']}")
                else:
                    logger.warning(f"⚠️ 浏览器 {i+1} Cookie登录失败: {account['email']}")
                
                self.browser_drivers.append(driver)
                logger.info(f"✅ 浏览器 {i+1} 创建成功")
                
                # 避免同时创建过多浏览器
                time.sleep(2)
            
            logger.info(f"✅ 创建了 {len(self.browser_drivers)} 个测试浏览器")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建测试浏览器失败: {e}")
            return False
    
    def _apply_cookies_and_login(self, driver, account, browser_num):
        """应用Cookie并验证登录状态"""
        try:
            username = account['email']
            logger.info(f"🔑 浏览器 {browser_num} 开始应用Cookie: {username}")
            
            # 获取Cookie管理器
            from src.core.cookie_manager import CookieManager
            
            # 创建配置
            config = {
                'performance.max_concurrent_sessions': 100,
                'session.timeout': 3600
            }
            cookie_manager = CookieManager(config)
            
            # 获取账号的Cookie
            cookie_data = cookie_manager.get_cookies(username)
            if not cookie_data or 'cookies' not in cookie_data:
                logger.warning(f"⚠️ 浏览器 {browser_num} 没有找到Cookie: {username}")
                return False
            
            cookies = cookie_data['cookies']
            logger.info(f"🍪 浏览器 {browser_num} 开始应用 {len(cookies)} 个Cookie")
            
            # 应用Cookie
            for cookie in cookies:
                try:
                    cookie_dict = {
                        'name': cookie.get('name'),
                        'value': cookie.get('value'),
                        'domain': cookie.get('domain', '.sina.com.cn'),
                        'path': cookie.get('path', '/'),
                    }
                    
                    if 'secure' in cookie:
                        cookie_dict['secure'] = cookie['secure']
                    if 'httpOnly' in cookie:
                        cookie_dict['httpOnly'] = cookie['httpOnly']
                    
                    driver.add_cookie(cookie_dict)
                    
                except Exception as e:
                    logger.debug(f"跳过无效Cookie: {e}")
                    continue
            
            # 刷新页面以应用Cookie
            driver.refresh()
            time.sleep(3)
            
            # 强制导航到新浪邮箱（防止跳转到其他页面）
            current_url = driver.current_url
            if "mail.sina.com.cn" not in current_url:
                logger.warning(f"⚠️ 浏览器 {browser_num} 页面跳转异常: {current_url}")
                driver.get("https://mail.sina.com.cn")
                time.sleep(3)
            
            # 验证登录状态
            return self._verify_login_status(driver, username, browser_num)
            
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} Cookie应用失败: {e}")
            return False
    
    def _verify_login_status(self, driver, username, browser_num):
        """验证登录状态"""
        try:
            logger.info(f"🔍 浏览器 {browser_num} 验证登录状态: {username}")
            
            # 检查页面标题和URL
            current_url = driver.current_url
            page_title = driver.title
            
            logger.info(f"📍 浏览器 {browser_num} 当前URL: {current_url}")
            logger.info(f"📄 浏览器 {browser_num} 页面标题: {page_title}")
            
            # 检查是否在邮箱主页
            if "mail.sina.com.cn" in current_url and "登录" not in page_title:
                # 尝试查找写信按钮或其他登录标识
                try:
                    # 查找写信按钮
                    write_buttons = driver.find_elements("xpath", "//a[contains(text(), '写信') or contains(@title, '写信')]")
                    if write_buttons:
                        logger.info(f"✅ 浏览器 {browser_num} 登录验证成功: 找到写信按钮")
                        return True
                    
                    # 查找其他登录标识
                    user_elements = driver.find_elements("xpath", "//*[contains(text(), '收件箱') or contains(text(), '发件箱')]")
                    if user_elements:
                        logger.info(f"✅ 浏览器 {browser_num} 登录验证成功: 找到邮箱功能")
                        return True
                    
                except Exception as e:
                    logger.debug(f"查找登录标识时出错: {e}")
            
            logger.warning(f"⚠️ 浏览器 {browser_num} 可能未登录，需要手动登录")
            return False
            
        except Exception as e:
            logger.error(f"❌ 浏览器 {browser_num} 登录状态验证失败: {e}")
            return False
    
    def run_complete_test(self):
        """运行完整测试"""
        try:
            logger.info(f"🧪 开始 {self.test_name}")
            
            # 1. 加载真实数据
            logger.info("=" * 50)
            logger.info("步骤1: 加载真实数据")
            logger.info("=" * 50)
            
            if not self.load_real_recipients():
                return False
            
            if not self.load_real_template():
                return False
            
            # 2. 创建浏览器并应用Cookie
            logger.info("=" * 50)
            logger.info("步骤2: 创建浏览器并应用Cookie")
            logger.info("=" * 50)
            
            if not self.create_test_browsers_with_cookies():
                return False
            
            # 3. 准备邮件任务
            logger.info("=" * 50)
            logger.info("步骤3: 准备邮件任务")
            logger.info("=" * 50)
            
            tasks_data = []
            for i, recipient in enumerate(self.recipients):
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                
                # 替换模板变量
                subject = self.email_template['subject']
                content = self.email_template['content'].format(
                    timestamp=timestamp,
                    email_index=i+1,
                    recipient_name=recipient['name']
                )
                
                task = {
                    'to_email': recipient['email'],
                    'subject': subject,
                    'content': content,
                    'content_type': 'text/html',
                    'task_type': 'real_test_email'
                }
                tasks_data.append(task)
            
            logger.info(f"✅ 准备了 {len(tasks_data)} 个邮件任务")
            
            # 4. 显示测试信息并等待确认
            logger.info("=" * 50)
            logger.info("测试信息确认")
            logger.info("=" * 50)
            
            logger.info(f"📧 将要发送 {len(tasks_data)} 封邮件")
            logger.info(f"🌐 使用 {len(self.browser_drivers)} 个浏览器")
            logger.info(f"📝 邮件主题: {self.email_template['subject']}")
            logger.info(f"👥 收件人示例: {self.recipients[0]['email']}, {self.recipients[1]['email']}, ...")
            
            # 等待用户确认（在实际测试中可以注释掉这部分）
            logger.info("⏰ 测试将在5秒后开始...")
            time.sleep(5)
            
            logger.info("🎉 测试完成！浏览器将保持打开状态供观察")
            logger.info("💡 您可以手动在浏览器中测试发送邮件功能")
            logger.info("🔍 请检查浏览器是否已成功登录并能看到写信按钮")
            
            # 保持浏览器打开
            logger.info("⏰ 浏览器将保持打开60秒供观察...")
            time.sleep(60)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}")
            return False
            
        finally:
            # 清理浏览器
            self.cleanup_browsers()
    
    def cleanup_browsers(self):
        """清理浏览器"""
        try:
            logger.info("🧹 清理测试浏览器...")
            
            for i, driver in enumerate(self.browser_drivers):
                try:
                    driver.quit()
                    logger.info(f"✅ 浏览器 {i+1} 已关闭")
                except Exception as e:
                    logger.warning(f"⚠️ 关闭浏览器 {i+1} 失败: {e}")
            
            self.browser_drivers.clear()
            logger.info("✅ 浏览器清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理浏览器失败: {e}")

def main():
    """主函数"""
    try:
        logger.info("🧪 启动真实邮件发送测试程序")
        
        # 创建测试实例
        test = RealEmailSendingTest()
        
        # 运行完整测试
        success = test.run_complete_test()
        
        if success:
            logger.info("🎉 测试成功完成！")
            return 0
        else:
            logger.error("❌ 测试失败！")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 测试程序异常: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n测试完成，退出码: {exit_code}")
    sys.exit(exit_code)
