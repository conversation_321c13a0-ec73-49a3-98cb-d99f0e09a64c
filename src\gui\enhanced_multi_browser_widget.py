#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强多浏览器发送器
保留所有原有功能，增加先添加任务再发送的流程
"""

import sys
import os
import time
import threading
from typing import List, Dict, Any, Optional
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from loguru import logger

# 导入原有组件
try:
    from src.core.email_sending_scheduler import EmailSendingScheduler, SendingConfig
    from src.core.multi_sender_manager import MultiSenderManager, MultiSenderConfig, RotationStrategy, IntervalStrategy
    from src.core.send_mode_manager import SendModeManager, SendModeConfig, SendMode, Priority
    from src.models.account import Account, AccountManager
    from src.models.database import DatabaseManager
    from src.gui.email_template_widget import EmailTemplateWidget
    from src.gui.send_statistics_widget import SendStatisticsWidget
    from src.gui.data_source_widget import DataSourceWidget
    from src.gui.import_template_widget import ImportTemplateWidget
    from src.utils.logger import setup_logger
except ImportError as e:
    logger.error(f"导入原有组件失败: {e}")

# 导入智能任务管理系统
try:
    from src.core.email_sending_manager import EmailSendingManager, SendingConfig as TaskSendingConfig, SendingMode as TaskSendingMode
    from src.core.smart_task_queue import TaskPriority
    from src.core.unified_email_sender import SendingStrategy
    from src.core.sender_factory import get_sender_factory
except ImportError as e:
    logger.error(f"导入智能任务管理系统失败: {e}")


class EnhancedMultiBrowserWidget(QWidget):
    """增强多浏览器发送器界面 - 保留所有原有功能"""
    
    # 信号定义
    stats_updated = pyqtSignal(dict)
    log_message = pyqtSignal(str, str)  # message, level
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        super().__init__()
        
        # 数据库管理器
        if db_manager is None:
            from src.utils.config_manager import ConfigManager
            config_manager = ConfigManager()
            db_path = config_manager.get_database_path()
            self.db_manager = DatabaseManager(str(db_path))
        else:
            self.db_manager = db_manager
        
        # 原有组件
        self.scheduler: Optional[EmailSendingScheduler] = None
        self.accounts: List[Account] = []
        self.is_sending = False
        
        # 新增：智能任务管理系统
        self.task_sending_manager = None
        self.task_mode_enabled = False  # 是否启用任务模式
        
        # 界面组件
        self.template_widget = None
        self.statistics_widget = None
        self.data_source_widget = None
        self.import_template_widget = None
        
        # 变量存储
        self.current_variables = {}
        
        # 多邮箱管理器
        self.multi_sender_manager: Optional[MultiSenderManager] = None
        
        # 发送模式管理器
        self.send_mode_manager: Optional[SendModeManager] = None
        
        # 定时器
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_stats_display)
        
        self.init_ui()
        self.connect_signals()
        self.load_templates()
        self.init_task_management()
        
        logger.info("🌐 增强多浏览器发送器初始化完成")
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("🚀 增强多浏览器邮件发送系统")
        self.setGeometry(100, 100, 1600, 1000)
        self.setMinimumSize(1400, 900)
        
        # 设置样式
        self.setStyleSheet("""
            QWidget {
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 9pt;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                font-size: 10pt;
                font-weight: bold;
            }
            QPushButton {
                background-color: #3498db;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }
            QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 6px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                border: 2px solid #3498db;
            }
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
            QCheckBox {
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 1px solid #bdc3c7;
            }
            QCheckBox::indicator:checked {
                background-color: #3498db;
                border: 1px solid #3498db;
            }
            QProgressBar {
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                text-align: center;
                background-color: #ecf0f1;
            }
            QProgressBar::chunk {
                background-color: #27ae60;
                border-radius: 5px;
            }
        """)
        
        # 主布局 - 使用水平分割器
        main_layout = QHBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧面板
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 1200])
        
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)
    
    def create_left_panel(self) -> QWidget:
        """创建左侧控制面板"""
        panel = QWidget()
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 发送模式选择
        mode_group = self.create_mode_selection_group()
        layout.addWidget(mode_group)
        
        # 快速配置区域
        config_group = self.create_quick_config_group()
        layout.addWidget(config_group)
        
        # 发送控制区域
        control_group = self.create_send_control_group()
        layout.addWidget(control_group)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout()
        scroll_layout.setContentsMargins(5, 5, 5, 5)
        scroll_layout.setSpacing(10)
        
        # 详细状态区域
        detailed_status = self.create_detailed_status_group()
        scroll_layout.addWidget(detailed_status)
        
        # 日志区域
        log_group = self.create_log_group()
        scroll_layout.addWidget(log_group)
        
        scroll_layout.addStretch()
        scroll_content.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_content)
        
        layout.addWidget(scroll_area)
        panel.setLayout(layout)
        return panel
    
    def create_mode_selection_group(self) -> QGroupBox:
        """创建发送模式选择组"""
        group = QGroupBox("🎯 发送模式")
        layout = QVBoxLayout()
        
        # 模式选择
        self.mode_radio_classic = QRadioButton("🌐 经典多浏览器发送")
        self.mode_radio_classic.setChecked(True)
        self.mode_radio_classic.toggled.connect(self.on_mode_changed)
        layout.addWidget(self.mode_radio_classic)
        
        self.mode_radio_task = QRadioButton("📋 智能任务管理发送")
        self.mode_radio_task.toggled.connect(self.on_mode_changed)
        layout.addWidget(self.mode_radio_task)
        
        # 模式说明
        mode_info = QLabel()
        mode_info.setWordWrap(True)
        mode_info.setStyleSheet("color: #666; font-size: 8pt; padding: 5px;")
        mode_info.setText(
            "• 经典模式：保留原有的直接发送流程\n"
            "• 任务模式：先添加任务，再点击发送"
        )
        layout.addWidget(mode_info)
        
        group.setLayout(layout)
        return group

    def create_quick_config_group(self) -> QGroupBox:
        """创建快速配置区域"""
        group = QGroupBox("⚙️ 快速配置")
        layout = QVBoxLayout()
        layout.setSpacing(12)

        # 基础设置
        basic_frame = QFrame()
        basic_frame.setFrameStyle(QFrame.Box)
        basic_frame.setStyleSheet("QFrame { background-color: white; border-radius: 6px; padding: 8px; }")
        basic_layout = QFormLayout()
        basic_layout.setSpacing(8)

        # 浏览器数量
        self.max_browsers_spin = QSpinBox()
        self.max_browsers_spin.setRange(1, 10)
        self.max_browsers_spin.setValue(3)
        self.max_browsers_spin.setToolTip("同时运行的浏览器数量")
        basic_layout.addRow("🌐 浏览器数量:", self.max_browsers_spin)

        # 发送间隔
        self.send_interval_spin = QDoubleSpinBox()
        self.send_interval_spin.setRange(0.1, 60.0)
        self.send_interval_spin.setValue(2.0)
        self.send_interval_spin.setSingleStep(0.1)
        self.send_interval_spin.setSuffix(" 秒")
        self.send_interval_spin.setToolTip("邮件发送间隔时间")
        basic_layout.addRow("⏱️ 发送间隔:", self.send_interval_spin)

        # 每账号发送数量
        self.emails_per_account_spin = QSpinBox()
        self.emails_per_account_spin.setRange(1, 100)
        self.emails_per_account_spin.setValue(5)
        self.emails_per_account_spin.setToolTip("每个账号发送的邮件数量")
        basic_layout.addRow("📧 每账号发送:", self.emails_per_account_spin)

        basic_frame.setLayout(basic_layout)
        layout.addWidget(basic_frame)

        # 高级设置
        self.advanced_group = QGroupBox("🔧 高级设置")
        self.advanced_group.setCheckable(True)
        self.advanced_group.setChecked(True)
        advanced_layout = self.create_advanced_config_layout()
        self.advanced_group.setLayout(advanced_layout)
        layout.addWidget(self.advanced_group)

        group.setLayout(layout)
        return group

    def create_advanced_config_layout(self) -> QVBoxLayout:
        """创建高级配置布局"""
        layout = QVBoxLayout()
        layout.setSpacing(8)

        # 多邮箱设置
        multi_frame = QFrame()
        multi_frame.setFrameStyle(QFrame.Box)
        multi_frame.setStyleSheet("QFrame { background-color: white; border-radius: 6px; padding: 6px; }")
        multi_layout = QFormLayout()
        multi_layout.setSpacing(4)

        # 每个邮箱发送数量
        self.emails_per_sender_spin = QSpinBox()
        self.emails_per_sender_spin.setRange(1, 100)
        self.emails_per_sender_spin.setValue(10)
        multi_layout.addRow("📮 每邮箱发送:", self.emails_per_sender_spin)

        # 轮换策略
        self.rotation_strategy_combo = QComboBox()
        self.rotation_strategy_combo.addItems([
            "顺序轮换", "随机轮换", "负载均衡", "按成功率轮换"
        ])
        multi_layout.addRow("🔄 轮换策略:", self.rotation_strategy_combo)

        # 间隔策略
        self.interval_strategy_combo = QComboBox()
        self.interval_strategy_combo.addItems([
            "固定间隔", "随机间隔", "递增间隔", "智能间隔"
        ])
        multi_layout.addRow("⏰ 间隔策略:", self.interval_strategy_combo)

        multi_frame.setLayout(multi_layout)
        layout.addWidget(multi_frame)

        # 发送模式设置
        mode_frame = QFrame()
        mode_frame.setFrameStyle(QFrame.Box)
        mode_frame.setStyleSheet("QFrame { background-color: white; border-radius: 6px; padding: 6px; }")
        mode_layout = QFormLayout()
        mode_layout.setSpacing(4)

        # 发送模式
        self.send_mode_combo = QComboBox()
        self.send_mode_combo.addItems([
            "单个逐渐发送", "批量逐渐发送", "并发发送", "定时发送"
        ])
        self.send_mode_combo.currentTextChanged.connect(self.on_send_mode_changed)
        mode_layout.addRow("📤 发送模式:", self.send_mode_combo)

        # 批量大小
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 50)
        self.batch_size_spin.setValue(5)
        self.batch_size_spin.setEnabled(False)
        mode_layout.addRow("📦 批量大小:", self.batch_size_spin)

        # 并发数量
        self.concurrent_count_spin = QSpinBox()
        self.concurrent_count_spin.setRange(1, 10)
        self.concurrent_count_spin.setValue(3)
        self.concurrent_count_spin.setEnabled(False)
        mode_layout.addRow("🔀 并发数量:", self.concurrent_count_spin)

        mode_frame.setLayout(mode_layout)
        layout.addWidget(mode_frame)

        # 其他设置
        other_frame = QFrame()
        other_frame.setFrameStyle(QFrame.Box)
        other_frame.setStyleSheet("QFrame { background-color: white; border-radius: 6px; padding: 6px; }")
        other_layout = QVBoxLayout()
        other_layout.setSpacing(4)

        # 复选框设置
        self.minimize_browsers_check = QCheckBox("🔽 最小化浏览器")
        self.minimize_browsers_check.setChecked(True)
        other_layout.addWidget(self.minimize_browsers_check)

        self.rotate_accounts_check = QCheckBox("🔄 轮换账号")
        self.rotate_accounts_check.setChecked(True)
        other_layout.addWidget(self.rotate_accounts_check)

        self.use_proxy_check = QCheckBox("🌐 使用代理")
        self.use_proxy_check.setChecked(False)
        other_layout.addWidget(self.use_proxy_check)

        self.auto_close_check = QCheckBox("🔒 自动关闭浏览器")
        self.auto_close_check.setChecked(True)
        other_layout.addWidget(self.auto_close_check)

        other_frame.setLayout(other_layout)
        layout.addWidget(other_frame)

        return layout

    def create_send_control_group(self) -> QGroupBox:
        """创建发送控制组"""
        group = QGroupBox("🚀 发送控制")
        layout = QVBoxLayout()
        layout.setSpacing(8)

        # 控制按钮
        btn_layout = QVBoxLayout()
        btn_layout.setSpacing(6)

        self.start_btn = QPushButton("🚀 开始发送")
        self.start_btn.clicked.connect(self.start_sending)
        self.start_btn.setStyleSheet("background-color: #27ae60;")
        btn_layout.addWidget(self.start_btn)

        self.pause_btn = QPushButton("⏸️ 暂停发送")
        self.pause_btn.clicked.connect(self.pause_sending)
        self.pause_btn.setEnabled(False)
        btn_layout.addWidget(self.pause_btn)

        self.resume_btn = QPushButton("▶️ 恢复发送")
        self.resume_btn.clicked.connect(self.resume_sending)
        self.resume_btn.setEnabled(False)
        btn_layout.addWidget(self.resume_btn)

        self.stop_btn = QPushButton("🛑 停止发送")
        self.stop_btn.clicked.connect(self.stop_sending)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("background-color: #e74c3c;")
        btn_layout.addWidget(self.stop_btn)

        layout.addLayout(btn_layout)

        # 进度显示
        progress_layout = QVBoxLayout()
        progress_layout.setSpacing(4)

        self.progress_label = QLabel("进度: 0%")
        self.progress_label.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(self.progress_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)

        layout.addLayout(progress_layout)

        group.setLayout(layout)
        return group

    def create_detailed_status_group(self) -> QGroupBox:
        """创建详细状态组"""
        group = QGroupBox("📊 发送状态")
        layout = QVBoxLayout()
        layout.setSpacing(6)

        # 状态标签
        self.status_label = QLabel("系统状态: 空闲")
        self.status_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.status_label)

        # 统计信息
        stats_layout = QGridLayout()
        stats_layout.setSpacing(4)

        # 统计标签
        self.total_tasks_label = QLabel("总任务: 0")
        self.completed_tasks_label = QLabel("已完成: 0")
        self.failed_tasks_label = QLabel("失败: 0")
        self.speed_label = QLabel("速度: 0/分钟")

        stats_layout.addWidget(self.total_tasks_label, 0, 0)
        stats_layout.addWidget(self.completed_tasks_label, 0, 1)
        stats_layout.addWidget(self.failed_tasks_label, 1, 0)
        stats_layout.addWidget(self.speed_label, 1, 1)

        layout.addLayout(stats_layout)

        group.setLayout(layout)
        return group

    def create_log_group(self) -> QGroupBox:
        """创建日志组"""
        group = QGroupBox("📝 发送日志")
        layout = QVBoxLayout()

        # 日志文本
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(150)
        self.log_text.setStyleSheet(
            "background-color: #2c3e50; color: #ecf0f1; "
            "font-family: 'Consolas', monospace; font-size: 8pt;"
        )
        layout.addWidget(self.log_text)

        # 日志控制
        log_btn_layout = QHBoxLayout()

        clear_log_btn = QPushButton("🧹 清空")
        clear_log_btn.setMaximumWidth(60)
        clear_log_btn.clicked.connect(self.clear_log)
        log_btn_layout.addWidget(clear_log_btn)

        log_btn_layout.addStretch()
        layout.addLayout(log_btn_layout)

        group.setLayout(layout)
        return group

    def create_right_panel(self) -> QWidget:
        """创建右侧主工作区域"""
        panel = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(10)

        # 创建主标签页
        self.main_tab_widget = QTabWidget()

        # 邮件发送标签页
        send_tab = self.create_send_tab()
        self.main_tab_widget.addTab(send_tab, "📧 邮件发送")

        # 模板管理标签页
        template_tab = self.create_template_tab()
        self.main_tab_widget.addTab(template_tab, "📝 模板管理")

        # 数据源管理标签页
        data_source_tab = self.create_data_source_tab()
        self.main_tab_widget.addTab(data_source_tab, "📊 数据源管理")

        # 导入模板标签页
        import_template_tab = self.create_import_template_tab()
        self.main_tab_widget.addTab(import_template_tab, "📥 导入模板")

        # 发送统计标签页
        statistics_tab = self.create_statistics_tab()
        self.main_tab_widget.addTab(statistics_tab, "📈 发送统计")

        # 任务管理标签页（新增）
        task_management_tab = self.create_task_management_tab()
        self.main_tab_widget.addTab(task_management_tab, "📋 任务管理")

        main_layout.addWidget(self.main_tab_widget)
        panel.setLayout(main_layout)
        return panel

    def create_send_tab(self) -> QWidget:
        """创建邮件发送选项卡"""
        tab = QWidget()
        layout = QVBoxLayout()
        layout.setSpacing(15)

        # 收件人设置组
        recipient_group = QGroupBox("📮 收件人设置")
        recipient_layout = QVBoxLayout()

        # 收件人输入方式选择
        input_method_layout = QHBoxLayout()

        self.manual_input_radio = QRadioButton("手动输入")
        self.manual_input_radio.setChecked(True)
        self.manual_input_radio.toggled.connect(self.on_input_method_changed)
        input_method_layout.addWidget(self.manual_input_radio)

        self.data_source_radio = QRadioButton("数据源选择")
        self.data_source_radio.toggled.connect(self.on_input_method_changed)
        input_method_layout.addWidget(self.data_source_radio)

        input_method_layout.addStretch()
        recipient_layout.addLayout(input_method_layout)

        # 手动输入区域
        self.manual_input_widget = QWidget()
        manual_layout = QVBoxLayout()

        # 收件人邮箱
        email_layout = QHBoxLayout()
        email_layout.addWidget(QLabel("收件人邮箱:"))
        self.recipient_emails_edit = QTextEdit()
        self.recipient_emails_edit.setPlaceholderText(
            "请输入收件人邮箱，每行一个，例如:\n"
            "<EMAIL>\n"
            "<EMAIL>\n"
            "<EMAIL>"
        )
        self.recipient_emails_edit.setMaximumHeight(100)
        email_layout.addWidget(self.recipient_emails_edit)
        manual_layout.addLayout(email_layout)

        # 抄送邮箱
        cc_layout = QHBoxLayout()
        cc_layout.addWidget(QLabel("抄送邮箱:"))
        self.cc_emails_edit = QLineEdit()
        self.cc_emails_edit.setPlaceholderText("多个邮箱用分号分隔")
        cc_layout.addWidget(self.cc_emails_edit)
        manual_layout.addLayout(cc_layout)

        self.manual_input_widget.setLayout(manual_layout)
        recipient_layout.addWidget(self.manual_input_widget)

        # 数据源选择区域
        self.data_source_widget_container = QWidget()
        data_source_layout = QVBoxLayout()

        data_source_select_layout = QHBoxLayout()
        data_source_select_layout.addWidget(QLabel("选择数据源:"))
        self.data_source_combo = QComboBox()
        self.data_source_combo.addItem("请选择数据源...")
        data_source_select_layout.addWidget(self.data_source_combo)

        refresh_data_btn = QPushButton("🔄 刷新")
        refresh_data_btn.clicked.connect(self.refresh_data_sources)
        data_source_select_layout.addWidget(refresh_data_btn)

        data_source_layout.addLayout(data_source_select_layout)

        self.data_source_widget_container.setLayout(data_source_layout)
        self.data_source_widget_container.setVisible(False)
        recipient_layout.addWidget(self.data_source_widget_container)

        recipient_group.setLayout(recipient_layout)
        layout.addWidget(recipient_group)

        # 邮件内容组
        content_group = QGroupBox("📝 邮件内容")
        content_layout = QFormLayout()

        # 邮件主题
        self.subject_edit = QLineEdit()
        self.subject_edit.setPlaceholderText("请输入邮件主题")
        content_layout.addRow("邮件主题:", self.subject_edit)

        # 邮件内容
        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("请输入邮件内容，支持HTML格式")
        self.content_edit.setMaximumHeight(200)
        content_layout.addRow("邮件内容:", self.content_edit)

        # 内容类型
        content_type_layout = QHBoxLayout()
        self.content_type_combo = QComboBox()
        self.content_type_combo.addItems(["text/plain", "text/html"])
        content_type_layout.addWidget(self.content_type_combo)

        # 模板选择
        template_select_layout = QHBoxLayout()
        template_select_layout.addWidget(QLabel("快速模板:"))
        self.template_combo = QComboBox()
        self.template_combo.addItem("选择模板...")
        self.template_combo.currentTextChanged.connect(self.on_template_selected)
        template_select_layout.addWidget(self.template_combo)

        content_type_layout.addLayout(template_select_layout)
        content_layout.addRow("内容类型:", content_type_layout)

        content_group.setLayout(content_layout)
        layout.addWidget(content_group)

        # 发送设置组
        send_settings_group = QGroupBox("⚙️ 发送设置")
        send_settings_layout = QFormLayout()

        # 发送优先级
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["低", "普通", "高", "紧急"])
        self.priority_combo.setCurrentText("普通")
        send_settings_layout.addRow("发送优先级:", self.priority_combo)

        # 发送延迟
        self.send_delay_spin = QSpinBox()
        self.send_delay_spin.setRange(0, 3600)
        self.send_delay_spin.setValue(0)
        self.send_delay_spin.setSuffix(" 秒")
        send_settings_layout.addRow("发送延迟:", self.send_delay_spin)

        send_settings_group.setLayout(send_settings_layout)
        layout.addWidget(send_settings_group)

        # 操作按钮
        button_layout = QHBoxLayout()

        preview_btn = QPushButton("👁️ 预览邮件")
        preview_btn.clicked.connect(self.preview_email)
        button_layout.addWidget(preview_btn)

        test_send_btn = QPushButton("🧪 测试发送")
        test_send_btn.clicked.connect(self.test_send)
        button_layout.addWidget(test_send_btn)

        button_layout.addStretch()

        save_template_btn = QPushButton("💾 保存为模板")
        save_template_btn.clicked.connect(self.save_as_template)
        button_layout.addWidget(save_template_btn)

        layout.addLayout(button_layout)

        tab.setLayout(layout)
        return tab

    def create_template_tab(self) -> QWidget:
        """创建模板管理选项卡"""
        try:
            self.template_widget = EmailTemplateWidget(self.db_manager)
            self.template_widget.template_selected.connect(self.on_template_widget_selected)
            return self.template_widget
        except Exception as e:
            logger.error(f"创建模板管理选项卡失败: {e}")
            tab = QWidget()
            layout = QVBoxLayout()
            label = QLabel("模板管理功能暂时不可用\n请检查相关依赖是否已安装")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            tab.setLayout(layout)
            return tab

    def create_statistics_tab(self) -> QWidget:
        """创建发送统计选项卡"""
        try:
            self.statistics_widget = SendStatisticsWidget(self.db_manager)
            return self.statistics_widget
        except Exception as e:
            logger.error(f"创建发送统计选项卡失败: {e}")
            tab = QWidget()
            layout = QVBoxLayout()
            label = QLabel("发送统计功能暂时不可用\n请检查相关依赖是否已安装")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            tab.setLayout(layout)
            return tab

    def create_data_source_tab(self) -> QWidget:
        """创建数据源管理选项卡"""
        try:
            self.data_source_widget = DataSourceWidget(self.db_manager)
            self.data_source_widget.recipients_selected.connect(self.on_recipients_selected)
            return self.data_source_widget
        except Exception as e:
            logger.error(f"创建数据源管理选项卡失败: {e}")
            tab = QWidget()
            layout = QVBoxLayout()
            label = QLabel("数据源管理功能暂时不可用\n请检查相关依赖是否已安装")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            tab.setLayout(layout)
            return tab

    def create_import_template_tab(self) -> QWidget:
        """创建导入模板选项卡"""
        try:
            self.import_template_widget = ImportTemplateWidget()
            self.import_template_widget.template_selected.connect(self.on_template_selected)
            return self.import_template_widget
        except Exception as e:
            logger.error(f"创建导入模板选项卡失败: {e}")
            tab = QWidget()
            layout = QVBoxLayout()
            label = QLabel("导入模板功能暂时不可用\n请检查相关依赖是否已安装")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
            tab.setLayout(layout)
            return tab

    def create_task_management_tab(self) -> QWidget:
        """创建任务管理选项卡"""
        tab = QWidget()
        layout = QVBoxLayout()

        # 任务管理说明
        info_group = QGroupBox("📋 智能任务管理")
        info_layout = QVBoxLayout()

        info_label = QLabel("""
        <h3>🎯 任务管理模式特色：</h3>
        <ul>
            <li><b>先添加任务，再发送</b> - 全新的发送流程</li>
            <li><b>智能队列管理</b> - 5级优先级智能调度</li>
            <li><b>批量处理</b> - 支持大数据量分批处理</li>
            <li><b>实时监控</b> - 完整的发送状态跟踪</li>
        </ul>

        <h3>📝 使用说明：</h3>
        <ol>
            <li>选择左侧的"📋 智能任务管理发送"模式</li>
            <li>在邮件发送标签页中配置邮件内容</li>
            <li>点击"➕ 添加到任务队列"按钮</li>
            <li>在发送控制区域点击"🚀 开始发送"</li>
        </ol>

        <p><i>任务管理模式提供更强大的控制和监控功能！</i></p>
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                font-size: 11px;
                line-height: 1.4;
            }
        """)
        info_layout.addWidget(info_label)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # 任务队列状态
        queue_group = QGroupBox("📊 任务队列状态")
        queue_layout = QVBoxLayout()

        # 任务统计
        stats_layout = QGridLayout()

        self.task_total_label = QLabel("总任务: 0")
        self.task_pending_label = QLabel("等待中: 0")
        self.task_processing_label = QLabel("处理中: 0")
        self.task_completed_label = QLabel("已完成: 0")
        self.task_failed_label = QLabel("失败: 0")

        stats_layout.addWidget(self.task_total_label, 0, 0)
        stats_layout.addWidget(self.task_pending_label, 0, 1)
        stats_layout.addWidget(self.task_processing_label, 1, 0)
        stats_layout.addWidget(self.task_completed_label, 1, 1)
        stats_layout.addWidget(self.task_failed_label, 2, 0)

        queue_layout.addLayout(stats_layout)

        # 任务操作按钮
        task_btn_layout = QHBoxLayout()

        refresh_tasks_btn = QPushButton("🔄 刷新任务")
        refresh_tasks_btn.clicked.connect(self.refresh_task_status)
        task_btn_layout.addWidget(refresh_tasks_btn)

        clear_completed_btn = QPushButton("🧹 清理已完成")
        clear_completed_btn.clicked.connect(self.clear_completed_tasks)
        task_btn_layout.addWidget(clear_completed_btn)

        task_btn_layout.addStretch()
        queue_layout.addLayout(task_btn_layout)

        queue_group.setLayout(queue_layout)
        layout.addWidget(queue_group)

        layout.addStretch()
        tab.setLayout(layout)
        return tab

    def init_task_management(self):
        """初始化任务管理系统"""
        try:
            # 创建发送配置
            config = TaskSendingConfig(
                mode=TaskSendingMode.MANUAL,
                strategy=SendingStrategy.ULTRA_FAST,
                concurrent_workers=3,
                send_interval=2.0
            )

            # 创建邮件发送管理器
            self.task_sending_manager = EmailSendingManager(config)

            # 设置发送器工厂
            factory = get_sender_factory()
            factory.initialize()
            self.task_sending_manager.set_sender_factory(lambda: factory.create_sender(config.strategy))

            logger.info("✅ 任务管理系统初始化完成")

        except Exception as e:
            logger.error(f"❌ 任务管理系统初始化失败: {e}")

    def connect_signals(self):
        """连接信号"""
        self.stats_updated.connect(self.on_stats_updated)
        self.log_message.connect(self.on_log_message)

    def load_templates(self):
        """加载模板"""
        try:
            # 这里可以加载模板到下拉框
            pass
        except Exception as e:
            logger.error(f"加载模板失败: {e}")

    def on_mode_changed(self):
        """发送模式改变"""
        if self.mode_radio_task.isChecked():
            self.task_mode_enabled = True
            self.log_message.emit("✅ 已切换到智能任务管理模式", "info")
        else:
            self.task_mode_enabled = False
            self.log_message.emit("✅ 已切换到经典多浏览器模式", "info")

    def on_send_mode_changed(self, mode_text: str):
        """发送模式改变时的处理"""
        if mode_text == "批量逐渐发送":
            self.batch_size_spin.setEnabled(True)
            self.concurrent_count_spin.setEnabled(False)
        elif mode_text == "并发发送":
            self.batch_size_spin.setEnabled(False)
            self.concurrent_count_spin.setEnabled(True)
        else:
            self.batch_size_spin.setEnabled(False)
            self.concurrent_count_spin.setEnabled(False)

        self.log_message.emit(f"发送模式已切换为: {mode_text}", "info")

    def on_input_method_changed(self):
        """输入方式改变"""
        if self.manual_input_radio.isChecked():
            self.manual_input_widget.setVisible(True)
            self.data_source_widget_container.setVisible(False)
        else:
            self.manual_input_widget.setVisible(False)
            self.data_source_widget_container.setVisible(True)

    def on_template_selected(self, template_data):
        """模板选择"""
        try:
            if template_data and isinstance(template_data, dict):
                if 'subject' in template_data:
                    self.subject_edit.setText(template_data['subject'])
                if 'content' in template_data:
                    self.content_edit.setPlainText(template_data['content'])

                self.log_message.emit("✅ 模板已应用", "info")
        except Exception as e:
            logger.error(f"应用模板失败: {e}")

    def on_template_widget_selected(self, template_data):
        """模板组件选择"""
        self.on_template_selected(template_data)

    def on_recipients_selected(self, recipients):
        """收件人选择"""
        try:
            if recipients:
                # 将收件人列表转换为文本
                emails_text = '\n'.join(recipients)
                self.recipient_emails_edit.setPlainText(emails_text)
                self.log_message.emit(f"✅ 已选择 {len(recipients)} 个收件人", "info")
        except Exception as e:
            logger.error(f"应用收件人失败: {e}")

    def refresh_data_sources(self):
        """刷新数据源"""
        try:
            # 这里可以刷新数据源列表
            self.log_message.emit("🔄 数据源已刷新", "info")
        except Exception as e:
            logger.error(f"刷新数据源失败: {e}")

    def preview_email(self):
        """预览邮件"""
        try:
            subject = self.subject_edit.text().strip()
            content = self.content_edit.toPlainText().strip()

            if not subject or not content:
                QMessageBox.warning(self, "警告", "请填写邮件主题和内容")
                return

            # 创建预览对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("📧 邮件预览")
            dialog.setModal(True)
            dialog.resize(600, 400)

            layout = QVBoxLayout()

            # 主题显示
            subject_label = QLabel(f"<b>主题:</b> {subject}")
            layout.addWidget(subject_label)

            # 内容显示
            content_text = QTextEdit()
            content_text.setReadOnly(True)
            content_text.setPlainText(content)
            layout.addWidget(content_text)

            # 按钮
            btn_layout = QHBoxLayout()
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            btn_layout.addStretch()
            btn_layout.addWidget(close_btn)
            layout.addLayout(btn_layout)

            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            logger.error(f"预览邮件失败: {e}")
            QMessageBox.critical(self, "错误", f"预览邮件失败:\n{e}")

    def test_send(self):
        """测试发送"""
        try:
            # 这里可以实现测试发送功能
            QMessageBox.information(self, "测试发送", "测试发送功能待实现")
        except Exception as e:
            logger.error(f"测试发送失败: {e}")

    def save_as_template(self):
        """保存为模板"""
        try:
            subject = self.subject_edit.text().strip()
            content = self.content_edit.toPlainText().strip()

            if not subject or not content:
                QMessageBox.warning(self, "警告", "请填写邮件主题和内容")
                return

            # 获取模板名称
            name, ok = QInputDialog.getText(self, "保存模板", "请输入模板名称:")
            if ok and name.strip():
                # 这里可以保存模板到数据库
                QMessageBox.information(self, "成功", f"模板 '{name}' 保存成功")
                self.log_message.emit(f"✅ 模板 '{name}' 已保存", "info")

        except Exception as e:
            logger.error(f"保存模板失败: {e}")
            QMessageBox.critical(self, "错误", f"保存模板失败:\n{e}")

    def start_sending(self):
        """开始发送"""
        try:
            if self.task_mode_enabled:
                # 任务管理模式：先添加任务到队列
                self.add_to_task_queue()
            else:
                # 经典模式：直接发送
                self.start_classic_sending()

        except Exception as e:
            logger.error(f"开始发送失败: {e}")
            QMessageBox.critical(self, "错误", f"开始发送失败:\n{e}")

    def add_to_task_queue(self):
        """添加到任务队列"""
        try:
            # 获取邮件内容
            subject = self.subject_edit.text().strip()
            content = self.content_edit.toPlainText().strip()

            if not subject or not content:
                QMessageBox.warning(self, "警告", "请填写邮件主题和内容")
                return

            # 获取收件人
            recipients = []
            if self.manual_input_radio.isChecked():
                emails_text = self.recipient_emails_edit.toPlainText().strip()
                if emails_text:
                    recipients = [email.strip() for email in emails_text.split('\n') if email.strip()]

            if not recipients:
                QMessageBox.warning(self, "警告", "请添加收件人邮箱")
                return

            # 转换优先级
            priority_map = {
                "低": TaskPriority.LOW,
                "普通": TaskPriority.NORMAL,
                "高": TaskPriority.HIGH,
                "紧急": TaskPriority.URGENT
            }
            priority = priority_map.get(self.priority_combo.currentText(), TaskPriority.NORMAL)

            # 添加批量任务
            tasks_data = []
            for email in recipients:
                tasks_data.append({
                    'to_email': email,
                    'subject': subject,
                    'content': content,
                    'content_type': self.content_type_combo.currentText()
                })

            batch_id = self.task_sending_manager.add_batch_tasks(
                tasks_data=tasks_data,
                batch_name=f"多浏览器批次_{int(time.time())}",
                priority=priority
            )

            self.log_message.emit(f"✅ 已添加 {len(tasks_data)} 个任务到队列", "info")
            QMessageBox.information(self, "成功", f"已添加 {len(tasks_data)} 个任务到队列\n批次ID: {batch_id}")

            # 刷新任务状态
            self.refresh_task_status()

        except Exception as e:
            logger.error(f"添加任务到队列失败: {e}")
            QMessageBox.critical(self, "错误", f"添加任务到队列失败:\n{e}")

    def start_classic_sending(self):
        """开始经典发送"""
        try:
            # 这里实现原有的经典发送逻辑
            self.log_message.emit("🚀 经典发送模式启动", "info")
            QMessageBox.information(self, "提示", "经典发送模式功能待完善")

        except Exception as e:
            logger.error(f"经典发送失败: {e}")

    def start_task_sending(self):
        """开始任务发送"""
        try:
            if not self.task_sending_manager:
                QMessageBox.warning(self, "警告", "任务管理系统未初始化")
                return

            # 检查是否有任务
            status = self.task_sending_manager.get_sending_status()
            if status['queue_stats']['total_tasks'] == 0:
                QMessageBox.warning(self, "警告", "没有待发送的任务，请先添加任务")
                return

            # 开始发送
            success = self.task_sending_manager.start_sending()

            if success:
                self.is_sending = True
                self.update_button_states()
                self.stats_timer.start(2000)

                self.log_message.emit("🚀 任务发送已开始", "info")
                self.status_label.setText("系统状态: 发送中")
            else:
                QMessageBox.warning(self, "警告", "启动发送失败")

        except Exception as e:
            logger.error(f"开始任务发送失败: {e}")
            QMessageBox.critical(self, "错误", f"开始任务发送失败:\n{e}")

    def pause_sending(self):
        """暂停发送"""
        try:
            if self.task_mode_enabled and self.task_sending_manager:
                success = self.task_sending_manager.pause_sending()
                if success:
                    self.update_button_states()
                    self.log_message.emit("⏸️ 发送已暂停", "info")
                    self.status_label.setText("系统状态: 已暂停")
            else:
                # 经典模式暂停
                self.log_message.emit("⏸️ 经典模式暂停功能待实现", "warning")
        except Exception as e:
            logger.error(f"暂停发送失败: {e}")

    def resume_sending(self):
        """恢复发送"""
        try:
            if self.task_mode_enabled and self.task_sending_manager:
                success = self.task_sending_manager.resume_sending()
                if success:
                    self.update_button_states()
                    self.log_message.emit("▶️ 发送已恢复", "info")
                    self.status_label.setText("系统状态: 发送中")
            else:
                # 经典模式恢复
                self.log_message.emit("▶️ 经典模式恢复功能待实现", "warning")
        except Exception as e:
            logger.error(f"恢复发送失败: {e}")

    def stop_sending(self):
        """停止发送"""
        try:
            if self.task_mode_enabled and self.task_sending_manager:
                success = self.task_sending_manager.stop_sending()
                if success:
                    self.is_sending = False
                    self.stats_timer.stop()
                    self.update_button_states()
                    self.log_message.emit("🛑 发送已停止", "info")
                    self.status_label.setText("系统状态: 空闲")
                    self.progress_bar.setValue(0)
            else:
                # 经典模式停止
                self.is_sending = False
                self.stats_timer.stop()
                self.update_button_states()
                self.log_message.emit("🛑 经典模式发送已停止", "info")
                self.status_label.setText("系统状态: 空闲")
        except Exception as e:
            logger.error(f"停止发送失败: {e}")

    def update_button_states(self):
        """更新按钮状态"""
        if self.task_mode_enabled and self.task_sending_manager:
            status = self.task_sending_manager.get_sending_status()
            sending_status = status['status']

            if sending_status == 'idle':
                self.start_btn.setEnabled(True)
                self.pause_btn.setEnabled(False)
                self.resume_btn.setEnabled(False)
                self.stop_btn.setEnabled(False)
            elif sending_status == 'sending':
                self.start_btn.setEnabled(False)
                self.pause_btn.setEnabled(True)
                self.resume_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)
            elif sending_status == 'paused':
                self.start_btn.setEnabled(False)
                self.pause_btn.setEnabled(False)
                self.resume_btn.setEnabled(True)
                self.stop_btn.setEnabled(True)
            else:
                self.start_btn.setEnabled(True)
                self.pause_btn.setEnabled(False)
                self.resume_btn.setEnabled(False)
                self.stop_btn.setEnabled(False)
        else:
            # 经典模式按钮状态
            if self.is_sending:
                self.start_btn.setEnabled(False)
                self.pause_btn.setEnabled(True)
                self.resume_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)
            else:
                self.start_btn.setEnabled(True)
                self.pause_btn.setEnabled(False)
                self.resume_btn.setEnabled(False)
                self.stop_btn.setEnabled(False)

    def refresh_task_status(self):
        """刷新任务状态"""
        try:
            if self.task_sending_manager:
                status = self.task_sending_manager.get_sending_status()
                queue_stats = status['queue_stats']

                # 更新任务统计标签
                self.task_total_label.setText(f"总任务: {queue_stats['total_tasks']}")
                self.task_pending_label.setText(f"等待中: {queue_stats['total_tasks'] - queue_stats['completed_tasks'] - queue_stats['failed_tasks']}")
                self.task_processing_label.setText("处理中: 0")  # 这个需要从队列获取
                self.task_completed_label.setText(f"已完成: {queue_stats['completed_tasks']}")
                self.task_failed_label.setText(f"失败: {queue_stats['failed_tasks']}")

        except Exception as e:
            logger.error(f"刷新任务状态失败: {e}")

    def clear_completed_tasks(self):
        """清理已完成任务"""
        try:
            reply = QMessageBox.question(
                self, "确认", "确定要清理所有已完成的任务吗？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 这里应该调用清理方法
                self.refresh_task_status()
                self.log_message.emit("🧹 已完成任务清理完成", "info")

        except Exception as e:
            logger.error(f"清理已完成任务失败: {e}")

    def update_stats_display(self):
        """更新统计显示"""
        try:
            if self.task_mode_enabled and self.task_sending_manager:
                status = self.task_sending_manager.get_sending_status()
                queue_stats = status['queue_stats']

                # 更新统计标签
                self.total_tasks_label.setText(f"总任务: {queue_stats['total_tasks']}")
                self.completed_tasks_label.setText(f"已完成: {queue_stats['completed_tasks']}")
                self.failed_tasks_label.setText(f"失败: {queue_stats['failed_tasks']}")

                # 计算进度
                total = queue_stats['total_tasks']
                completed = queue_stats['completed_tasks']

                if total > 0:
                    progress = int((completed / total) * 100)
                    self.progress_bar.setValue(progress)
                    self.progress_label.setText(f"进度: {progress}%")

                # 更新任务状态
                self.refresh_task_status()
            else:
                # 经典模式统计更新
                pass

        except Exception as e:
            logger.error(f"更新统计显示失败: {e}")

    def clear_log(self):
        """清空日志"""
        if self.log_text:
            self.log_text.clear()

    def on_log_message(self, message: str, level: str):
        """处理日志消息"""
        if not self.log_text:
            return

        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 根据级别设置颜色
        color_map = {
            "info": "#2ecc71",
            "warning": "#f39c12",
            "error": "#e74c3c",
            "success": "#27ae60"
        }
        color = color_map.get(level, "#ecf0f1")

        formatted_message = f'<span style="color: {color}">[{timestamp}] {message}</span>'
        self.log_text.append(formatted_message)

        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def on_stats_updated(self, stats: dict):
        """处理统计更新"""
        try:
            # 更新界面统计显示
            pass
        except Exception as e:
            logger.error(f"处理统计更新失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 停止发送
            if self.is_sending:
                self.stop_sending()

            # 停止定时器
            if self.stats_timer.isActive():
                self.stats_timer.stop()

            event.accept()

        except Exception as e:
            logger.error(f"关闭窗口失败: {e}")
            event.accept()
