#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器实例数据模型
定义浏览器实例的数据结构和状态管理
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Any, Tuple, Dict
from selenium import webdriver

@dataclass
class BrowserInstance:
    """浏览器实例数据类"""
    browser_id: str
    driver: Optional[webdriver.Chrome] = None
    status: str = "idle"  # idle, ready, busy, error, closed
    current_account: Optional[Any] = None  # Account对象
    proxy_info: Optional[Dict] = None  # 代理信息字典
    sent_count: int = 0
    last_activity: Optional[float] = None
    created_time: Optional[float] = None
    error_count: int = 0
    max_errors: int = 5
    window_size: Tuple[int, int] = (800, 600)
    is_minimized: bool = False
    
    def __post_init__(self):
        if self.created_time is None:
            self.created_time = datetime.now().timestamp()
        if self.last_activity is None:
            self.last_activity = datetime.now().timestamp()
    
    def is_available(self) -> bool:
        """检查浏览器是否可用"""
        return (self.status in ["idle", "ready"] and 
                self.driver is not None and 
                self.error_count < self.max_errors)
    
    def mark_busy(self):
        """标记为忙碌状态"""
        self.status = "busy"
        self.last_activity = datetime.now().timestamp()
    
    def mark_ready(self):
        """标记为就绪状态"""
        self.status = "ready"
        self.last_activity = datetime.now().timestamp()
    
    def mark_error(self, error_msg: str = ""):
        """标记错误状态"""
        self.status = "error"
        self.error_count += 1
        self.last_activity = datetime.now().timestamp()
    
    def reset_errors(self):
        """重置错误计数"""
        self.error_count = 0
        if self.status == "error":
            self.status = "idle"
    
    def update_activity(self):
        """更新活动时间"""
        self.last_activity = datetime.now().timestamp()
