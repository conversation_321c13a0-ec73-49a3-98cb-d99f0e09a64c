#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证多浏览器发送模块是否使用第一步策略
确保多浏览器发送严格按照5步顺序进行
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_multi_browser_strategy_usage():
    """检查多浏览器发送模块是否使用第一步策略"""
    print("🔍 检查多浏览器发送模块策略使用")
    print("=" * 60)
    
    check_results = []
    
    # 检查super_speed_sender_manager.py
    print("📋 检查 super_speed_sender_manager.py")
    try:
        with open("src/core/super_speed_sender_manager.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            ("UnifiedEmailSender" in content, "导入UnifiedEmailSender"),
            ("SendingStrategy.ULTRA_FAST" in content, "使用ULTRA_FAST策略"),
            ("unified_sender.send_email" in content, "调用统一发送器"),
            ("使用第一步策略（5步逻辑复刻）" in content, "包含5步逻辑说明"),
            ("严格按照5步顺序" in content, "包含5步顺序说明"),
            ("sender.send_email_ultra_fast" not in content, "已移除直接调用")
        ]
        
        for check, desc in checks:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            check_results.append(check)
            
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        check_results.append(False)
    
    # 检查concurrent_multi_browser_manager.py
    print("\n📋 检查 concurrent_multi_browser_manager.py")
    try:
        with open("src/core/concurrent_multi_browser_manager.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            ("UnifiedEmailSender" in content, "导入UnifiedEmailSender"),
            ("SendingStrategy.ULTRA_FAST" in content, "使用ULTRA_FAST策略"),
            ("unified_sender.send_email" in content, "调用统一发送器"),
            ("使用第一步策略（5步逻辑复刻）" in content, "包含5步逻辑说明"),
            ("严格按照5步顺序" in content, "包含5步顺序说明"),
            ("sender.send_email_ultra_fast" not in content, "已移除直接调用")
        ]
        
        for check, desc in checks:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            check_results.append(check)
            
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        check_results.append(False)
    
    # 检查optimized_multi_browser_widget.py
    print("\n📋 检查 optimized_multi_browser_widget.py")
    try:
        with open("src/gui/optimized_multi_browser_widget.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            ("SendingStrategy.ULTRA_FAST" in content, "使用ULTRA_FAST策略"),
            ("使用第一步策略（5步逻辑复刻）" in content, "包含5步逻辑说明"),
            ("SendingStrategy.STANDARD" not in content, "已移除STANDARD策略")
        ]
        
        for check, desc in checks:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            check_results.append(check)
            
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        check_results.append(False)
    
    return check_results

def check_first_strategy_implementation():
    """检查第一步策略实现"""
    print("\n📋 检查第一步策略实现")
    print("=" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender, SendingStrategy
        import inspect
        
        # 检查第一步策略方法
        source = inspect.getsource(UnifiedEmailSender._send_ultra_fast)
        
        checks = [
            ("纯粹的5步细节逻辑复刻" in source, "方法描述正确"),
            ("_execute_5_steps_logic" in source, "调用5步执行逻辑"),
            ("5步逻辑复刻成功" in source, "包含成功日志"),
            ("5步逻辑复刻失败" in source, "包含失败日志")
        ]
        
        results = []
        for check, desc in checks:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            results.append(check)
        
        # 检查5步执行逻辑
        exec_source = inspect.getsource(UnifiedEmailSender._execute_5_steps_logic)
        
        step_checks = [
            ("第1步：点击写信按钮" in exec_source, "第1步日志"),
            ("第2步：填写收件人" in exec_source, "第2步日志"),
            ("第3步：填写主题" in exec_source, "第3步日志"),
            ("第4步：填写内容" in exec_source, "第4步日志"),
            ("第5步：点击发送" in exec_source, "第5步日志"),
            ("_step1_click_compose" in exec_source, "调用第1步方法"),
            ("_step2_fill_recipient" in exec_source, "调用第2步方法"),
            ("_step3_fill_subject" in exec_source, "调用第3步方法"),
            ("_step4_fill_content" in exec_source, "调用第4步方法"),
            ("_step5_click_send" in exec_source, "调用第5步方法")
        ]
        
        for check, desc in step_checks:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            results.append(check)
        
        return results
        
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return [False]

def main():
    """主函数"""
    print("🎯 验证多浏览器发送模块第一步策略使用")
    print("目标：确保多浏览器发送严格按照5步顺序进行")
    print("流程：点击写信按钮 → 填写收件人 → 填写主题 → 填写内容 → 点击发送")
    
    # 检查多浏览器模块策略使用
    multi_browser_results = check_multi_browser_strategy_usage()
    
    # 检查第一步策略实现
    first_strategy_results = check_first_strategy_implementation()
    
    # 合并结果
    all_results = multi_browser_results + first_strategy_results
    
    # 结果汇总
    print("\n" + "=" * 60)
    print("📊 验证结果汇总:")
    print("=" * 60)
    
    passed = sum(all_results)
    total = len(all_results)
    success_rate = (passed / total) * 100 if total > 0 else 0
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    print(f"📈 成功率: {success_rate:.1f}%")
    
    if success_rate >= 95:
        print("\n🎉 验证完全成功！多浏览器发送已使用第一步策略！")
        print("✅ 多浏览器发送模块已修改为使用第一步策略")
        print("✅ 第一步策略包含完整的5步逻辑复刻")
        print("✅ 发送流程将严格按照5步顺序进行")
        print("✅ 与测试成功日志的流程完全一致")
        
        print("\n🎯 修改效果:")
        print("  📧 super_speed_sender_manager.py: 使用UnifiedEmailSender + ULTRA_FAST")
        print("  📧 concurrent_multi_browser_manager.py: 使用UnifiedEmailSender + ULTRA_FAST")
        print("  📧 optimized_multi_browser_widget.py: 使用ULTRA_FAST策略")
        print("  📧 第一步策略: 完整的5步逻辑复刻")
        
        print("\n📈 预期结果:")
        print("🎯 多浏览器发送将严格按照以下顺序:")
        print("  1. 点击写信按钮")
        print("  2. 填写收件人")
        print("  3. 填写主题")
        print("  4. 填写内容")
        print("  5. 点击发送")
        
    elif success_rate >= 80:
        print("\n✅ 验证基本成功，但有部分问题需要注意")
    else:
        print("\n❌ 验证失败，多浏览器发送模块可能未正确使用第一步策略")
    
    return 0 if success_rate >= 95 else 1

if __name__ == "__main__":
    sys.exit(main())
