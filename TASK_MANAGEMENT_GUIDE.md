# 📋 智能任务管理系统使用指南

## 🎯 系统概述

新的智能任务管理系统实现了**先添加任务，再点击发送**的全新流程，支持几十万封邮件的智能分批处理和强大的任务队列管理。

### 🔄 新流程 vs 旧流程

| 旧流程 | 新流程 |
|--------|--------|
| 导入数据 → 立即发送 | 添加任务 → 管理任务 → 点击发送 |
| 一次性处理所有数据 | 智能分批处理 |
| 简单队列管理 | 强大的任务队列系统 |
| 有限的控制选项 | 丰富的控制和监控功能 |

## 🚀 核心功能

### 1. 智能任务队列系统
- **5级优先级管理**：关键 > 紧急 > 高 > 普通 > 低
- **智能负载均衡**：自动分配工作线程负载
- **自动重试机制**：失败任务自动重试
- **状态跟踪**：完整的任务生命周期管理
- **数据持久化**：SQLite数据库存储

### 2. 大数据量分批处理
- **智能分批策略**：保守/平衡/激进/自定义
- **自适应调整**：根据成功率动态调整批次大小
- **内存优化**：分批加载，避免内存溢出
- **逐步分配**：发送完一批再分配下一批
- **支持规模**：理论支持几十万封邮件

### 3. 邮件发送管理
- **流程分离**：任务管理与发送执行完全分离
- **多种模式**：手动/自动/定时发送
- **并发发送**：可配置多工作线程并发
- **实时监控**：发送进度、速度、状态实时显示
- **灵活控制**：暂停、恢复、停止随时控制

## 📝 使用指南

### 场景一：单个邮件发送

1. **打开任务管理界面**
   ```
   启动程序 → 任务管理 → 单个任务标签页
   ```

2. **填写邮件信息**
   - 收件人邮箱：输入目标邮箱地址
   - 邮件主题：输入邮件主题
   - 邮件内容：输入邮件正文
   - 优先级：选择任务优先级（默认：普通）

3. **添加任务**
   ```
   点击"➕ 添加任务"按钮
   ```

4. **开始发送**
   ```
   配置发送参数 → 点击"🚀 开始发送"按钮
   ```

### 场景二：批量邮件发送

1. **准备批量数据**
   ```
   格式：邮箱,主题,内容
   示例：
   <EMAIL>,欢迎邮件,欢迎加入我们！
   <EMAIL>,通知邮件,重要通知内容
   ```

2. **批量输入**
   ```
   任务管理 → 批量导入标签页 → 输入批量数据
   ```

3. **配置批次**
   - 批次名称：为批次命名（可选）
   - 优先级：设置批次优先级

4. **添加批次**
   ```
   点击"📦 添加批次"按钮
   ```

5. **开始发送**
   ```
   配置发送参数 → 点击"🚀 开始发送"按钮
   ```

### 场景三：大数据量文件导入

1. **准备数据文件**
   ```
   支持格式：CSV, Excel (xlsx/xls)
   必须包含：email列（邮箱地址）
   可选列：name, company等（用于模板替换）
   ```

2. **文件导入**
   ```
   任务管理 → 文件导入标签页 → 选择文件
   ```

3. **设置邮件模板**
   - 主题模板：支持变量替换，如"Hello {name}"
   - 内容模板：支持变量替换，如"Dear {name}, welcome to {company}!"

4. **配置分批参数**
   ```
   分批大小：建议1000-5000（根据系统性能调整）
   ```

5. **导入并处理**
   ```
   点击"📥 导入文件" → 确认开始分批处理
   ```

6. **监控分批进度**
   ```
   系统自动分批创建任务 → 实时查看进度
   ```

7. **开始发送**
   ```
   分批完成后 → 配置发送参数 → 点击"🚀 开始发送"
   ```

## ⚙️ 发送配置

### 发送策略
- **超高速**：最快发送速度，适合大批量发送
- **标准**：平衡速度和稳定性
- **安全**：最稳定模式，速度较慢但成功率高

### 并发设置
- **并发数**：同时发送的工作线程数（建议2-5）
- **发送间隔**：每封邮件间的等待时间（秒）

### 高级配置
- **最大重试次数**：失败任务的重试次数
- **重试延迟**：重试前的等待时间
- **任务超时**：单个任务的最大执行时间

## 📊 监控和管理

### 实时状态监控
- **系统状态**：空闲/准备中/发送中/已暂停/已完成/错误
- **处理进度**：百分比进度显示
- **发送速度**：每分钟发送邮件数量
- **任务统计**：总任务/已完成/失败数量

### 批次管理
- **批次列表**：查看所有批次的状态和进度
- **批次操作**：暂停/恢复/取消特定批次
- **进度跟踪**：每个批次的详细进度信息

### 发送控制
- **开始发送**：启动邮件发送过程
- **暂停发送**：暂停当前发送，可随时恢复
- **恢复发送**：从暂停状态恢复发送
- **停止发送**：完全停止发送过程

## 🎯 最佳实践

### 大数据量处理建议

1. **分批大小设置**
   ```
   数据量 < 1万：批次大小 500-1000
   数据量 1-10万：批次大小 1000-2000
   数据量 > 10万：批次大小 2000-5000
   ```

2. **并发数配置**
   ```
   系统配置较低：并发数 2-3
   系统配置中等：并发数 3-5
   系统配置较高：并发数 5-8
   ```

3. **发送间隔设置**
   ```
   追求速度：间隔 1-2秒
   平衡模式：间隔 2-3秒
   稳定优先：间隔 3-5秒
   ```

### 性能优化建议

1. **内存管理**
   - 大数据量时使用文件导入而非批量输入
   - 及时清理已完成的任务
   - 监控系统内存使用情况

2. **网络优化**
   - 确保网络连接稳定
   - 根据网络状况调整发送间隔
   - 监控发送成功率

3. **系统稳定性**
   - 定期备份任务数据
   - 监控系统资源使用
   - 设置合理的重试参数

## 🔧 故障排除

### 常见问题

1. **文件导入失败**
   ```
   检查文件格式和编码
   确保包含email列
   检查数据完整性
   ```

2. **发送速度慢**
   ```
   增加并发数
   减少发送间隔
   检查网络连接
   ```

3. **任务失败率高**
   ```
   检查邮箱配置
   调整发送策略为"安全"
   增加重试次数
   ```

4. **系统卡顿**
   ```
   减少批次大小
   降低并发数
   清理已完成任务
   ```

### 错误代码

- **E001**：数据库连接失败
- **E002**：文件格式不支持
- **E003**：邮箱配置错误
- **E004**：网络连接超时
- **E005**：内存不足

## 📈 性能指标

### 处理能力
- **理论上限**：支持几十万封邮件
- **推荐规模**：单批次1-5万封邮件
- **并发能力**：最多8个工作线程
- **内存占用**：分批加载，内存可控

### 发送速度
- **超高速模式**：60-120封/分钟
- **标准模式**：30-60封/分钟
- **安全模式**：15-30封/分钟

### 成功率
- **正常情况**：95%以上
- **网络不稳定**：85-95%
- **配置错误**：可能低于50%

## 🎉 总结

新的智能任务管理系统提供了：

✅ **清晰的流程**：先添加任务，再点击发送  
✅ **强大的功能**：支持大数据量智能分批处理  
✅ **灵活的控制**：丰富的配置和监控选项  
✅ **优秀的性能**：高效的并发处理能力  
✅ **稳定的运行**：完善的错误处理和恢复机制  

通过这个系统，您可以轻松管理从几封到几十万封邮件的发送任务，享受专业级的邮件营销体验！

---

**📞 技术支持**  
如有问题，请查看日志文件或联系技术支持。

**📅 最后更新**  
2025-08-04
