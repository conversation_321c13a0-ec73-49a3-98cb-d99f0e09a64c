#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的超高速邮件发送器
第一阶段速度优化：目标从18秒优化到5秒以内
"""

import time
import json
from typing import Dict, List, Tuple, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from src.utils.logger import get_logger
from src.core.real_sending_verifier import RealSendingVerifier

logger = get_logger("OptimizedUltraFastSender")

class OptimizedUltraFastSender:
    """优化的超高速邮件发送器 - 第一阶段速度优化"""
    
    def __init__(self, driver: webdriver.Chrome):
        """
        初始化优化发送器
        
        Args:
            driver: Chrome浏览器驱动
        """
        self.driver = driver
        self.wait = WebDriverWait(driver, 3)  # 减少等待时间从10秒到3秒
        self.verifier = RealSendingVerifier(driver)
        
        # 优化的时间参数 - 大幅减少等待时间
        self.optimized_timings = {
            'page_load': 0.3,        # 页面加载等待：1.0s → 0.3s
            'element_wait': 0.2,     # 元素等待：0.5s → 0.2s
            'form_fill': 0.1,        # 表单填写：0.3s → 0.1s
            'send_click': 0.1,       # 发送点击：0.2s → 0.1s
            'verification': 1.0      # 验证等待：2.0s → 1.0s
        }
        
        # 性能统计
        self.performance_stats = {
            'total_attempts': 0,
            'ultra_fast_success': 0,
            'standard_success': 0,
            'safe_success': 0,
            'total_time': 0,
            'average_speed': 0,
            'fastest_time': float('inf'),
            'slowest_time': 0
        }
        
        # 预编译的JavaScript代码 - 避免重复编译
        self.precompiled_scripts = self._precompile_scripts()
    
    def _precompile_scripts(self) -> Dict[str, str]:
        """预编译JavaScript脚本，避免运行时编译"""
        return {
            'ultra_fast_send': """
                // 超高速发送脚本 - 预编译版本
                (function() {
                    console.log('🚀 启动超高速发送...');
                    
                    // 批量查找所有元素 - 一次性完成
                    const elements = {
                        to: document.querySelector('input[name="to"]') || 
                            document.querySelector('#to') ||
                            document.querySelector('.to-field') ||
                            document.querySelector('input[placeholder*="收件人"]') ||
                            document.querySelector('input[type="email"]'),
                        
                        subject: document.querySelector('input[name="subject"]') || 
                                document.querySelector('#subject') ||
                                document.querySelector('.subject-field') ||
                                document.querySelector('input[placeholder*="主题"]'),
                        
                        content: document.querySelector('textarea[name="content"]') || 
                                document.querySelector('#content') ||
                                document.querySelector('.content-editor') ||
                                document.querySelector('textarea[placeholder*="内容"]') ||
                                document.querySelector('div[contenteditable="true"]'),
                        
                        send: document.querySelector('button[type="submit"]') || 
                             document.querySelector('.send-button') ||
                             document.querySelector('#send') ||
                             document.querySelector('button[title*="发送"]') ||
                             document.querySelector('input[value*="发送"]') ||
                             document.querySelector('a[href*="send"]')
                    };
                    
                    console.log('📋 找到的元素:', elements);
                    
                    // 验证关键元素
                    if (!elements.to || !elements.subject || !elements.send) {
                        console.error('❌ 缺少关键元素');
                        return {success: false, error: '缺少关键元素', elements: elements};
                    }
                    
                    // 超高速填写 - 直接设置值，无延迟
                    try {
                        // 收件人
                        elements.to.focus();
                        elements.to.value = arguments[0];
                        elements.to.dispatchEvent(new Event('input', {bubbles: true}));
                        elements.to.dispatchEvent(new Event('change', {bubbles: true}));
                        
                        // 主题
                        elements.subject.focus();
                        elements.subject.value = arguments[1];
                        elements.subject.dispatchEvent(new Event('input', {bubbles: true}));
                        elements.subject.dispatchEvent(new Event('change', {bubbles: true}));
                        
                        // 内容（如果存在）
                        if (elements.content) {
                            elements.content.focus();
                            if (elements.content.tagName.toLowerCase() === 'div') {
                                elements.content.innerHTML = arguments[2];
                            } else {
                                elements.content.value = arguments[2];
                            }
                            elements.content.dispatchEvent(new Event('input', {bubbles: true}));
                            elements.content.dispatchEvent(new Event('change', {bubbles: true}));
                        }
                        
                        console.log('✅ 表单填写完成');
                        
                        // 立即点击发送 - 无延迟
                        elements.send.focus();
                        elements.send.click();
                        console.log('✅ 发送按钮已点击');
                        
                        return {success: true, method: 'ultra_fast_javascript', elements: elements};
                        
                    } catch(e) {
                        console.error('❌ 填写或发送失败:', e);
                        return {success: false, error: e.message, elements: elements};
                    }
                })();
            """,
            
            'quick_element_check': """
                // 快速元素检查脚本
                return {
                    hasToField: !!(document.querySelector('input[name="to"]') || document.querySelector('#to')),
                    hasSubjectField: !!(document.querySelector('input[name="subject"]') || document.querySelector('#subject')),
                    hasSendButton: !!(document.querySelector('button[type="submit"]') || document.querySelector('.send-button')),
                    isComposePage: window.location.href.includes('compose') || document.querySelector('.compose') !== null
                };
            """,
            
            'instant_clear': """
                // 瞬间清空所有字段
                const inputs = document.querySelectorAll('input[type="text"], input[type="email"], textarea, div[contenteditable="true"]');
                inputs.forEach(input => {
                    if (input.tagName.toLowerCase() === 'div') {
                        input.innerHTML = '';
                    } else {
                        input.value = '';
                    }
                    input.dispatchEvent(new Event('input', {bubbles: true}));
                });
                return inputs.length;
            """
        }
    
    def send_email_optimized(self, to_email: str, subject: str, content: str) -> Dict:
        """
        优化的邮件发送方法 - 目标5秒以内完成
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            content: 邮件内容
            
        Returns:
            Dict: 发送结果，包含真实验证信息
        """
        start_time = time.time()
        self.performance_stats['total_attempts'] += 1
        
        try:
            logger.info(f"🚀 开始优化发送: {to_email}")
            logger.info(f"📝 主题: {subject}")
            
            # 策略1: 超高速JavaScript发送 (目标: 2秒内)
            result = self._ultra_fast_strategy_optimized(to_email, subject, content)
            if result['success']:
                return self._finalize_result(result, start_time, 'ultra_fast')
            
            # 策略2: 优化标准发送 (目标: 4秒内)
            result = self._standard_strategy_optimized(to_email, subject, content)
            if result['success']:
                return self._finalize_result(result, start_time, 'standard')
            
            # 策略3: 安全发送 (目标: 6秒内)
            result = self._safe_strategy_optimized(to_email, subject, content)
            if result['success']:
                return self._finalize_result(result, start_time, 'safe')
            
            # 所有策略都失败
            total_time = time.time() - start_time
            failure_result = {
                'success': False,
                'error': '所有优化策略都失败',
                'total_time': total_time,
                'strategies_tried': 3
            }
            
            self._update_performance_stats(failure_result, 'failed')
            logger.error(f"❌ 所有优化策略都失败，总耗时: {total_time:.2f}秒")
            return failure_result
            
        except Exception as e:
            total_time = time.time() - start_time
            error_result = {
                'success': False,
                'error': f'发送异常: {str(e)}',
                'total_time': total_time
            }
            self._update_performance_stats(error_result, 'error')
            logger.error(f"❌ 发送异常: {e}")
            return error_result
    
    def _ultra_fast_strategy_optimized(self, to_email: str, subject: str, content: str) -> Dict:
        """超高速策略优化版 - 目标2秒内完成"""
        try:
            logger.info("⚡ 执行超高速策略优化版...")
            start_time = time.time()
            
            # 快速检查页面状态 - 0.1秒
            page_check = self.driver.execute_script(self.precompiled_scripts['quick_element_check'])
            if not page_check.get('hasToField') or not page_check.get('hasSendButton'):
                if not self._ensure_compose_page_fast():
                    return {'success': False, 'error': '无法快速进入写信页面'}
            
            # 瞬间清空字段 - 0.05秒
            cleared_count = self.driver.execute_script(self.precompiled_scripts['instant_clear'])
            logger.debug(f"清空了 {cleared_count} 个字段")
            
            # 超高速JavaScript发送 - 0.5秒
            result = self.driver.execute_script(
                self.precompiled_scripts['ultra_fast_send'], 
                to_email, subject, content
            )
            
            # 最小等待发送完成 - 0.2秒
            time.sleep(self.optimized_timings['send_click'])
            
            elapsed_time = time.time() - start_time
            
            if result and result.get('success'):
                logger.info(f"✅ 超高速策略成功，耗时: {elapsed_time:.2f}秒")
                return {'success': True, 'time': elapsed_time, 'method': 'ultra_fast_optimized'}
            else:
                error_msg = result.get('error', '未知错误') if result else 'JavaScript执行失败'
                logger.warning(f"❌ 超高速策略失败: {error_msg}")
                return {'success': False, 'error': error_msg, 'time': elapsed_time}
                
        except Exception as e:
            logger.error(f"❌ 超高速策略异常: {e}")
            return {'success': False, 'error': str(e)}
    
    def _standard_strategy_optimized(self, to_email: str, subject: str, content: str) -> Dict:
        """标准策略优化版 - 目标4秒内完成"""
        try:
            logger.info("🔧 执行标准策略优化版...")
            start_time = time.time()
            
            # 确保在写信页面 - 0.5秒
            if not self._ensure_compose_page_fast():
                return {'success': False, 'error': '无法进入写信页面'}
            
            # 优化的元素查找 - 使用更短的超时时间
            fast_wait = WebDriverWait(self.driver, 2)
            
            # 查找并填写收件人 - 0.5秒
            to_field = self._find_element_fast([
                "input[name='to']",
                "#to",
                ".to-field",
                "input[placeholder*='收件人']"
            ])
            
            if not to_field:
                return {'success': False, 'error': '未找到收件人字段'}
            
            to_field.clear()
            to_field.send_keys(to_email)
            time.sleep(self.optimized_timings['form_fill'])
            
            # 查找并填写主题 - 0.5秒
            subject_field = self._find_element_fast([
                "input[name='subject']",
                "#subject",
                ".subject-field",
                "input[placeholder*='主题']"
            ])
            
            if not subject_field:
                return {'success': False, 'error': '未找到主题字段'}
            
            subject_field.clear()
            subject_field.send_keys(subject)
            time.sleep(self.optimized_timings['form_fill'])
            
            # 查找并填写内容 - 0.5秒
            content_field = self._find_element_fast([
                "textarea[name='content']",
                "#content",
                ".content-editor",
                "textarea[placeholder*='内容']"
            ])
            
            if content_field:
                content_field.clear()
                content_field.send_keys(content)
                time.sleep(self.optimized_timings['form_fill'])
            
            # 查找并点击发送按钮 - 0.5秒
            send_button = self._find_element_fast([
                "button[type='submit']",
                ".send-button",
                "#send",
                "button[title*='发送']",
                "input[value*='发送']"
            ])
            
            if not send_button:
                return {'success': False, 'error': '未找到发送按钮'}
            
            send_button.click()
            time.sleep(self.optimized_timings['send_click'])
            
            elapsed_time = time.time() - start_time
            logger.info(f"✅ 标准策略优化版成功，耗时: {elapsed_time:.2f}秒")
            return {'success': True, 'time': elapsed_time, 'method': 'standard_optimized'}
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ 标准策略优化版异常: {e}")
            return {'success': False, 'error': str(e), 'time': elapsed_time}
    
    def _safe_strategy_optimized(self, to_email: str, subject: str, content: str) -> Dict:
        """安全策略优化版 - 目标6秒内完成"""
        try:
            logger.info("🛡️ 执行安全策略优化版...")
            start_time = time.time()
            
            # 页面刷新确保状态 - 1秒
            self.driver.refresh()
            time.sleep(1)
            
            # 使用标准策略的逻辑，但增加一些等待时间
            result = self._standard_strategy_optimized(to_email, subject, content)
            
            elapsed_time = time.time() - start_time
            if result['success']:
                logger.info(f"✅ 安全策略优化版成功，耗时: {elapsed_time:.2f}秒")
                return {'success': True, 'time': elapsed_time, 'method': 'safe_optimized'}
            else:
                logger.error(f"❌ 安全策略优化版失败，耗时: {elapsed_time:.2f}秒")
                return {'success': False, 'error': '安全策略失败', 'time': elapsed_time}
                
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ 安全策略优化版异常: {e}")
            return {'success': False, 'error': str(e), 'time': elapsed_time}
    
    def _ensure_compose_page_fast(self) -> bool:
        """快速确保在写信页面 - 目标0.5秒内"""
        try:
            current_url = self.driver.current_url.lower()
            
            # 如果已经在写信页面
            if 'compose' in current_url or 'write' in current_url:
                return True
            
            # 快速查找写信按钮
            write_button_selectors = [
                "//a[contains(text(), '写信')]",
                "//a[contains(@title, '写信')]",
                "//a[@href*='compose']",
                "//button[contains(text(), '写信')]"
            ]
            
            fast_wait = WebDriverWait(self.driver, 2)
            for selector in write_button_selectors:
                try:
                    write_button = fast_wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    write_button.click()
                    time.sleep(self.optimized_timings['page_load'])
                    return True
                except TimeoutException:
                    continue
            
            logger.warning("❌ 无法快速进入写信页面")
            return False
            
        except Exception as e:
            logger.error(f"❌ 快速确保写信页面失败: {e}")
            return False
    
    def _find_element_fast(self, selectors: List[str]):
        """快速查找元素 - 使用更短的超时时间"""
        for selector in selectors:
            try:
                element = self.driver.find_element(By.CSS_SELECTOR, selector)
                if element.is_displayed():
                    return element
            except NoSuchElementException:
                continue
        return None
    
    def _finalize_result(self, strategy_result: Dict, start_time: float, strategy_name: str) -> Dict:
        """完成结果处理，包括真实验证"""
        try:
            # 进行真实发送验证 - 优化验证时间
            is_verified, confidence, verification_details = self.verifier.verify_real_sending(
                timeout=int(self.optimized_timings['verification'] * 10)  # 转换为整数秒
            )
            
            # 计算总耗时
            total_time = time.time() - start_time
            
            # 构建最终结果
            final_result = {
                'success': is_verified,
                'strategy_used': strategy_name,
                'verification_confidence': confidence,
                'verification_details': verification_details,
                'total_time': total_time,
                'strategy_time': strategy_result.get('time', 0),
                'verification_time': verification_details.get('verification_time', 0)
            }
            
            # 更新性能统计
            self._update_performance_stats(final_result, strategy_name)
            
            if is_verified:
                logger.info(f"✅ 优化发送成功！策略: {strategy_name}, 置信度: {confidence:.2f}, 总耗时: {total_time:.2f}秒")
            else:
                logger.warning(f"❌ 发送验证失败！策略: {strategy_name}, 置信度: {confidence:.2f}")
            
            return final_result
            
        except Exception as e:
            logger.error(f"❌ 结果处理异常: {e}")
            total_time = time.time() - start_time
            return {
                'success': False,
                'error': f'结果处理异常: {str(e)}',
                'total_time': total_time
            }
    
    def _update_performance_stats(self, result: Dict, strategy: str):
        """更新性能统计"""
        total_time = result.get('total_time', 0)
        self.performance_stats['total_time'] += total_time
        
        if result.get('success'):
            if strategy == 'ultra_fast':
                self.performance_stats['ultra_fast_success'] += 1
            elif strategy == 'standard':
                self.performance_stats['standard_success'] += 1
            elif strategy == 'safe':
                self.performance_stats['safe_success'] += 1
            
            # 更新速度统计
            if total_time < self.performance_stats['fastest_time']:
                self.performance_stats['fastest_time'] = total_time
            if total_time > self.performance_stats['slowest_time']:
                self.performance_stats['slowest_time'] = total_time
        
        # 计算平均速度
        if self.performance_stats['total_attempts'] > 0:
            self.performance_stats['average_speed'] = (
                self.performance_stats['total_time'] / self.performance_stats['total_attempts']
            )
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        total_success = (
            self.performance_stats['ultra_fast_success'] + 
            self.performance_stats['standard_success'] + 
            self.performance_stats['safe_success']
        )
        
        success_rate = (total_success / self.performance_stats['total_attempts'] 
                       if self.performance_stats['total_attempts'] > 0 else 0)
        
        return {
            'total_attempts': self.performance_stats['total_attempts'],
            'total_success': total_success,
            'success_rate': f"{success_rate:.1%}",
            'average_speed': f"{self.performance_stats['average_speed']:.2f}秒",
            'fastest_time': f"{self.performance_stats['fastest_time']:.2f}秒" if self.performance_stats['fastest_time'] != float('inf') else "N/A",
            'slowest_time': f"{self.performance_stats['slowest_time']:.2f}秒",
            'ultra_fast_success': self.performance_stats['ultra_fast_success'],
            'standard_success': self.performance_stats['standard_success'],
            'safe_success': self.performance_stats['safe_success']
        }
