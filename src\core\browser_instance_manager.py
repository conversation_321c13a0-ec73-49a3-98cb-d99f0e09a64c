#!/usr/bin/env python3
"""
浏览器实例管理器
管理多个浏览器实例的创建、配置、登录和清理
"""

import time
import threading
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from src.utils.logger import setup_logger
from src.models.account import Account
from src.models.browser_instance import BrowserInstance
from src.core.cookie_manager import CookieManager

logger = setup_logger("INFO")

@dataclass
class BrowserConfig:
    """浏览器配置"""
    max_browsers: int = 3
    window_width: int = 800
    window_height: int = 600
    minimize_browsers: bool = True
    headless: bool = False
    disable_images: bool = True
    disable_javascript: bool = False
    user_agent: Optional[str] = None
    proxy_enabled: bool = False

class BrowserInstanceManager:
    """浏览器实例管理器"""
    
    def __init__(self, config: BrowserConfig):
        self.config = config
        self.browser_instances: Dict[str, BrowserInstance] = {}
        self.cookie_manager = CookieManager({
            'cookie_dir': 'data/cookies',
            'encryption_enabled': True,
            'max_age_days': 30
        })
        
        # 线程锁
        self.creation_lock = threading.Lock()
        
        logger.info("🚀 浏览器实例管理器初始化完成")
    
    def create_browser_instances(self, count: int) -> Dict[str, BrowserInstance]:
        """创建多个浏览器实例"""
        try:
            logger.info(f"🔧 开始创建 {count} 个浏览器实例...")
            
            if count <= 0:
                logger.error(f"❌ 浏览器数量无效: {count}")
                return {}
            
            # 清理现有实例
            self.cleanup_all_instances()
            
            created_instances = {}
            
            for i in range(count):
                browser_id = f"browser_{i+1}"
                
                try:
                    browser_instance = self._create_single_browser(browser_id)
                    if browser_instance:
                        created_instances[browser_id] = browser_instance
                        self.browser_instances[browser_id] = browser_instance
                        logger.info(f"✅ 浏览器 {browser_id} 创建成功")
                    else:
                        logger.error(f"❌ 浏览器 {browser_id} 创建失败")
                        
                except Exception as e:
                    logger.error(f"❌ 创建浏览器 {browser_id} 异常: {e}")
                    continue
            
            success_count = len(created_instances)
            logger.info(f"📊 浏览器创建完成: {success_count}/{count} 成功")
            
            return created_instances
            
        except Exception as e:
            logger.error(f"❌ 创建浏览器实例失败: {e}")
            return {}
    
    def _create_single_browser(self, browser_id: str) -> Optional[BrowserInstance]:
        """创建单个浏览器实例"""
        try:
            with self.creation_lock:
                logger.info(f"🌐 创建浏览器实例: {browser_id}")
                
                # 配置Chrome选项
                chrome_options = self._create_chrome_options()
                
                # 创建WebDriver服务
                service = Service()
                
                # 创建WebDriver
                driver = webdriver.Chrome(service=service, options=chrome_options)
                
                # 设置窗口属性
                if self.config.minimize_browsers:
                    driver.minimize_window()
                    is_minimized = True
                else:
                    driver.set_window_size(self.config.window_width, self.config.window_height)
                    is_minimized = False
                
                # 设置超时
                driver.implicitly_wait(10)
                driver.set_page_load_timeout(30)
                
                # 创建浏览器实例对象
                browser_instance = BrowserInstance(
                    browser_id=browser_id,
                    driver=driver,
                    proxy_info=None,
                    current_account=None,
                    window_size=(self.config.window_width, self.config.window_height),
                    is_minimized=is_minimized
                )
                
                logger.info(f"✅ 浏览器实例创建成功: {browser_id}")
                return browser_instance
                
        except Exception as e:
            logger.error(f"❌ 创建浏览器实例失败: {browser_id} - {e}")
            return None
    
    def _create_chrome_options(self) -> Options:
        """创建Chrome选项"""
        chrome_options = Options()
        
        # 基本选项
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        
        # 反检测选项
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 性能优化选项
        if self.config.disable_images:
            chrome_options.add_argument('--disable-images')
        
        if self.config.disable_javascript:
            chrome_options.add_argument('--disable-javascript')
        
        # 无头模式
        if self.config.headless:
            chrome_options.add_argument('--headless')
        
        # 自定义User-Agent
        if self.config.user_agent:
            chrome_options.add_argument(f'--user-agent={self.config.user_agent}')
        
        # 窗口大小
        if self.config.minimize_browsers:
            chrome_options.add_argument('--window-size=400,300')
        else:
            chrome_options.add_argument(f'--window-size={self.config.window_width},{self.config.window_height}')
        
        return chrome_options
    
    def login_browser_with_account(self, browser_id: str, account: Account) -> bool:
        """使用账号登录浏览器"""
        try:
            if browser_id not in self.browser_instances:
                logger.error(f"❌ 浏览器 {browser_id} 不存在")
                return False
            
            browser_instance = self.browser_instances[browser_id]
            logger.info(f"🔑 浏览器 {browser_id} 开始登录账号: {account.email}")
            
            # 导航到新浪邮箱
            if not self._navigate_to_sina_mail(browser_instance):
                logger.error(f"❌ 浏览器 {browser_id} 无法访问新浪邮箱")
                return False
            
            # 尝试Cookie登录
            if self._try_cookie_login(browser_instance, account):
                logger.info(f"🔑 浏览器 {browser_id} Cookie登录成功: {account.email}")
                browser_instance.current_account = account
                return True
            
            # Cookie登录失败，尝试手动登录
            if self._try_manual_login(browser_instance, account):
                logger.info(f"🔑 浏览器 {browser_id} 手动登录成功: {account.email}")
                browser_instance.current_account = account
                
                # 保存Cookie
                self._save_account_cookies(browser_instance, account)
                return True
            
            logger.error(f"❌ 浏览器 {browser_id} 登录失败: {account.email}")
            return False
            
        except Exception as e:
            logger.error(f"❌ 浏览器登录异常: {e}")
            return False
    
    def _navigate_to_sina_mail(self, browser_instance: BrowserInstance) -> bool:
        """导航到新浪邮箱"""
        try:
            driver = browser_instance.driver
            driver.get("https://mail.sina.com.cn/")
            
            # 等待页面加载
            WebDriverWait(driver, 10).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 导航到新浪邮箱失败: {e}")
            return False
    
    def _try_cookie_login(self, browser_instance: BrowserInstance, account: Account) -> bool:
        """尝试Cookie登录"""
        try:
            # 加载保存的Cookie
            cookies = self.cookie_manager.load_cookies(account.email)
            if not cookies:
                logger.info(f"📝 账号 {account.email} 没有保存的Cookie")
                return False
            
            driver = browser_instance.driver
            
            # 清除现有Cookie
            driver.delete_all_cookies()
            
            # 添加保存的Cookie
            for cookie in cookies:
                try:
                    driver.add_cookie(cookie)
                except Exception as e:
                    logger.warning(f"⚠️ 添加Cookie失败: {e}")
            
            # 刷新页面验证登录状态
            driver.refresh()
            time.sleep(3)
            
            # 检查是否登录成功
            return self._check_login_status(driver)
            
        except Exception as e:
            logger.error(f"❌ Cookie登录失败: {e}")
            return False
    
    def _try_manual_login(self, browser_instance: BrowserInstance, account: Account) -> bool:
        """尝试手动登录"""
        try:
            driver = browser_instance.driver
            
            # 查找登录表单
            email_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            password_input = driver.find_element(By.NAME, "password")
            login_button = driver.find_element(By.XPATH, "//input[@type='submit']")
            
            # 输入账号密码
            email_input.clear()
            email_input.send_keys(account.email)
            
            password_input.clear()
            password_input.send_keys(account.password)
            
            # 点击登录
            login_button.click()
            
            # 等待登录完成
            time.sleep(5)
            
            # 检查登录状态
            return self._check_login_status(driver)
            
        except Exception as e:
            logger.error(f"❌ 手动登录失败: {e}")
            return False
    
    def _check_login_status(self, driver) -> bool:
        """检查登录状态"""
        try:
            # 检查是否存在邮箱主界面的特征元素
            WebDriverWait(driver, 10).until(
                EC.any_of(
                    EC.presence_of_element_located((By.ID, "compose")),
                    EC.presence_of_element_located((By.CLASS_NAME, "compose")),
                    EC.presence_of_element_located((By.XPATH, "//a[contains(text(), '写邮件')]"))
                )
            )
            return True
            
        except Exception:
            return False
    
    def _save_account_cookies(self, browser_instance: BrowserInstance, account: Account):
        """保存账号Cookie"""
        try:
            driver = browser_instance.driver
            cookies = driver.get_cookies()
            self.cookie_manager.save_cookies(account.email, cookies)
            logger.info(f"💾 保存账号Cookie: {account.email}")
            
        except Exception as e:
            logger.error(f"❌ 保存Cookie失败: {e}")
    
    def switch_browser_account(self, browser_id: str, new_account: Account) -> bool:
        """切换浏览器账号"""
        try:
            if browser_id not in self.browser_instances:
                logger.error(f"❌ 浏览器 {browser_id} 不存在")
                return False
            
            browser_instance = self.browser_instances[browser_id]
            old_account = browser_instance.current_account.email if browser_instance.current_account else "None"
            
            logger.info(f"🔄 浏览器 {browser_id} 切换账号: {old_account} -> {new_account.email}")
            
            # 执行账号切换
            success = self.login_browser_with_account(browser_id, new_account)
            
            if success:
                logger.info(f"✅ 浏览器 {browser_id} 账号切换成功")
                return True
            else:
                logger.error(f"❌ 浏览器 {browser_id} 账号切换失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 切换浏览器账号异常: {e}")
            return False
    
    def get_browser_instance(self, browser_id: str) -> Optional[BrowserInstance]:
        """获取浏览器实例"""
        return self.browser_instances.get(browser_id)
    
    def get_all_browser_instances(self) -> Dict[str, BrowserInstance]:
        """获取所有浏览器实例"""
        return self.browser_instances.copy()
    
    def cleanup_browser_instance(self, browser_id: str):
        """清理单个浏览器实例"""
        try:
            if browser_id in self.browser_instances:
                browser_instance = self.browser_instances[browser_id]
                if browser_instance.driver:
                    browser_instance.driver.quit()
                del self.browser_instances[browser_id]
                logger.info(f"🧹 清理浏览器实例: {browser_id}")
                
        except Exception as e:
            logger.error(f"❌ 清理浏览器实例失败: {e}")
    
    def cleanup_all_instances(self):
        """清理所有浏览器实例"""
        logger.info("🧹 清理所有浏览器实例...")
        
        for browser_id in list(self.browser_instances.keys()):
            self.cleanup_browser_instance(browser_id)
        
        logger.info("✅ 所有浏览器实例清理完成")
    
    def get_instance_stats(self) -> Dict[str, Any]:
        """获取实例统计信息"""
        total_instances = len(self.browser_instances)
        logged_in_instances = 0
        
        for instance in self.browser_instances.values():
            if instance.current_account:
                logged_in_instances += 1
        
        return {
            'total_instances': total_instances,
            'logged_in_instances': logged_in_instances,
            'idle_instances': total_instances - logged_in_instances,
            'instance_details': {
                browser_id: {
                    'browser_id': browser_id,
                    'current_account': instance.current_account.email if instance.current_account else None,
                    'is_minimized': instance.is_minimized,
                    'window_size': instance.window_size
                }
                for browser_id, instance in self.browser_instances.items()
            }
        }
