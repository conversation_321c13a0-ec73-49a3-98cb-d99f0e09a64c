#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试第2步智能选择器修复
验证新的智能选择器能否正确找到可见的收件人字段
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def analyze_page_inputs():
    """分析页面输入框情况"""
    print("🔍 分析页面输入框情况")
    print("=" * 60)
    
    # 从日志中提取的页面输入框信息
    page_inputs = [
        {'name': 'to', 'type': 'text', 'visible': False},
        {'name': '', 'type': 'text', 'visible': True},
        {'name': 'cc', 'type': 'text', 'visible': False},
        {'name': '', 'type': 'text', 'visible': False},
        {'name': 'bcc', 'type': 'text', 'visible': False},
        {'name': '', 'type': 'text', 'visible': False},
        {'name': 'subj', 'type': 'text', 'visible': True},
        {'name': '', 'placeholder': '搜索联系人', 'type': 'text', 'visible': True},
        {'name': 'phrase', 'type': 'text', 'visible': True},
        {'name': 'phrase', 'type': 'text', 'visible': False}
    ]
    
    print("📋 页面text类型输入框分析:")
    for i, input_info in enumerate(page_inputs):
        name = input_info.get('name', '')
        placeholder = input_info.get('placeholder', '')
        visible = input_info.get('visible', False)
        
        status = "✅ 可见" if visible else "❌ 隐藏"
        purpose = ""
        
        if name == 'to':
            purpose = " (收件人字段-但隐藏)"
        elif name == 'subj':
            purpose = " (主题字段)"
        elif placeholder == '搜索联系人':
            purpose = " (联系人搜索)"
        elif name == 'phrase':
            purpose = " (搜索短语)"
        elif name == '' and visible and not placeholder:
            purpose = " (可能是实际收件人输入框)"
        
        print(f"  {i+1}. {status} name='{name}' placeholder='{placeholder}'{purpose}")
    
    print(f"\n🎯 关键发现:")
    print(f"  ❌ name='to'字段存在但隐藏")
    print(f"  ✅ 有可见的无name文本框（很可能是实际收件人输入框）")
    print(f"  ✅ name='subj'字段可见（主题字段正常）")
    
    return page_inputs

def test_new_selector_logic():
    """测试新的选择器逻辑"""
    print("\n🔍 测试新的智能选择器逻辑")
    print("=" * 60)
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        import inspect
        
        step2_source = inspect.getsource(UnifiedEmailSender._step2_fill_recipient)
        
        # 检查新的智能选择器特性
        new_features = [
            ("智能查找可见的收件人字段" in step2_source, "智能查找逻辑"),
            ("input.offsetParent !== null" in step2_source, "可见性检查"),
            ("name === 'to'" in step2_source, "优先级1: name='to'可见字段"),
            ("第一个可见的text输入框" in step2_source, "优先级2: 第一个可见文本框"),
            ("最终选择的收件人字段" in step2_source, "最终选择日志"),
            ("for (var i = 0; i < textInputs.length; i++)" in step2_source, "遍历所有text输入框")
        ]
        
        print("📋 新的智能选择器特性:")
        passed = 0
        for check, desc in new_features:
            status = "✅" if check else "❌"
            print(f"  {status} {desc}")
            if check:
                passed += 1
        
        print(f"\n📊 新特性完整性: {passed}/{len(new_features)}")
        
        return passed == len(new_features)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def simulate_selector_behavior():
    """模拟选择器行为"""
    print("\n🔍 模拟新选择器在实际页面上的行为")
    print("=" * 60)
    
    # 模拟页面输入框（基于实际日志）
    mock_inputs = [
        {'name': 'to', 'type': 'text', 'visible': False, 'index': 0},
        {'name': '', 'type': 'text', 'visible': True, 'index': 1},  # 这个很可能是实际收件人框
        {'name': 'cc', 'type': 'text', 'visible': False, 'index': 2},
        {'name': '', 'type': 'text', 'visible': False, 'index': 3},
        {'name': 'bcc', 'type': 'text', 'visible': False, 'index': 4},
        {'name': '', 'type': 'text', 'visible': False, 'index': 5},
        {'name': 'subj', 'type': 'text', 'visible': True, 'index': 6},
        {'name': '', 'placeholder': '搜索联系人', 'type': 'text', 'visible': True, 'index': 7},
        {'name': 'phrase', 'type': 'text', 'visible': True, 'index': 8}
    ]
    
    print("📋 模拟新选择器逻辑执行:")
    
    # 步骤1: 查找可见的input[type="text"]字段
    print("  🔍 步骤1: 遍历所有text输入框...")
    selected_field = None
    
    for input_info in mock_inputs:
        if input_info['type'] == 'text' and input_info['visible']:
            print(f"    发现可见字段: index={input_info['index']}, name='{input_info['name']}', placeholder='{input_info.get('placeholder', '')}'")
            
            # 优先级1: name="to"的可见字段
            if input_info['name'] == 'to':
                selected_field = input_info
                print(f"    🎯 优先级1匹配: 选择name='to'的可见字段")
                break
            
            # 优先级2: 第一个可见的text输入框
            if not selected_field:
                selected_field = input_info
                print(f"    🎯 优先级2匹配: 选择第一个可见的text输入框")
    
    if selected_field:
        print(f"\n✅ 最终选择结果:")
        print(f"  📍 选择字段: index={selected_field['index']}")
        print(f"  📝 name='{selected_field['name']}'")
        print(f"  👁️ visible={selected_field['visible']}")
        print(f"  💡 placeholder='{selected_field.get('placeholder', '')}'")
        
        if selected_field['index'] == 1:
            print(f"\n🎉 预期结果: 选择了index=1的字段（很可能是实际收件人输入框）")
            print(f"✅ 这应该能解决'收件人字段不可见'的问题")
        else:
            print(f"\n⚠️ 意外结果: 选择了index={selected_field['index']}的字段")
    else:
        print(f"\n❌ 未找到合适的字段")
    
    return selected_field is not None and selected_field['index'] == 1

def main():
    """主函数"""
    print("🎯 测试第2步智能选择器修复")
    print("目标：解决'收件人字段不可见'问题")
    print("方案：智能选择第一个可见的text输入框作为收件人字段")
    print("=" * 80)
    
    # 分析页面输入框情况
    page_inputs = analyze_page_inputs()
    
    # 测试新的选择器逻辑
    logic_ok = test_new_selector_logic()
    
    # 模拟选择器行为
    behavior_ok = simulate_selector_behavior()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 修复效果评估")
    print("=" * 80)
    
    if logic_ok and behavior_ok:
        print("🎉 修复预期成功！")
        print("✅ 新的智能选择器逻辑已实现")
        print("✅ 模拟测试显示能正确选择可见字段")
        print("✅ 应该能解决'收件人字段不可见'问题")
        
        print("\n🎯 修复原理:")
        print("  1. 🔍 遍历所有text类型输入框")
        print("  2. ✅ 只考虑可见的字段（offsetParent !== null）")
        print("  3. 🎯 优先选择name='to'的可见字段")
        print("  4. 🎯 备选第一个可见的text输入框")
        print("  5. 📝 详细日志记录选择过程")
        
        print("\n🚀 下一步:")
        print("请重新运行多浏览器发送测试，观察第2步是否成功")
        
    else:
        print("❌ 修复可能不完整")
        if not logic_ok:
            print("❌ 智能选择器逻辑实现不完整")
        if not behavior_ok:
            print("❌ 模拟测试未达到预期效果")
    
    return 0 if (logic_ok and behavior_ok) else 1

if __name__ == "__main__":
    sys.exit(main())
