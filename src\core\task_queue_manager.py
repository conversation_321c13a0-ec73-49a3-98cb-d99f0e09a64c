#!/usr/bin/env python3
"""
任务队列管理器 - 重新架构版本
实现完整的任务队列管理系统，支持任务添加、状态管理、批量处理等功能
"""

import time
import threading
import queue
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from enum import Enum

from src.utils.logger import setup_logger

logger = setup_logger("INFO")

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待中
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"   # 已取消

@dataclass
class EmailTask:
    """邮件任务数据类"""
    task_id: str
    to_email: str
    subject: str
    content: str
    content_type: str = "html"
    status: TaskStatus = TaskStatus.PENDING
    create_time: datetime = field(default_factory=datetime.now)
    start_time: Optional[datetime] = None
    complete_time: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'to_email': self.to_email,
            'subject': self.subject,
            'content': self.content,
            'content_type': self.content_type,
            'status': self.status.value,
            'create_time': self.create_time.isoformat(),
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'complete_time': self.complete_time.isoformat() if self.complete_time else None,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries
        }

class TaskQueueManager:
    """任务队列管理器"""
    
    def __init__(self, max_concurrent_tasks: int = 3, batch_size: int = 10):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.batch_size = batch_size
        
        # 任务存储
        self.tasks: Dict[str, EmailTask] = {}
        self.task_queue = queue.Queue()
        self.processing_tasks: Dict[str, EmailTask] = {}
        
        # 状态管理
        self.is_running = False
        self.is_paused = False
        self.worker_threads: List[threading.Thread] = []
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'pending_tasks': 0,
            'processing_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'cancelled_tasks': 0
        }
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 回调函数
        self.on_task_status_changed = None
        self.on_stats_updated = None
        
        logger.info("任务队列管理器初始化完成")
    
    def add_task(self, to_email: str, subject: str, content: str, content_type: str = "html") -> str:
        """添加单个任务"""
        task_id = f"task_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        
        task = EmailTask(
            task_id=task_id,
            to_email=to_email,
            subject=subject,
            content=content,
            content_type=content_type
        )
        
        with self.lock:
            self.tasks[task_id] = task
            self.task_queue.put(task)
            self.stats['total_tasks'] += 1
            self.stats['pending_tasks'] += 1
        
        logger.info(f"添加任务到队列: {task_id} -> {to_email}")
        
        # 触发回调
        if self.on_task_status_changed:
            self.on_task_status_changed(task)
        if self.on_stats_updated:
            self.on_stats_updated(self.stats.copy())
        
        return task_id
    
    def add_batch_tasks(self, tasks_data: List[Dict[str, str]]) -> List[str]:
        """批量添加任务"""
        task_ids = []
        
        for task_data in tasks_data:
            task_id = self.add_task(
                to_email=task_data['to_email'],
                subject=task_data['subject'],
                content=task_data['content'],
                content_type=task_data.get('content_type', 'html')
            )
            task_ids.append(task_id)
        
        logger.info(f"批量添加 {len(task_ids)} 个任务到队列")
        return task_ids
    
    def get_task(self, task_id: str) -> Optional[EmailTask]:
        """获取任务"""
        with self.lock:
            return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> List[EmailTask]:
        """获取所有任务"""
        with self.lock:
            return list(self.tasks.values())
    
    def get_tasks_by_status(self, status: TaskStatus) -> List[EmailTask]:
        """根据状态获取任务"""
        with self.lock:
            return [task for task in self.tasks.values() if task.status == status]
    
    def update_task_status(self, task_id: str, status: TaskStatus, error_message: str = None):
        """更新任务状态"""
        with self.lock:
            task = self.tasks.get(task_id)
            if not task:
                return
            
            old_status = task.status
            task.status = status
            
            if status == TaskStatus.PROCESSING:
                task.start_time = datetime.now()
                self.processing_tasks[task_id] = task
                self.stats['pending_tasks'] -= 1
                self.stats['processing_tasks'] += 1
            
            elif status == TaskStatus.COMPLETED:
                task.complete_time = datetime.now()
                if task_id in self.processing_tasks:
                    del self.processing_tasks[task_id]
                self.stats['processing_tasks'] -= 1
                self.stats['completed_tasks'] += 1
            
            elif status == TaskStatus.FAILED:
                task.complete_time = datetime.now()
                task.error_message = error_message
                if task_id in self.processing_tasks:
                    del self.processing_tasks[task_id]
                self.stats['processing_tasks'] -= 1
                self.stats['failed_tasks'] += 1
            
            elif status == TaskStatus.CANCELLED:
                task.complete_time = datetime.now()
                if task_id in self.processing_tasks:
                    del self.processing_tasks[task_id]
                    self.stats['processing_tasks'] -= 1
                else:
                    self.stats['pending_tasks'] -= 1
                self.stats['cancelled_tasks'] += 1
        
        logger.info(f"任务状态更新: {task_id} {old_status.value} -> {status.value}")
        
        # 触发回调
        if self.on_task_status_changed:
            self.on_task_status_changed(task)
        if self.on_stats_updated:
            self.on_stats_updated(self.stats.copy())
    
    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        with self.lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            # 只能移除未开始处理的任务
            if task.status == TaskStatus.PROCESSING:
                logger.warning(f"无法移除正在处理的任务: {task_id}")
                return False
            
            # 更新统计
            if task.status == TaskStatus.PENDING:
                self.stats['pending_tasks'] -= 1
            elif task.status == TaskStatus.COMPLETED:
                self.stats['completed_tasks'] -= 1
            elif task.status == TaskStatus.FAILED:
                self.stats['failed_tasks'] -= 1
            elif task.status == TaskStatus.CANCELLED:
                self.stats['cancelled_tasks'] -= 1
            
            self.stats['total_tasks'] -= 1
            
            del self.tasks[task_id]
            
            logger.info(f"移除任务: {task_id}")
            
            # 触发回调
            if self.on_stats_updated:
                self.on_stats_updated(self.stats.copy())
            
            return True
    
    def clear_completed_tasks(self):
        """清理已完成的任务"""
        with self.lock:
            completed_tasks = [task_id for task_id, task in self.tasks.items() 
                             if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]]
            
            for task_id in completed_tasks:
                self.remove_task(task_id)
            
            logger.info(f"清理了 {len(completed_tasks)} 个已完成的任务")
    
    def get_stats(self) -> Dict[str, int]:
        """获取统计信息"""
        with self.lock:
            return self.stats.copy()
    
    def pause(self):
        """暂停处理"""
        self.is_paused = True
        logger.info("任务队列已暂停")
    
    def resume(self):
        """恢复处理"""
        self.is_paused = False
        logger.info("任务队列已恢复")
    
    def stop(self):
        """停止任务队列"""
        logger.info("正在停止任务队列...")
        self.is_running = False
        
        # 等待工作线程结束
        for thread in self.worker_threads:
            if thread.is_alive():
                thread.join(timeout=5)
        
        self.worker_threads.clear()
        logger.info("任务队列已停止")
    
    def get_queue_size(self) -> int:
        """获取队列大小"""
        return self.task_queue.qsize()
    
    def is_empty(self) -> bool:
        """检查队列是否为空"""
        with self.lock:
            return len(self.tasks) == 0 or all(
                task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
                for task in self.tasks.values()
            )
